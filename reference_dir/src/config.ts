// Environment detection
const isDevelopment = import.meta.env.MODE === 'development';
const isProduction = import.meta.env.MODE === 'production';

// API configuration with environment-specific defaults
export const API_BASE_URL = import.meta.env.VITE_API_URL ||
  (isProduction ? 'https://api.ncrpmatrix.in' : 'http://localhost:8000');
export const API_URL = API_BASE_URL;

// Validate API URL in production
if (isProduction && API_BASE_URL.includes('localhost')) {
}

// Authentication configuration
export const TOKEN_STORAGE_KEY = 'auth_token';
export const REFRESH_TOKEN_STORAGE_KEY = 'refresh_token';
export const USER_STORAGE_KEY = 'user_info';

// Application configuration
export const APP_NAME = import.meta.env.VITE_APP_NAME || 'NCRP Matrix';
export const APP_VERSION = import.meta.env.VITE_APP_VERSION || '1.0.0';

// Environment-specific configuration
export const CONFIG = {
  // Debug settings
  DEBUG_LOGS: import.meta.env.VITE_DEBUG_LOGS === 'true' && isDevelopment,
  ENABLE_PERFORMANCE_MONITORING: import.meta.env.VITE_ENABLE_PERFORMANCE_MONITORING === 'true',

  // Security settings
  SESSION_TIMEOUT_MINUTES: parseInt(import.meta.env.VITE_SESSION_TIMEOUT_MINUTES || '60'),

  // Environment info
  ENVIRONMENT: import.meta.env.VITE_ENV || import.meta.env.MODE,
  IS_DEVELOPMENT: isDevelopment,
  IS_PRODUCTION: isProduction,
};

// Feature flags with environment overrides
export const FEATURES = {
  EMAIL_VERIFICATION: import.meta.env.VITE_FEATURE_EMAIL_VERIFICATION !== 'false',
  PASSWORD_RESET: import.meta.env.VITE_FEATURE_PASSWORD_RESET !== 'false',
  TEMPLATE_MANAGEMENT: import.meta.env.VITE_FEATURE_TEMPLATE_MANAGEMENT !== 'false',
};

// Configuration logging removed for security

import { useMutation, useQueryClient } from '@tanstack/react-query';
import { useAlert } from '../context/TailwindAlertContext';
import complaintService from '../services/complaintService';
import authService from '../services/authService';
import { complaintsQueryKeys } from './useComplaintsQuery';
import { debugMutation } from '../utils/debugUtils';
import { uploadComplaint } from '../services/api';

// Interface for CSV update mutation
interface CSVUpdateData {
  csvData: string;
  complaintId: string;
  operationMetadata?: any;
}

// Interface for complaint update mutation
interface ComplaintUpdateData {
  id: string;
  data: any;
}

// Interface for complaint upload mutation
interface ComplaintUploadData {
  formData: FormData;
  onProgress?: (progress: number) => void;
}

/**
 * Hook for updating CSV data with optimistic updates
 */
export const useUpdateCSVData = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useAlert();

  return useMutation({
    mutationFn: async ({ csvData, complaintId, operationMetadata }: CSVUpdateData) => {


      try {
        // Ensure we have a CSRF token
        await authService.fetchCsrfToken();

        // Convert CSV to base64
        const base64Data = btoa(csvData);



        // Use parseCSV for all operations

        const response = await complaintService.parseCSV(
          base64Data,
          complaintId,
          operationMetadata
        );

        return response;
      } catch (error) {
        throw error;
      }
    },
    onMutate: async ({ csvData, complaintId, operationMetadata }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: complaintsQueryKeys.csvData(complaintId) });
      await queryClient.cancelQueries({ queryKey: complaintsQueryKeys.detail(complaintId) });

      // Snapshot the previous value
      const previousCSVData = queryClient.getQueryData(complaintsQueryKeys.csvData(complaintId));
      const previousComplaintData = queryClient.getQueryData(complaintsQueryKeys.detail(complaintId));

      // Optimistically update CSV data
      queryClient.setQueryData(complaintsQueryKeys.csvData(complaintId), {
        csv_data_base64: btoa(csvData)
      });

      // If we have operation metadata, we can be more specific about the update
      if (operationMetadata?.isSingleRowOperation && previousComplaintData) {
        // For single row operations, we don't need to update the full complaint data
        // The CSV data update is sufficient
      }

      // Return a context object with the snapshotted value
      return { previousCSVData, previousComplaintData };
    },
    onError: (err, variables, context) => {
      debugMutation('useUpdateCSVData', variables, undefined, err);

      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousCSVData) {
        queryClient.setQueryData(complaintsQueryKeys.csvData(variables.complaintId), context.previousCSVData);
      }
      if (context?.previousComplaintData) {
        queryClient.setQueryData(complaintsQueryKeys.detail(variables.complaintId), context.previousComplaintData);
      }

      // Show error message
      const errorMessage = err instanceof Error ? err.message : 'Failed to update CSV data';
      showError(errorMessage);
    },
    onSuccess: (data, variables) => {
      debugMutation('useUpdateCSVData', variables, data);

      // Show success message
      const operationType = variables.operationMetadata?.action || 'update';
      showSuccess(`CSV ${operationType} completed successfully`);

      // Invalidate and refetch related queries to ensure data consistency
      queryClient.invalidateQueries({ queryKey: complaintsQueryKeys.csvData(variables.complaintId) });

      // For full CSV updates, also invalidate the complaint detail and graph data
      if (!variables.operationMetadata?.isSingleRowOperation) {
        queryClient.invalidateQueries({ queryKey: complaintsQueryKeys.detail(variables.complaintId) });
        queryClient.invalidateQueries({ queryKey: complaintsQueryKeys.graphData(variables.complaintId) });
      }
    },
    onSettled: (_data, _error, variables) => {
      // Always refetch after error or success to ensure we have the latest data
      queryClient.invalidateQueries({ queryKey: complaintsQueryKeys.csvData(variables.complaintId) });
    },
  });
};

/**
 * Hook for updating complaint data
 */
export const useUpdateComplaint = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useAlert();

  return useMutation({
    mutationFn: async ({ id, data }: ComplaintUpdateData) => {
      return await complaintService.updateComplaint(id, data);
    },
    onMutate: async ({ id }) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: complaintsQueryKeys.detail(id) });

      // Snapshot the previous value
      const previousData = queryClient.getQueryData(complaintsQueryKeys.detail(id));

      // Return a context object with the snapshotted value
      return { previousData };
    },
    onError: (err, variables, context) => {
      // If the mutation fails, use the context returned from onMutate to roll back
      if (context?.previousData) {
        queryClient.setQueryData(complaintsQueryKeys.detail(variables.id), context.previousData);
      }

      // Show error message
      const errorMessage = err instanceof Error ? err.message : 'Failed to update complaint';
      showError(errorMessage);
    },
    onSuccess: (data, variables) => {
      // Show success message
      showSuccess('Complaint updated successfully');

      // Update the cache with the new data
      if (data) {
        queryClient.setQueryData(complaintsQueryKeys.detail(variables.id), data);
      }

      // Invalidate related queries
      queryClient.invalidateQueries({ queryKey: complaintsQueryKeys.lists() });
    },
    onSettled: (_data, _error, variables) => {
      // Always refetch after error or success
      queryClient.invalidateQueries({ queryKey: complaintsQueryKeys.detail(variables.id) });
    },
  });
};

/**
 * Hook for deleting a complaint
 */
export const useDeleteComplaint = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useAlert();

  return useMutation({
    mutationFn: async (id: string) => {
      return await complaintService.deleteComplaint(id);
    },
    onMutate: async (id) => {
      // Cancel any outgoing refetches
      await queryClient.cancelQueries({ queryKey: complaintsQueryKeys.detail(id) });
      await queryClient.cancelQueries({ queryKey: complaintsQueryKeys.lists() });

      // Snapshot the previous value
      const previousData = queryClient.getQueryData(complaintsQueryKeys.detail(id));

      // Optimistically remove from lists
      queryClient.setQueriesData(
        { queryKey: complaintsQueryKeys.lists() },
        (old: any) => {
          if (!old) return old;

          return {
            ...old,
            data: old.data?.filter((complaint: any) => complaint.id !== id) || [],
            total: Math.max(0, (old.total || 1) - 1),
          };
        }
      );

      // Return a context object with the snapshotted value
      return { previousData };
    },
    onError: (err, id, context) => {
      // If the mutation fails, restore the previous data
      if (context?.previousData) {
        queryClient.setQueryData(complaintsQueryKeys.detail(id), context.previousData);
      }

      // Invalidate lists to restore the item
      queryClient.invalidateQueries({ queryKey: complaintsQueryKeys.lists() });

      // Show error message
      const errorMessage = err instanceof Error ? err.message : 'Failed to delete complaint';
      showError(errorMessage);
    },
    onSuccess: (_data, id) => {
      // Show success message
      showSuccess('Complaint deleted successfully');

      // Remove from cache
      queryClient.removeQueries({ queryKey: complaintsQueryKeys.detail(id) });
      queryClient.removeQueries({ queryKey: complaintsQueryKeys.csvData(id) });
      queryClient.removeQueries({ queryKey: complaintsQueryKeys.graphData(id) });

      // Invalidate lists to refetch
      queryClient.invalidateQueries({ queryKey: complaintsQueryKeys.lists() });
    },
  });
};

/**
 * Hook for uploading complaints with proper cache invalidation
 */
export const useUploadComplaint = () => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useAlert();

  return useMutation({
    mutationFn: async ({ formData, onProgress }: ComplaintUploadData) => {
      return await uploadComplaint(formData, { onProgress });
    },
    onSuccess: (data, _variables) => {
      // Show success message
      showSuccess('Complaint uploaded successfully');

      // Clear all complaint-related cache to ensure fresh data
      queryClient.invalidateQueries({ queryKey: complaintsQueryKeys.lists() });

      // If we have a complaint ID, also clear specific complaint cache
      if (data.complaint_id) {
        queryClient.removeQueries({ queryKey: complaintsQueryKeys.detail(data.complaint_id) });
        queryClient.removeQueries({ queryKey: complaintsQueryKeys.csvData(data.complaint_id) });
        queryClient.removeQueries({ queryKey: complaintsQueryKeys.graphData(data.complaint_id) });
      }

      // Clear all cached data to ensure no stale state
      queryClient.clear();
    },
    onError: (err) => {
      // Show error message
      const errorMessage = err instanceof Error ? err.message : 'Failed to upload complaint';
      showError(errorMessage);
    },
  });
};

/**
 * Hook to clear all complaint cache - useful when complaints are uploaded/replaced
 */
export const useClearComplaintCache = () => {
  const queryClient = useQueryClient();

  return () => {
    // Clear all complaint-related queries
    queryClient.invalidateQueries({ queryKey: ['complaints'] });
    queryClient.clear();
  };
};
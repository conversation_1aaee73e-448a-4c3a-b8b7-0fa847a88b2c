import { useState, useCallback } from 'react';
import { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import apiClient from '../services/api';

interface RetryConfig {
  maxRetries?: number;
  retryDelay?: number;
  retryStatusCodes?: number[];
  retryNetworkErrors?: boolean;
  onRetry?: (retryCount: number, error: any) => void;
}

interface ApiState<T> {
  data: T | null;
  loading: boolean;
  error: Error | null;
  retryCount: number;
}

/**
 * Custom hook for making API calls with automatic retry functionality
 *
 * @param defaultConfig Default retry configuration
 * @returns Object with API call function and state
 */
function useApiWithRetry<T = any>(defaultConfig?: RetryConfig) {
  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
    retryCount: 0
  });

  const makeRequest = useCallback(async <R = T>(
    url: string,
    options?: AxiosRequestConfig,
    retryConfig?: RetryConfig
  ): Promise<AxiosResponse<R>> => {
    // Merge default config with provided config
    const config: RetryConfig = {
      maxRetries: 3,
      retryDelay: 1000,
      retryStatusCodes: [408, 429, 500, 502, 503, 504],
      retryNetworkErrors: true,
      ...defaultConfig,
      ...retryConfig
    };

    let retries = 0;
    let lastError: any = null;

    while (retries <= config.maxRetries!) {
      try {
        // Make the API call
        const response = await apiClient({
          url,
          ...options
        });

        // Reset retry count on success
        setState(prev => ({ ...prev, retryCount: 0 }));

        return response;
      } catch (error) {
        lastError = error;
        const axiosError = error as AxiosError;

        // Check if we should retry based on error type
        const shouldRetryStatus = axiosError.response &&
          config.retryStatusCodes!.includes(axiosError.response.status);

        const shouldRetryNetwork = config.retryNetworkErrors! &&
          (axiosError.code === 'ECONNABORTED' ||
           axiosError.message.includes('Network Error') ||
           !axiosError.response);

        if ((shouldRetryStatus || shouldRetryNetwork) && retries < config.maxRetries!) {
          retries++;

          // Call onRetry callback if provided
          if (config.onRetry) {
            config.onRetry(retries, error);
          }

          // Log retry attempt

          // Calculate delay with exponential backoff
          const delay = config.retryDelay! * Math.pow(2, retries - 1);

          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, delay));

          // Update retry count in state
          setState(prev => ({ ...prev, retryCount: retries }));

          // Continue to next retry attempt
          continue;
        }

        // If we shouldn't retry or have exhausted retries, throw the error
        throw error;
      }
    }

    // This should never be reached, but TypeScript requires it
    throw lastError;
  }, [defaultConfig]);

  const callApi = useCallback(async <R = T>(
    url: string,
    options?: AxiosRequestConfig,
    retryConfig?: RetryConfig
  ): Promise<R | null> => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const response = await makeRequest<R>(url, options, retryConfig);
      setState(prev => ({
        ...prev,
        data: response.data as unknown as T,
        loading: false,
        error: null
      }));
      return response.data;
    } catch (error: any) {

      // Extract user-friendly message if available
      const userMessage = error.userMessage ||
        error.response?.data?.detail ||
        error.message ||
        'An unexpected error occurred';

      const enhancedError = new Error(userMessage);
      enhancedError.stack = error.stack;

      setState(prev => ({
        ...prev,
        loading: false,
        error: enhancedError
      }));

      return null;
    }
  }, [makeRequest]);

  return {
    callApi,
    ...state,
    reset: useCallback(() => {
      setState({
        data: null,
        loading: false,
        error: null,
        retryCount: 0
      });
    }, [])
  };
}

export default useApiWithRetry;

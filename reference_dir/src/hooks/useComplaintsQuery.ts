import { useQuery } from '@tanstack/react-query';
import authService from '../services/authService';

// Query keys for consistent caching
export const complaintsQueryKeys = {
  all: ['complaints'] as const,
  lists: () => [...complaintsQueryKeys.all, 'list'] as const,
  list: (page: number, limit: number) => [...complaintsQueryKeys.lists(), { page, limit }] as const,
  details: () => [...complaintsQueryKeys.all, 'detail'] as const,
  detail: (id: string) => [...complaintsQueryKeys.details(), id] as const,
  csvData: (id: string) => [...complaintsQueryKeys.detail(id), 'csv'] as const,
  graphData: (id: string) => [...complaintsQueryKeys.detail(id), 'graph'] as const,
};

// Interface for complaints list response
interface ComplaintsListResponse {
  data: any[];
  total: number;
  totalPages: number;
  page: number;
  limit: number;
}



/**
 * Hook to fetch complaints list with pagination
 */
export const useComplaintsList = (page: number = 1, limit: number = 10) => {
  return useQuery({
    queryKey: complaintsQueryKeys.list(page, limit),
    queryFn: async (): Promise<ComplaintsListResponse> => {
      const response = await authService.api.get('/complaints', {
        params: { page, limit }
      });
      return response.data;
    },
    staleTime: 2 * 60 * 1000, // 2 minutes
    gcTime: 5 * 60 * 1000, // 5 minutes
  });
};

/**
 * Hook to fetch a single complaint by ID
 */
export const useComplaintDetail = (id: string | undefined) => {
  return useQuery({
    queryKey: complaintsQueryKeys.detail(id || ''),
    queryFn: async (): Promise<any> => {
      if (!id) throw new Error('Complaint ID is required');

      const response = await authService.api.get(`/complaints`, {
        params: { complaint_id: id }
      });

      // Handle both paginated and direct response formats
      if (response.data.data && Array.isArray(response.data.data) && response.data.data.length > 0) {
        return response.data.data[0];
      }

      return response.data;
    },
    enabled: !!id, // Only run query if ID is provided
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook to fetch CSV data for a complaint
 */
export const useComplaintCSVData = (id: string | undefined) => {
  return useQuery({
    queryKey: complaintsQueryKeys.csvData(id || ''),
    queryFn: async (): Promise<{ csv_data_base64: string }> => {
      if (!id) throw new Error('Complaint ID is required');

      try {
        // Try the optimized endpoint first
        const response = await authService.api.get(`/complaints/csv-data/${id}`, {
          withCredentials: true,
          headers: {
            'X-CSRF-Token': authService.getCsrfToken(),
            'X-Requested-With': 'XMLHttpRequest'
          },
          params: {
            fields: 'csv_data_base64'
          }
        });

        return response.data;
      } catch (error) {
        // Fallback to full complaint data
        const response = await authService.api.get(`/complaints`, {
          params: { complaint_id: id }
        });

        let complaintData = response.data;

        // Handle paginated response
        if (complaintData.data && Array.isArray(complaintData.data) && complaintData.data.length > 0) {
          complaintData = complaintData.data[0];
        }

        return { csv_data_base64: complaintData.csv_data_base64 || '' };
      }
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

/**
 * Hook to fetch graph data for a complaint
 */
export const useComplaintGraphData = (id: string | undefined) => {
  return useQuery({
    queryKey: complaintsQueryKeys.graphData(id || ''),
    queryFn: async (): Promise<any> => {
      if (!id) throw new Error('Complaint ID is required');

      try {
        // Try the optimized endpoint first
        const response = await authService.api.get(`/complaints/graph-data/${id}`, {
          withCredentials: true,
          headers: {
            'X-CSRF-Token': authService.getCsrfToken(),
            'X-Requested-With': 'XMLHttpRequest'
          },
          params: {
            fields: 'graph_data,metadata'
          }
        });

        return response.data;
      } catch (error) {
        // Fallback to full complaint data
        const response = await authService.api.get(`/complaints`, {
          params: { complaint_id: id }
        });

        let complaintData = response.data;

        // Handle paginated response
        if (complaintData.data && Array.isArray(complaintData.data) && complaintData.data.length > 0) {
          complaintData = complaintData.data[0];
        }

        return {
          graph_data: complaintData.graph_data,
          metadata: complaintData.metadata
        };
      }
    },
    enabled: !!id,
    staleTime: 5 * 60 * 1000, // 5 minutes
    gcTime: 10 * 60 * 1000, // 10 minutes
  });
};

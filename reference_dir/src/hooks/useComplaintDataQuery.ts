import { useComplaintDetail, useComplaintCSVData, useComplaintGraphData } from './useComplaintsQuery';

/**
 * Compatibility hook that provides the same interface as the old useComplaintData hook
 * but uses React Query under the hood for better performance and caching
 */
const useComplaintDataQuery = (id: string, _forceRefresh: boolean = false) => {
  // Use React Query hooks
  const {
    data: complaintData,
    isLoading: complaintLoading,
    error: complaintError,
    refetch: refetchComplaint
  } = useComplaintDetail(id);

  const {
    data: csvDataResponse,
    isLoading: csvLoading,
    error: csvError,
    refetch: refetchCSV
  } = useComplaintCSVData(id);

  const {
    data: graphDataResponse,
    isLoading: graphLoading,
    error: graphError,
    refetch: refetchGraph
  } = useComplaintGraphData(id);

  // Combine the data into a single object like the old hook
  const combinedData = complaintData ? {
    ...complaintData,
    csv_data_base64: csvDataResponse?.csv_data_base64 || complaintData.csv_data_base64,
    graph_data: graphDataResponse?.graph_data || complaintData.graph_data,
    metadata: graphDataResponse?.metadata || complaintData.metadata
  } : null;

  // Combined loading state
  const loading = complaintLoading || csvLoading || graphLoading;

  // Combined error state
  const error = complaintError || csvError || graphError;

  // Combined refetch function
  const refetch = async () => {
    await Promise.all([
      refetchComplaint(),
      refetchCSV(),
      refetchGraph()
    ]);
  };

  return {
    data: combinedData,
    loading,
    error,
    refetch
  };
};

export default useComplaintDataQuery;

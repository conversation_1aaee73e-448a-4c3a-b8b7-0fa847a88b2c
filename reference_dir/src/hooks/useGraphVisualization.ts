import { useState, useEffect } from 'react';
import complaintService from '../services/complaintService';
import { useAlert } from '../context/TailwindAlertContext';

interface GraphData {
  metadata: any;
  transactions: any[];
}

/**
 * Custom hook for handling graph visualization data
 * @param complaintId The ID of the complaint to fetch graph data for
 * @returns Object containing loading state, error state, and graph data
 */
export const useGraphVisualization = (complaintId: string | undefined) => {
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [graphData, setGraphData] = useState<GraphData | null>(null);
  const { showError } = useAlert();

  useEffect(() => {
    let isMounted = true; // Cleanup flag
    const controller = new AbortController(); // Abort controller for cleanup

    const fetchGraphData = async () => {
      if (!complaintId) {
        if (isMounted) {
          setError('No complaint ID provided');
          setLoading(false);
        }
        return;
      }

      try {
        if (isMounted) {
          setLoading(true);
          setError(null);
        }
        // Fetch graph data from API with abort signal
        const response = await complaintService.getComplaintGraphData(complaintId);

        if (!response) {
          throw new Error('No data received from server');
        }

        // Extract metadata and transactions from the response
        const metadata = response.metadata || {};

        // Check if graph_data exists and has the right structure
        if (!response.graph_data) {
          throw new Error('No graph data available for this complaint');
        }

        // Extract transactions from graph_data
        let transactions = [];
        if (response.graph_data.transactions) {
          transactions = response.graph_data.transactions;
        } else if (typeof response.graph_data === 'string') {
          // Try to parse graph_data if it's a string
          try {
            const parsedData = JSON.parse(response.graph_data);
            transactions = parsedData.transactions || [];
          } catch (parseErr) {
            throw new Error('Invalid graph data format');
          }
        }

        if (!metadata) {
          throw new Error('No metadata available');
        }

        // Set graph data even if transactions is empty - we'll show a message in the UI
        if (isMounted) {
          setGraphData({ metadata, transactions });
        }

      } catch (err: any) {
        if (err.name === 'AbortError') {
          return; // Don't update state if aborted
        }

        if (isMounted) {
          setError(err.message || 'Failed to load graph data');
          showError('Failed to load graph data: ' + (err.message || 'Unknown error'));
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchGraphData();

    // Cleanup function
    return () => {
      isMounted = false;
      controller.abort();
    };
  }, [complaintId, showError]);

  return { loading, error, graphData };
};

export default useGraphVisualization;

import { useCallback } from 'react';
import { useAlert } from '../context/TailwindAlertContext';
import { useAuth } from '../context/AuthContext';
import { getUserFriendlyError, shouldDisplayError } from '../utils/errorHandler';


/**
 * Custom hook for smart error handling with user-friendly messages
 * and automatic subscription checking
 */
export const useSmartError = () => {
  const { showSmartError, showError } = useAlert();
  const { checkSubscriptionStatus } = useAuth();

  /**
   * Handle errors with smart processing and subscription checking
   */
  const handleError = useCallback(async (error: any, context?: string) => {
    // Log the error for debugging

    // Check if this is a subscription-related error
    const isSubscriptionError = error?.response?.status === 403 &&
      (error?.response?.data?.detail?.toLowerCase().includes('subscription') ||
       error?.response?.data?.detail?.toLowerCase().includes('expired'));

    if (isSubscriptionError) {
      // Check subscription status and refresh user data if needed
      try {
        await checkSubscriptionStatus();
      } catch (subscriptionError) {
      }
    }

    // Use smart error handling to display user-friendly message
    showSmartError(error, context);
  }, [showSmartError, checkSubscriptionStatus]);

  /**
   * Handle errors with immediate display (bypasses cooldown)
   */
  const handleCriticalError = useCallback((error: any, _context?: string) => {

    const userError = getUserFriendlyError(error);
    showError(userError.message, userError.duration);
  }, [showError]);

  /**
   * Check if an error should be displayed (useful for manual error handling)
   */
  const shouldDisplay = useCallback((error: any): boolean => {
    return shouldDisplayError(error);
  }, []);

  /**
   * Wrapper for async operations with automatic error handling
   */
  const withErrorHandling = useCallback(<T>(
    operation: () => Promise<T>,
    context?: string,
    options?: {
      showLoading?: boolean;
      critical?: boolean;
      onSuccess?: (result: T) => void;
      onError?: (error: any) => void;
    }
  ) => {
    return async (): Promise<T | null> => {
      try {
        const result = await operation();

        if (options?.onSuccess) {
          options.onSuccess(result);
        }

        return result;
      } catch (error) {
        // Call custom error handler if provided
        if (options?.onError) {
          options.onError(error);
        }

        // Handle error based on criticality
        if (options?.critical) {
          handleCriticalError(error, context);
        } else {
          await handleError(error, context);
        }

        return null;
      }
    };
  }, [handleError, handleCriticalError]);

  /**
   * Wrapper for API calls with automatic error handling and retry logic
   */
  const apiCall = useCallback(<T>(
    apiFunction: () => Promise<T>,
    context?: string,
    options?: {
      retries?: number;
      retryDelay?: number;
      critical?: boolean;
      onSuccess?: (result: T) => void;
      onError?: (error: any) => void;
    }
  ) => {
    const maxRetries = options?.retries || 0;
    const retryDelay = options?.retryDelay || 1000;

    const executeWithRetry = async (attempt: number = 0): Promise<T | null> => {
      try {
        const result = await apiFunction();

        if (options?.onSuccess) {
          options.onSuccess(result);
        }

        return result;
      } catch (error) {
        // Check if we should retry
        const userError = getUserFriendlyError(error);
        const shouldRetry = userError.retryable && attempt < maxRetries;

        if (shouldRetry) {

          // Wait before retrying
          await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(2, attempt)));

          return executeWithRetry(attempt + 1);
        }

        // Call custom error handler if provided
        if (options?.onError) {
          options.onError(error);
        }

        // Handle error based on criticality
        if (options?.critical) {
          handleCriticalError(error, context);
        } else {
          await handleError(error, context);
        }

        return null;
      }
    };

    return executeWithRetry();
  }, [handleError, handleCriticalError]);

  return {
    handleError,
    handleCriticalError,
    shouldDisplay,
    withErrorHandling,
    apiCall
  };
};

export default useSmartError;

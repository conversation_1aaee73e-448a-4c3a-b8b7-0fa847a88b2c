import { useState, useCallback, useEffect } from 'react';
import authService from '../services/authService';
import { useAlert } from '../context/TailwindAlertContext';
import { getComplaints } from '../services/api';


interface PaginationState {
  page: number;
  limit: number;
  total: number;
  totalPages: number;
}

interface CacheEntry {
  data: any;
  timestamp: number;
  page: number;
  limit: number;
}

const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
const cache = new Map<string, CacheEntry>();

const useComplaints = (initialPage: number = 1, initialLimit: number = 10) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [data, setData] = useState<any>(null);
  const [pagination, setPagination] = useState<PaginationState>({
    page: initialPage,
    limit: initialLimit,
    total: 0,
    totalPages: 0
  });

  const { showError } = useAlert();

  // Function to generate cache key
  const getCacheKey = (page: number, limit: number) => `complaints_${page}_${limit}`;

  // Function to check if cache is valid
  const isCacheValid = (entry: CacheEntry) => {
    return Date.now() - entry.timestamp < CACHE_DURATION;
  };

  // Function to get data from cache
  const getFromCache = (page: number, limit: number) => {
    const key = getCacheKey(page, limit);
    const entry = cache.get(key);
    if (entry && isCacheValid(entry)) {
      return entry.data;
    }
    return null;
  };

  // Function to set data in cache
  const setInCache = (page: number, limit: number, data: any) => {
    const key = getCacheKey(page, limit);
    cache.set(key, {
      data,
      timestamp: Date.now(),
      page,
      limit
    });
  };

  // Function to clear cache
  const clearCache = () => {
    cache.clear();
  };

  // Function to fetch complaints with caching
  const fetchComplaints = useCallback(async (page: number = pagination.page, limit: number = pagination.limit, forceRefresh: boolean = false) => {
    setLoading(true);
    setError(null);

    try {
      // Check authentication
      const isAuth = await authService.isAuthenticated();
      if (!isAuth) {
        setError('Authentication required. Please log in again.');
        showError('Authentication required. Please log in again.');
        setTimeout(() => {
          window.location.href = '/login';
        }, 2000);
        return;
      }

      // Get CSRF token
      await authService.fetchCsrfToken();

      // Check cache first if not forcing refresh
      if (!forceRefresh) {
        const cachedData = getFromCache(page, limit);
        if (cachedData) {
          setData(cachedData);
          setPagination(prev => ({
            ...prev,
            page,
            limit,
            total: cachedData.total || prev.total,
            totalPages: cachedData.totalPages || prev.totalPages
          }));
          setLoading(false);
          return;
        }
      }

      // Fetch fresh data
      const response = await getComplaints(forceRefresh, page, limit);

      if (response.success && response.data) {
        // Update cache
        setInCache(page, limit, response.data);

        // Update state
        setData(response.data);
        setPagination({
          page,
          limit,
          total: response.data.total || 0,
          totalPages: response.data.totalPages || Math.ceil((response.data.total || 0) / limit)
        });
      } else {
        setError('Failed to fetch complaints');
        showError('Failed to fetch complaints');
      }
    } catch (e) {
      const errorMessage = e instanceof Error ? e.message : 'An error occurred';
      setError(errorMessage);
      showError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, [pagination.page, pagination.limit, showError]);

  // Function to change page
  const changePage = useCallback((newPage: number) => {
    if (newPage !== pagination.page) {
      fetchComplaints(newPage, pagination.limit);
    }
  }, [pagination.page, pagination.limit, fetchComplaints]);

  // Function to change limit
  const changeLimit = useCallback((newLimit: number) => {
    if (newLimit !== pagination.limit) {
      fetchComplaints(1, newLimit); // Reset to first page when changing limit
    }
  }, [pagination.limit, fetchComplaints]);

  // Initial fetch
  useEffect(() => {
    fetchComplaints(initialPage, initialLimit);
  }, []);

  // Clear cache when component unmounts
  useEffect(() => {
    return () => {
      clearCache();
    };
  }, []);

  return {
    loading,
    error,
    data,
    pagination,
    fetchComplaints,
    changePage,
    changeLimit,
    clearCache
  };
};

export default useComplaints;
import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

type ThemeColor =
  // Updated theme options - Matrix theme removed
  | 'darkmode' // Dark cyberpunk theme
  | 'mint'     // Green
  | 'lightblue'
  | 'lightgray'
  | 'orange';  // Orange theme

interface ThemeContextType {
  themeColor: ThemeColor;
  setThemeColor: (color: ThemeColor) => void;
  isDark: boolean;
}

const TailwindThemeContext = createContext<ThemeContextType | undefined>(undefined);

export const useThemeContext = (): ThemeContextType => {
  const context = useContext(TailwindThemeContext);
  if (context === undefined) {
    throw new Error('useThemeContext must be used within a ThemeContextProvider');
  }
  return context;
};

interface ThemeContextProviderProps {
  children: ReactNode;
}

export const TailwindThemeProvider: React.FC<ThemeContextProviderProps> = ({ children }) => {
  // Get the initial theme color from localStorage or default to 'lightblue'
  const [themeColor, setThemeColor] = useState<ThemeColor>(() => {
    if (typeof window !== 'undefined') {
      const savedThemeColor = localStorage.getItem('themeColor') as ThemeColor;
      const validThemes = [
        // Updated theme list - Matrix theme removed
        'darkmode', // Dark cyberpunk theme
        'mint',     // Green
        'lightblue',
        'lightgray',
        'orange'    // Orange theme
      ];

      return savedThemeColor && validThemes.includes(savedThemeColor)
        ? savedThemeColor
        : 'lightblue';
    }
    return 'lightblue';
  });

  // Apply the theme class to the document element
  useEffect(() => {
    const root = window.document.documentElement;

    // Apply theme color classes - Matrix theme removed
    root.classList.remove(
      // Updated theme list
      'theme-darkmode', // Dark cyberpunk theme
      'theme-mint',     // Green
      'theme-lightblue',
      'theme-lightgray',
      'theme-orange'    // Orange theme
    );
    root.classList.add(`theme-${themeColor}`);
    localStorage.setItem('themeColor', themeColor);

    // Update body background color for smooth transitions
    document.body.style.backgroundColor = `var(--theme-bg-primary)`;
    document.body.style.color = `var(--theme-text)`;
  }, [themeColor]);

  // Set a specific theme color
  const handleSetThemeColor = (color: ThemeColor) => {
    setThemeColor(color);
    localStorage.setItem('themeColor', color);
  };

  // Determine if the current theme is dark
  const isDark = [
    // Dark themes include only darkmode (Matrix theme removed)
    'darkmode'
  ].includes(themeColor);

  // Context value
  const contextValue = {
    themeColor,
    setThemeColor: handleSetThemeColor,
    isDark,
  };

  return (
    <TailwindThemeContext.Provider value={contextValue}>
      {children}
    </TailwindThemeContext.Provider>
  );
};

export default TailwindThemeContext;

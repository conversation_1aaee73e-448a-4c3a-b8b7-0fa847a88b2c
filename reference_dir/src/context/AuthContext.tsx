import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import authService from '../services/authService';
import { User } from '../types/auth';
import { useAlert } from './TailwindAlertContext';


interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  loading: boolean;
  postLoginLoading: boolean;
  logout: () => Promise<void>;
  refreshUserData: () => Promise<User | null>;
  refreshToken: () => Promise<void>;
  resolveSessionConflict: (action: 'TAKE_OVER' | 'LOGOUT') => Promise<void>;
  sessionId: string | null;
  setPostLoginLoading: (loading: boolean) => void;
  checkSubscriptionStatus: () => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [postLoginLoading, setPostLoginLoading] = useState(false);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const { showError, showSuccess, showInfo } = useAlert();

  // Check authentication status with retry mechanism and abort signal
  const checkAuthStatus = async (retryCount = 0, signal?: AbortSignal): Promise<boolean> => {
    try {
      const isAuth = await authService.isAuthenticated(retryCount, signal);

      // Check if request was aborted
      if (signal?.aborted) {
        return isAuthenticated; // Return current state if aborted
      }

      if (isAuth === 'network-error') {
        // Network error: do not log out, just show warning and keep user state
        if (retryCount === 0) { // Only show message on first attempt
          showInfo('Unable to verify session due to network issue. You are still logged in.');
        }
        return true;
      }

      setIsAuthenticated(!!isAuth);
      if (!isAuth) {
        setUser(null);
      }
      return !!isAuth;
    } catch (error: any) {
      // Handle aborted requests
      if (error.name === 'AbortError' || signal?.aborted) {
        return isAuthenticated; // Return current state if aborted
      }

      // Retry up to 2 times with exponential backoff (reduced for production)
      if (retryCount < 2) {
        const delay = Math.pow(2, retryCount) * 500; // Reduced delay
        await new Promise(resolve => setTimeout(resolve, delay));
        return checkAuthStatus(retryCount + 1, signal);
      }

      setIsAuthenticated(false);
      setUser(null);
      return false;
    }
  };

  // Refresh user data with enhanced session management
  const refreshUserData = async (): Promise<User | null> => {
    try {
      const userData = await authService.getUserData();

      if (userData) {
        setUser(userData);
        setIsAuthenticated(true);

        // Session ID is now httponly, so we get it from user data
        if (userData.session_id) {
          setSessionId(userData.session_id);
        }
      } else {
        setUser(null);
        setIsAuthenticated(false);
        setSessionId(null);
      }
      return userData;
    } catch (error: any) {
      setUser(null);
      setIsAuthenticated(false);
      setSessionId(null);
      return null;
    }
  };

  // Enhanced token refresh mechanism
  const refreshToken = async (): Promise<void> => {
    try {
      await authService.refreshToken();
      await refreshUserData();
    } catch (error: any) {

      // Check if this is a session conflict error
      if (error.response?.status === 409 && error.response?.data?.code === 'SESSION_CONFLICT') {
        // Handle session conflict
        showError('Another session is active. Please choose to take over or logout.');
        // Don't logout automatically, let the user decide
      } else {
        // If token refresh fails for other reasons, log out the user
        console.log('🚨 LOGOUT TRIGGER: Token refresh failed, calling logout()', error);
        await logout();
      }
    }
  };

  // Enhanced logout function with secure cleanup
  const logout = async () => {





    try {
      await authService.logout();
    } catch (error) {
      // Even if the API call fails, we still want to clear local state
    } finally {
      // Always clear state regardless of API call success
      setUser(null);
      setIsAuthenticated(false);
      setSessionId(null);

      // Clear all storage
      localStorage.clear();
      sessionStorage.clear();

      // Clear non-HttpOnly cookies (HttpOnly cookies are cleared by the backend)
      // This mainly clears CSRF tokens and any other non-HttpOnly cookies
      document.cookie.split(";").forEach(function(c) {
        document.cookie = c.replace(/^ +/, "").replace(/=.*/, "=;expires=" + new Date().toUTCString() + ";path=/");
      });

      // Force reload to clear any remaining state
      window.location.href = '/login';
    }
  };

  // Enhanced session conflict resolution with secure session management
  const resolveSessionConflict = async (action: 'TAKE_OVER' | 'LOGOUT'): Promise<void> => {
    try {
      const response = await authService.api.post('/auth/resolve-session-conflict', { action });

      if (action === 'TAKE_OVER') {
        // Refresh user data after taking over the session
        await refreshUserData();
        showSuccess('Session conflict resolved. You are now logged in on this device.');
      } else {
        // Logout if the user chose to logout - secure cleanup
        setUser(null);
        setIsAuthenticated(false);
        setSessionId(null);

        // Clear only necessary data from browser storage
        localStorage.removeItem('session_id');
        localStorage.removeItem('user');
        localStorage.removeItem('csrf_token');
        sessionStorage.clear();

        showInfo('You have been logged out due to session conflict.');
        window.location.href = '/login';
      }

      return response.data;
    } catch (error) {
      showError('Failed to resolve session conflict. Please try again.');
      throw error;
    }
  };

  // Check subscription status and update user data if needed
  const checkSubscriptionStatus = async (): Promise<boolean> => {
    if (!user) return false;

    try {
      const now = new Date();
      const subscriptionExpires = user.subscription_expires ? new Date(user.subscription_expires) : null;

      // If user has paid status but subscription has expired
      if (user.paid && subscriptionExpires && subscriptionExpires < now) {

        // Refresh user data to get updated subscription status from server
        const updatedUser = await refreshUserData();

        // Return false if subscription is expired
        return updatedUser?.paid || false;
      }

      // Return current paid status
      return user.paid || false;
    } catch (error) {
      return false;
    }
  };

  // Set up session persistence and token refresh
  useEffect(() => {
    // Set up periodic token refresh (every 6 days to stay within 7-day limit)
    const refreshInterval = setInterval(async () => {
      if (isAuthenticated) {
        await refreshToken();
      }
    }, 6 * 24 * 60 * 60 * 1000); // Refresh every 6 days

    // Browser close detection for session cleanup
    const handleBeforeUnload = async () => {
      // Only cleanup if user is authenticated
      if (isAuthenticated) {
        try {
          // Send a beacon request to cleanup session on browser close
          // This is more reliable than fetch for browser close events
          const sessionId = localStorage.getItem('session_id');
          if (sessionId) {
            navigator.sendBeacon('/api/auth/cleanup-session', JSON.stringify({
              session_id: sessionId,
              reason: 'browser_close'
            }));
          }
        } catch (error) {
          // Ignore errors during cleanup
        }
      }
    };

    // Add browser close event listener
    window.addEventListener('beforeunload', handleBeforeUnload);

    // TEMPORARILY DISABLED: Session persistence check to debug logout issue
    // TODO: Re-enable after fixing the page refresh logout problem
    const sessionCheckInterval = null;
    /*
    const sessionCheckInterval = setInterval(async () => {
      if (isAuthenticated) {
        try {
          // Create abort controller for session check
          const sessionCheckController = new AbortController();
          const isAuth = await authService.isAuthenticated(0, sessionCheckController.signal);

          if (isAuth === false) {
            // Only logout if backend explicitly says not authenticated
            console.log('🚨 LOGOUT TRIGGER: Session check failed, backend says not authenticated');
            await logout();
          }
          // For network errors, keep the session active
        } catch (error: any) {
          // Handle aborted requests gracefully
          if (error.name === 'AbortError') {
            console.log('Session check aborted');
            return;
          }
          // Don't logout on network errors during periodic checks
          console.log('Session check failed, but maintaining session:', error.message);
        }
      }
    }, 30 * 60 * 1000); // Check every 30 minutes
    */

    return () => {
      clearInterval(refreshInterval);
      if (sessionCheckInterval) {
        clearInterval(sessionCheckInterval);
      }
      // Remove browser close event listener
      window.removeEventListener('beforeunload', handleBeforeUnload);
    };
  }, [isAuthenticated]);

  // Initial auth check with error handling and abort controller
  useEffect(() => {
    let ran = false;
    let abortController: AbortController | null = null;

    const initAuth = async () => {
      if (ran) {
        return;
      }
      ran = true;

      // Create abort controller for this auth check
      abortController = new AbortController();

      // Note: We don't check for HttpOnly cookies here because they're not accessible to JavaScript
      // This is by design for security. Instead, we rely on the backend to verify authentication.

      try {
        // Pass abort signal to prevent race conditions
        const isAuth = await authService.isAuthenticated(0, abortController.signal);

        // Check if request was aborted
        if (abortController.signal.aborted) {
          return;
        }

        if (isAuth === true) {
          setIsAuthenticated(true);
          // Only fetch user data if auth check succeeded
          await refreshUserData();
        } else if (isAuth === false) {
          // Backend says not authenticated - clear state but don't force logout
          setIsAuthenticated(false);
          setUser(null);
        } else {
          // Network error - keep existing state, show warning
        }
      } catch (error: any) {
        // Handle aborted requests gracefully
        if (error.name === 'AbortError' || abortController?.signal.aborted) {
          return;
        }

        // Don't show error on page refresh - it's likely a race condition
        // Only clear state if it's a real auth failure
        if (error.response?.status === 401 || error.response?.status === 403) {
          setIsAuthenticated(false);
          setUser(null);
        }
      } finally {
        if (!abortController?.signal.aborted) {
          setLoading(false);
        }
      }
    };

    initAuth();

    // Cleanup function to abort ongoing requests
    return () => {
      if (abortController) {
        abortController.abort();
      }
    };
  }, []);

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated,
        loading,
        postLoginLoading,
        logout,
        refreshUserData,
        refreshToken,
        resolveSessionConflict,
        sessionId,
        setPostLoginLoading,
        checkSubscriptionStatus
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

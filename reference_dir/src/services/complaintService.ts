import authService from './authService';
import { apiCache } from './api';
import logger from '../utils/logger';
import { debugApiCall, debugLog } from '../utils/debugUtils';


/**
 * Service for handling complaint-related API calls
 */
const complaintService = {
  /**
   * Fetch all complaints for the current user
   * @returns Promise with the complaints data
   */
  async getComplaints() {
    try {
      logger.debug('Fetching complaints from:', authService.api.defaults.baseURL + '/complaints');

      // Check if user is authenticated by checking for session token
      if (!document.cookie.includes('access_token')) {
        logger.error('User is not authenticated');
        throw new Error('Authentication required');
      }

      // Ensure we have a CSRF token
      await authService.fetchCsrfToken();

      // Use the axios instance with proper cookie handling
      const response = await authService.api.get('/complaints', {
        withCredentials: true, // Ensure cookies are sent
        headers: {
          'X-CSRF-Token': authService.getCsrfToken() // Add CSRF token
        }
      });

      logger.debug('Complaints response successful');
      return response.data;
    } catch (error: any) {
      logger.error('Error fetching complaints:', error);
      // Log more detailed error information
      if ((error as import('axios').AxiosError).response) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        logger.error('Error response data:', (error as import('axios').AxiosError).response?.data);
        logger.error('Error response status:', (error as import('axios').AxiosError).response?.status);
        logger.error('Error response headers:', (error as import('axios').AxiosError).response?.headers);
      } else if ((error as import('axios').AxiosError).request) {
        // The request was made but no response was received
        logger.error('Error request:', (error as import('axios').AxiosError).request);
      } else {
        // Something happened in setting up the request that triggered an Error
        logger.error('Error message:', (error as import('axios').AxiosError).message);
      }

      // Return empty array instead of throwing to prevent UI crashes
      return [];
    }
  },

  /**
   * Fetch a specific complaint by ID
   * @param id Complaint ID or complaint number
   * @returns Promise with the complaint data
   */
  async getComplaintById(id: string) {
    logger.debug('complaintService: getComplaintById called with ID:', id);
    try {
      // Use the cached API method for better performance
      const { getComplaintById: cachedGetComplaintById } = await import('./api');
      const cachedResponse = await cachedGetComplaintById(id);

      if (cachedResponse.success && cachedResponse.data) {
        logger.debug('complaintService: Complaint data fetched successfully via cached API');

        // Process the data to ensure it's in the correct format
        const complaintData = cachedResponse.data;

        // If the data is in the new paginated format, extract the first item
        if (complaintData.data && Array.isArray(complaintData.data) && complaintData.data.length > 0) {
          logger.debug('complaintService: Extracting complaint from paginated response');
          return complaintData.data[0];
        }

        return complaintData;
      }

      // Fallback to direct API call if cached method fails
      logger.debug('complaintService: Falling back to direct API call');

      // Ensure we have a CSRF token
      await authService.fetchCsrfToken();

      // Use the axios instance with proper cookie handling
      const url = `/complaints?complaint_id=${encodeURIComponent(id)}`;

      const response = await authService.api.get(url, {
        withCredentials: true, // Ensure cookies are sent
        headers: {
          'X-CSRF-Token': authService.getCsrfToken() // Add CSRF token
        }
      });

      // If the data is in the new paginated format, extract the first item
      if (response.data && response.data.data &&
          Array.isArray(response.data.data) && response.data.data.length > 0) {
        logger.debug('complaintService: Extracting complaint from paginated response');
        return response.data.data[0];
      }

      return response.data;
    } catch (error: any) {
      logger.error('complaintService: Error fetching complaint data:', error);
      // Return empty object instead of throwing to prevent UI crashes
      return {};
    }
  },

  /**
   * Fetch graph visualization data for a specific complaint
   * @param id Complaint ID or complaint number
   * @returns Promise with the graph data
   */
  async getComplaintGraphData(id: string) {
    logger.debug('complaintService: getComplaintGraphData called with ID:', id);
    try {
      // Use the optimized endpoint that only fetches graph data
      // Ensure we have a CSRF token
      await authService.fetchCsrfToken();

      // Check if we have cached graph data
      const cacheKey = `complaint_graph_${id}`;
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        logger.debug('complaintService: Using cached graph data');
        return cachedData;
      }

      // Use the optimized endpoint to fetch only graph data
      const url = `/complaints/graph-data/${id}`;
      logger.debug('complaintService: Fetching graph data from URL:', url);

      const response = await authService.api.get(url, {
        withCredentials: true,
        headers: {
          'X-CSRF-Token': authService.getCsrfToken(),
          'X-Requested-With': 'XMLHttpRequest'
        },
        params: {
          fields: 'graph_data,metadata' // Only request the fields we need
        }
      });

      logger.debug('complaintService: Graph data fetched successfully');

      // Cache the graph data for future use
      if (response.data) {
        apiCache.set(cacheKey, response.data, 5 * 60 * 1000); // Cache for 5 minutes
      }

      return response.data;
    } catch (error: any) {
      logger.error('complaintService: Error fetching graph data:', error);

      // Fallback to the old method if the optimized endpoint fails
      logger.debug('complaintService: Falling back to full complaint data fetch');
      try {
        // Use the cached API method for better performance and consistency
        const { getComplaintById: cachedGetComplaintById } = await import('./api');
        const cachedResponse = await cachedGetComplaintById(id, true); // true = force refresh

        if (cachedResponse.success && cachedResponse.data) {
          logger.debug('complaintService: Complaint data fetched successfully via cached API');

          // Process the data to ensure it's in the correct format
          const complaintData = cachedResponse.data;

          // If the data is in the new paginated format, extract the first item
          if (complaintData.data && Array.isArray(complaintData.data) && complaintData.data.length > 0) {
            logger.debug('complaintService: Extracting complaint from paginated response');
            return complaintData.data[0];
          }

          return complaintData;
        }
      } catch (fallbackError) {
        logger.error('complaintService: Fallback method also failed:', fallbackError);
      }

      // Return empty object instead of throwing to prevent UI crashes
      return {};
    }
  },

  /**
   * Create a new complaint
   * @param complaintData Complaint data to create
   * @returns Promise with the created complaint
   */
  async createComplaint(data: any) {
    try {
      // Extract metadata from the response - prioritize graph_data.metadata
      let metadata = data.complaintData?.metadata || {};

      // If graph_data has metadata, use that as the primary source
      if (data.graphData && data.graphData.metadata) {
        metadata = data.graphData.metadata;
        logger.debug('Using metadata from graph_data.metadata');
      } else if (data.complaintData?.metadata) {
        metadata = data.complaintData.metadata;
        logger.debug('Using metadata from complaintData.metadata');
      }

      // Format the data to match the Complaint model expected by the backend
      const complaintData = {
        complaint_number: metadata.complaint_number || metadata['Acknowledgement No'] || '',
        complainant_name: metadata.complainant_name || metadata.Name || '',
        amount: parseFloat((metadata.total_amount || metadata['Total Fraudulent Amount'] || '0').toString().replace(/[^0-9.]/g, '')),
        category: metadata.category || metadata['Category of Complaint'] || '',
        subcategory: metadata.subcategory || metadata['Sub-category of Complaint'] || '',
        date: metadata.date || metadata['Date of Complaint'] || '',
        status: 'Pending',
        // Include all transaction data
        transactions: data.complaintData?.transactions || [],
        layer_transactions: data.complaintData?.layer_transactions || {},
        // Convert graph_data to JSON string as expected by the backend
        graph_data: typeof data.graphData === 'object' ? JSON.stringify(data.graphData) : data.graphData,
        // Keep bank_notice_data as an object as expected by the backend
        bank_notice_data: data.complaintData?.bank_notice_data || {},
        notice_docx_base64: data.complaintData?.notice_docx_base64 || '',
        csv_data_base64: data.complaintData?.csv_data_base64 || '',
        // IMPORTANT: Copy metadata to complaint metadata field for dashboard display
        metadata: metadata
      };

      // Clean up the data to ensure it's valid
      // Convert amount to a valid number
      if (isNaN(complaintData.amount)) {
        complaintData.amount = 0;
      }

      // DO NOT convert the date to ISO format - use it as is
      // This preserves the original date format from the HTML
      if (!complaintData.date || complaintData.date === '') {
        // Only use empty string if no date is provided
        complaintData.date = '';
      }
      const response = await authService.api.post('/complaints', complaintData);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  /**
   * Update an existing complaint
   * @param id Complaint ID or complaint number
   * @param complaintData Updated complaint data
   * @returns Promise with the update result
   */
  async updateComplaint(id: string, complaintData: any) {
    try {
      logger.debug(`updateComplaint called for ID: ${id}`);

      // Ensure bank_notice_data is an object, not a string
      if (complaintData.bank_notice_data && typeof complaintData.bank_notice_data === 'string') {
        try {
          complaintData.bank_notice_data = JSON.parse(complaintData.bank_notice_data);
        } catch (e) {
          complaintData.bank_notice_data = {};
        }
      }

      // Ensure we have a CSRF token
      await authService.fetchCsrfToken();

      // Log the CSRF token for debugging
      logger.debug('Updating complaint with CSRF token:', authService.getCsrfToken());

      // Create a direct axios instance for this specific request
      const axios = (await import('axios')).default;
      const directAxios = axios.create({
        baseURL: authService.api.defaults.baseURL,
        withCredentials: true // Important for cookies
      });

      // Make the request with explicit headers
      logger.debug(`Sending update request to server for complaint ID: ${id}`);
      const response = await directAxios.post('/complaints/action', {
        action: 'update',
        complaint_id: id, // Use complaint_id parameter
        update_data: complaintData
      }, {
        timeout: 60000, // Increased timeout for large data
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': authService.getCsrfToken() || '',
          'X-Requested-With': 'XMLHttpRequest'
        },
        withCredentials: true // Ensure cookies are sent
      });

      logger.debug(`Update successful for complaint ID: ${id}`);

      // Clear ALL caches to ensure fresh data after update
      logger.debug('Clearing all caches after complaint update');
      apiCache.remove(`complaint_${id}`);
      apiCache.clear('complaints_list');
      apiCache.clear('complaints');

      return response.data;
    } catch (error: any) {
      logger.error(`Error updating complaint ${id}:`, error);

      // Try to clear caches even on error to ensure we don't have stale data
      try {
        apiCache.remove(`complaint_${id}`);
        apiCache.clear('complaints_list');
        apiCache.clear('complaints');
      } catch (cacheError) {
        logger.error('Error clearing caches:', cacheError);
      }

      throw error;
    }
  },

  /**
   * Delete a complaint
   * @param id Complaint ID or complaint number
   * @returns Promise with the deletion result
   */
  async deleteComplaint(id: string) {
    try {
      // First, clear all caches to ensure we don't have stale data
      // This is critical to prevent stale data from appearing in the UI
      logger.debug(`Clearing cache before deleting complaint: ${id}`);

      // Clear specific complaint cache
      apiCache.remove(`complaint_${id}`);

      // Clear all complaints list caches with any pagination parameters
      apiCache.clear('complaints_list');

      // Also clear the general complaints cache
      apiCache.clear('complaints');

      // Ensure we have a CSRF token
      await authService.fetchCsrfToken();

      // Use the action endpoint directly since it's more reliable
      const response = await authService.api.post('/complaints/action', {
        action: 'delete',
        complaint_id: id // Use complaint_id instead of complaint_number
      }, {
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': authService.getCsrfToken() || '',
          'X-Requested-With': 'XMLHttpRequest'
        },
        withCredentials: true // Ensure cookies are sent
      });

      // Clear caches again after successful deletion
      logger.debug(`Clearing cache after deleting complaint: ${id}`);
      apiCache.remove(`complaint_${id}`);
      apiCache.clear('complaints_list');
      apiCache.clear('complaints');

      return response.data;
    } catch (error) {
      // Even if deletion fails, try to refresh the cache
      // This ensures we don't show deleted complaints that might still be in the cache
      logger.debug(`Clearing cache after failed deletion attempt: ${id}`);
      apiCache.remove(`complaint_${id}`);
      apiCache.clear('complaints_list');
      apiCache.clear('complaints');
      throw error;
    }
  },

  /**
   * Process a complaint file for extraction
   * @param formData Form data with the file and parameters
   * @param onUploadProgress Progress callback
   * @returns Promise with the extraction result
   */
  async processComplaintFile(formData: FormData, onUploadProgress?: (progressEvent: any) => void) {
    try {
      // Ensure we have a CSRF token before making the request
      await authService.fetchCsrfToken();

      const csrfToken = authService.getCsrfToken();


      // Create a direct axios instance for this specific request
      const axios = (await import('axios')).default;
      const directAxios = axios.create({
        baseURL: authService.api.defaults.baseURL,
        withCredentials: true // Important for cookies
      });

      // Make the request with explicit headers
      const response = await directAxios.post('/extraction/extract-complaint', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
          'X-CSRF-Token': csrfToken || '',
          'X-Requested-With': 'XMLHttpRequest'
        },
        onUploadProgress,
        withCredentials: true, // Ensure cookies are sent
        timeout: 120000 // Increase timeout to 2 minutes for large files
      });

      return response;
    } catch (error) {

      throw error;
    }
  },

  /**
   * Download Excel file for a complaint
   * @param id Complaint ID or complaint number
   * @returns Promise with the Excel file
   */
  async downloadCSV(id: string) {
    try {
      // We're using HttpOnly cookies now, so we don't need to manually get the token
      // Just ensure we have a CSRF token
      await authService.fetchCsrfToken();

      // Use fetch with proper headers and credentials
      const response = await fetch(`${authService.api.defaults.baseURL}/extraction/download-csv/${id}?format=excel`, {
        method: 'GET',
        headers: {
          'X-CSRF-Token': authService.getCsrfToken() || '',
          'Accept': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        },
        credentials: 'include' // Include cookies
      });

      if (!response.ok) {
        throw new Error(`Error downloading file: ${response.status} ${response.statusText}`);
      }

      // Get the blob from the response
      const blob = await response.blob();

      // Create a URL for the blob
      const url = window.URL.createObjectURL(blob);

      // Create a temporary anchor element to trigger the download
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `Complaint_${id}.xlsx`);
      document.body.appendChild(link);
      link.click();

      // Clean up
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);

      return { success: true };
    } catch (error) {
      throw error;
    }
  },

  /**
   * Fetch only CSV data for a complaint (optimized)
   * @param id Complaint ID or complaint number
   * @returns Promise with just the CSV data
   */
  async getComplaintCSVData(id: string) {
    logger.debug('complaintService: getComplaintCSVData called with ID:', id);
    try {
      // Check if we have cached CSV data
      const cacheKey = `complaint_csv_${id}`;
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        logger.debug('complaintService: Using cached CSV data');
        return cachedData;
      }

      // Ensure we have a CSRF token
      await authService.fetchCsrfToken();

      // Use the optimized endpoint to fetch only CSV data
      const url = `/complaints/csv-data/${id}`;
      logger.debug('complaintService: Fetching CSV data from URL:', url);

      const response = await authService.api.get(url, {
        withCredentials: true,
        headers: {
          'X-CSRF-Token': authService.getCsrfToken(),
          'X-Requested-With': 'XMLHttpRequest'
        },
        params: {
          fields: 'csv_data_base64' // Only request the CSV data
        }
      });

      logger.debug('complaintService: CSV data fetched successfully');

      // Cache the CSV data for future use
      if (response.data) {
        apiCache.set(cacheKey, response.data, 5 * 60 * 1000); // Cache for 5 minutes
      }

      return response.data;
    } catch (error: any) {
      logger.error('complaintService: Error fetching CSV data:', error);

      // Fallback to the old method if the optimized endpoint fails
      logger.debug('complaintService: Falling back to full complaint data fetch for CSV');
      try {
        // Use the cached API method for better performance and consistency
        const { getComplaintById: cachedGetComplaintById } = await import('./api');
        const cachedResponse = await cachedGetComplaintById(id, true); // true = force refresh

        if (cachedResponse.success && cachedResponse.data) {
          logger.debug('complaintService: Complaint data fetched successfully via cached API');

          // Extract only the CSV data
          const complaintData = cachedResponse.data;

          // If the data is in the new paginated format, extract the first item
          if (complaintData.data && Array.isArray(complaintData.data) && complaintData.data.length > 0) {
            return { csv_data_base64: complaintData.data[0].csv_data_base64 || '' };
          }

          return { csv_data_base64: complaintData.csv_data_base64 || '' };
        }
      } catch (fallbackError) {
        logger.error('complaintService: Fallback method also failed:', fallbackError);
      }

      // Return empty object instead of throwing to prevent UI crashes
      return { csv_data_base64: '' };
    }
  },

  /**
   * Get complaint summary information
   * @param id Complaint ID or complaint number
   * @returns Promise with the summary data
   */
  async getComplaintSummary(id: string) {
    logger.debug('complaintService: getComplaintSummary called with ID:', id);
    try {
      // Check if we have cached summary data
      const cacheKey = `complaint_summary_${id}`;
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        logger.debug('complaintService: Using cached summary data');
        return cachedData;
      }

      // Ensure we have a CSRF token
      await authService.fetchCsrfToken();

      // Use the extraction endpoint to get summary data
      const response = await authService.api.get(`/extraction/complaint-summary/${id}`, {
        withCredentials: true,
        headers: {
          'X-CSRF-Token': authService.getCsrfToken()
        }
      });

      logger.debug('complaintService: Summary data fetched successfully');

      // Cache the summary data for future use
      if (response.data) {
        apiCache.set(cacheKey, response.data, 5 * 60 * 1000); // Cache for 5 minutes
      }

      return response.data;
    } catch (error: any) {
      logger.error('complaintService: Error fetching summary data:', error);
      if (error.response) {
        logger.error('Error response data:', error.response.data);
        logger.error('Error response status:', error.response.status);
      } else if (error.request) {
        logger.error('Error request:', error.request);
      } else {
        logger.error('Error message:', error.message);
      }

      throw error;
    }
  },

  /**
   * Fetch only notice data for a complaint (optimized)
   * @param id Complaint ID or complaint number
   * @returns Promise with just the notice data
   */
  async getComplaintNoticeData(id: string) {
    logger.debug('complaintService: getComplaintNoticeData called with ID:', id);
    try {
      // Check if we have cached notice data
      const cacheKey = `complaint_notice_${id}`;
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        logger.debug('complaintService: Using cached notice data');
        return cachedData;
      }

      // Ensure we have a CSRF token
      await authService.fetchCsrfToken();

      // Use the optimized endpoint to fetch only notice data
      const url = `/complaints/notice-data/${id}`;
      logger.debug('complaintService: Fetching notice data from URL:', url);

      const response = await authService.api.get(url, {
        withCredentials: true,
        headers: {
          'X-CSRF-Token': authService.getCsrfToken(),
          'X-Requested-With': 'XMLHttpRequest'
        },
        params: {
          fields: 'bank_notice_data,metadata' // Only request the notice data and metadata
        }
      });

      logger.debug('complaintService: Notice data fetched successfully');

      // Cache the notice data for future use
      if (response.data) {
        apiCache.set(cacheKey, response.data, 5 * 60 * 1000); // Cache for 5 minutes
      }

      return response.data;
    } catch (error: any) {
      logger.error('complaintService: Error fetching notice data:', error);

      // Fallback to the old method if the optimized endpoint fails
      logger.debug('complaintService: Falling back to full complaint data fetch for notice data');
      try {
        // Use the cached API method for better performance and consistency
        const { getComplaintById: cachedGetComplaintById } = await import('./api');
        const cachedResponse = await cachedGetComplaintById(id, true); // true = force refresh

        if (cachedResponse.success && cachedResponse.data) {
          logger.debug('complaintService: Complaint data fetched successfully via cached API');

          // Extract only the notice data and metadata
          const complaintData = cachedResponse.data;

          // If the data is in the new paginated format, extract the first item
          if (complaintData.data && Array.isArray(complaintData.data) && complaintData.data.length > 0) {
            const data = complaintData.data[0];
            return {
              bank_notice_data: data.bank_notice_data || {},
              metadata: data.metadata || {}
            };
          }

          return {
            bank_notice_data: complaintData.bank_notice_data || {},
            metadata: complaintData.metadata || {}
          };
        }
      } catch (fallbackError) {
        logger.error('complaintService: Fallback method also failed:', fallbackError);
      }

      // Return empty object instead of throwing to prevent UI crashes
      return { bank_notice_data: {}, metadata: {} };
    }
  },

  /**
   * Update complaint notes
   * @param id Complaint ID or complaint number
   * @param notes Notes text
   * @returns Promise with the update result
   */
  async updateComplaintNotes(id: string, notes: string) {
    logger.debug('complaintService: updateComplaintNotes called with ID:', id);
    try {
      // Ensure we have a CSRF token
      await authService.fetchCsrfToken();

      // Use the action endpoint to update notes
      const response = await authService.api.post('/complaints/action', {
        action: 'update',
        complaint_id: id,
        update_data: { notes }
      }, {
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': authService.getCsrfToken()
        }
      });

      logger.debug('complaintService: Notes updated successfully');
      return response.data;
    } catch (error: any) {
      logger.error('complaintService: Error updating notes:', error);
      throw error;
    }
  },

  /**
   * Parse CSV data and generate graph
   * @param csvData Base64 encoded CSV data
   * @param complaintId Optional complaint ID to update
   * @param metadata Optional metadata to preserve
   * @returns Promise with the parsed data and graph
   */
  async parseCSV(csvData: string, complaintId?: string, metadata?: any) {
    try {
      debugLog('PARSE_CSV', 'Starting parseCSV', `complaintId: ${complaintId}, csvDataLength: ${csvData.length}, hasMetadata: ${!!metadata}`);

      // Validate inputs
      if (!csvData || csvData.trim() === '') {
        throw new Error('CSV data is empty');
      }

      if (!complaintId) {
        throw new Error('Complaint ID is required');
      }

      logger.debug(`parseCSV called for complaint ID: ${complaintId}`);

      // Log metadata if provided
      if (metadata) {
        logger.debug(`Using provided metadata with keys: ${Object.keys(metadata).join(', ')}`);
      } else {
        logger.debug('No metadata provided to parseCSV');
      }

      // Ensure the CSV data has all required fields
      const requiredHeaders = ['Layer', 'Fraud Type', 'Sender Account', 'Sender Txn ID', 'Sender Bank', 'Receiver Account',
                             'Txn ID', 'Receiver Bank', 'Txn Type', 'Txn Date', 'Amount', 'Reference'];

      // Check if the CSV data is base64 encoded
      let decodedCsvData = csvData;
      try {
        // Try to decode the CSV data if it's base64 encoded
        if (csvData.match(/^[A-Za-z0-9+/=]+$/)) {
          decodedCsvData = atob(csvData);
        }
      } catch (e) {
        // If decoding fails, use the original data

      }

      // Parse the CSV data
      const lines = decodedCsvData.split('\n');
      if (lines.length > 0) {
        const headerLine = lines[0];
        const headerFields = headerLine.split(',').map(h => h.trim());

        // Check if all required fields are present
        const missingHeaders = requiredHeaders.filter(header => !headerFields.includes(header));
        if (missingHeaders.length > 0) {
          logger.warn('Missing required headers in CSV data:', missingHeaders);

          // Add the missing headers to the CSV
          const newHeaderLine = [...headerFields, ...missingHeaders].join(',');
          lines[0] = newHeaderLine;

          // Add default values for the missing fields in each data row
          for (let i = 1; i < lines.length; i++) {
            if (lines[i].trim() !== '') {
              // Add default values for each missing header
              const defaultValues = missingHeaders.map(header => {
                if (header === 'Txn Type') return 'Unknown';
                if (header === 'Txn Date') return ''; // Don't use current date as fallback
                if (header === 'Amount') return '0';
                if (header === 'Fraud Type') return 'banking_upi';
                return '';
              });
              lines[i] += ',' + defaultValues.join(',');
            }
          }

          // Regenerate the CSV content
          decodedCsvData = lines.join('\n');
          logger.debug('Fixed CSV data with missing headers');

          // Re-encode the CSV data if it was originally base64 encoded
          if (csvData.match(/^[A-Za-z0-9+/=]+$/)) {
            csvData = btoa(decodedCsvData);
          } else {
            csvData = decodedCsvData;
          }
        }
      }

      // Use a debounced approach to prevent multiple rapid updates
      // This helps prevent the UI from refreshing too frequently

      // Create a unique request ID for this update
      const requestId = `csv_update_${Date.now()}`;

      // Store the current request ID to track the latest request
      (window as any).__latestCSVUpdateRequest = requestId;

      // First, try to get the existing complaint data to ensure we have all metadata
      let existingMetadata = metadata;

      if (!existingMetadata) {
        try {
          logger.debug(`Fetching existing complaint data to get metadata for ID: ${complaintId}`);
          const existingComplaint = await this.getComplaintById(complaintId);

          if (existingComplaint) {
            // Try different sources of metadata in order of preference
            existingMetadata = existingComplaint.metadata ||
                              (existingComplaint.graph_data && existingComplaint.graph_data.metadata) ||
                              existingComplaint.summary;

            if (existingMetadata) {
              logger.debug(`Found existing metadata with keys: ${Object.keys(existingMetadata).join(', ')}`);
            } else {
              logger.debug('No existing metadata found in complaint');
            }
          }
        } catch (metadataError) {
          logger.warn('Error fetching existing metadata:', metadataError);
          // Continue without existing metadata
        }
      }

      // Ensure we have a CSRF token
      await authService.fetchCsrfToken();

      // Check if this is still the latest request
      if ((window as any).__latestCSVUpdateRequest !== requestId) {
        logger.debug('Skipping outdated CSV update API call');
        return null; // Skip processing outdated requests
      }

      // Make the API request with proper headers and timeout
      logger.debug(`Making direct API call to update CSV for complaint ID: ${complaintId}`);

      // Prepare request data with metadata if provided
      const requestData: any = {
        action: 'update_csv',
        complaint_id: complaintId,
        csv_data: csvData
      };

      // Include metadata if available (either provided or fetched)
      if (existingMetadata) {
        requestData.metadata = existingMetadata;
        logger.debug(`Including metadata in update_csv request with keys: ${Object.keys(existingMetadata).join(', ')}`);
      }

      debugLog('PARSE_CSV', 'Making API call to /complaints/action', requestData);

      const response = await authService.api.post('/complaints/action', requestData, {
        // Increase timeout to handle larger CSV data
        timeout: 60000, // Increased to 60 seconds
        headers: {
          'Content-Type': 'application/json',
          'X-CSRF-Token': authService.getCsrfToken()
        }
      });

      debugApiCall('POST', '/complaints/action', requestData, response.data);

      if (!response.data) {
        throw new Error('Empty response from server');
      }

      logger.debug(`Direct API call successful for complaint ID: ${complaintId}`);

      // Ensure bank_notice_data is an object, not a string
      if (response.data && response.data.bank_notice_data && typeof response.data.bank_notice_data === 'string') {
        try {
          response.data.bank_notice_data = JSON.parse(response.data.bank_notice_data);
        } catch (e) {
          response.data.bank_notice_data = {};
        }
      }

      // Ensure graph_data.metadata is preserved
      if (response.data && response.data.graph_data) {
        if (!response.data.graph_data.metadata && existingMetadata) {
          // If response doesn't have metadata but we have existing metadata, add it
          response.data.graph_data.metadata = { ...existingMetadata };
          logger.debug('Added existing metadata to graph_data in response');
        } else if (response.data.graph_data.metadata && existingMetadata) {
          // If both have metadata, merge them with priority to existing metadata for important fields
          const mergedMetadata = { ...response.data.graph_data.metadata };

          // Preserve important fields from existing metadata
          const fieldsToPreserve = [
            'complaint_number', 'complainant_name', 'date', 'total_amount',
            'category', 'subcategory', 'complainant_mobile', 'complainant_email'
          ];

          fieldsToPreserve.forEach(field => {
            if (existingMetadata[field] !== undefined) {
              mergedMetadata[field] = existingMetadata[field];
            }
          });

          response.data.graph_data.metadata = mergedMetadata;
          logger.debug('Merged existing metadata with response metadata in graph_data');
        }
      }

      // Also ensure metadata is available at the top level
      if (response.data && !response.data.metadata && existingMetadata) {
        response.data.metadata = { ...existingMetadata };
        logger.debug('Added existing metadata to top level of response');
      }

      // Clear ALL caches to ensure fresh data
      logger.debug('Clearing all caches after direct API call');
      apiCache.remove(`complaint_${complaintId}`);
      apiCache.clear('complaints_list');
      apiCache.clear('complaints');

      return response.data;
    } catch (error: any) {
      debugApiCall('POST', '/complaints/action', undefined, undefined, error);
      debugLog('PARSE_CSV', 'parseCSV failed with error', error);
      logger.error(`Error in parseCSV for complaint ID ${complaintId}:`, error);

      // Try to clear caches even on error to ensure we don't have stale data
      try {
        apiCache.remove(`complaint_${complaintId}`);
        apiCache.clear('complaints_list');
        apiCache.clear('complaints');
      } catch (cacheError) {
        logger.error('Error clearing caches:', cacheError);
      }

      throw error;
    }
  },

  /**
   * Handle table row operations (update or delete)
   * @param complaintId Complaint ID
   * @param operation Operation type ('update' or 'delete')
   * @param rowId Row ID
   * @param csvData Base64 encoded CSV data
   * @param metadata Optional metadata to preserve
   * @returns Promise with the operation result
   */
  async handleTableRowOperation(
    complaintId: string,
    operation: 'update' | 'delete',
    rowId: string,
    csvData: string,
    metadata?: any
  ) {
    const maxRetries = 3;
    let retryCount = 0;

    while (retryCount < maxRetries) {
      try {
        // Validate inputs
        if (!complaintId) {
          throw new Error('Complaint ID is required');
        }

        if (!operation || (operation !== 'update' && operation !== 'delete')) {
          throw new Error('Valid operation (update or delete) is required');
        }

        if (!rowId) {
          throw new Error('Row ID is required');
        }

        if (!csvData || csvData.trim() === '') {
          throw new Error('CSV data is empty');
        }

        logger.debug(`handleTableRowOperation called for complaint ID: ${complaintId}, operation: ${operation}, row: ${rowId}`);

        // Log metadata if provided
        if (metadata) {
          logger.debug(`Using provided metadata with keys: ${Object.keys(metadata).join(', ')}`);
        } else {
          logger.debug('No metadata provided to handleTableRowOperation');
        }

        // Ensure we have a CSRF token
        await authService.fetchCsrfToken();

        // Prepare request data
        const requestData = {
          action: 'update_csv',
          complaint_id: complaintId,
          csv_data: csvData,
          metadata: metadata,
          // Include row operation details in metadata
          row_operation: {
            operation: operation,
            row_id: rowId
          }
        };

        // Make the API request to the complaints/action endpoint
        logger.debug(`Making API call to update CSV with row operation for complaint ID: ${complaintId}`);
        const response = await authService.api.post('/complaints/action', requestData, {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': authService.getCsrfToken()
          }
        });

        // Clear all cached data for this complaint to ensure fresh data
        apiCache.remove(`complaint_${complaintId}`);
        apiCache.clear('complaints_list');
        apiCache.clear('complaints');

        // Log the successful operation
        logger.debug(`Table row operation successful for complaint ID: ${complaintId}, operation: ${operation}, row: ${rowId}`);

        return response.data;
      } catch (error: any) {
        retryCount++;
        logger.error(`Error in handleTableRowOperation (attempt ${retryCount}/${maxRetries}):`, error);

        // If this is the last retry, throw the error
        if (retryCount === maxRetries) {
          throw error;
        }

        // If we get a 500 error, wait before retrying
        if (error.response?.status === 500) {
          const waitTime = Math.min(1000 * Math.pow(2, retryCount), 5000); // Exponential backoff with max 5s
          logger.debug(`Retrying in ${waitTime}ms...`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
          continue;
        }

        // For other errors, throw immediately
        throw error;
      }
    }
  },



  // The generateGraph and storeComplaintMetadata functions are not needed
  // as the extract-complaint endpoint already returns graph data
  // and we can use createComplaint to store the data

  /**
   * Analyze multiple complaints to find common accounts with detailed transaction data
   * @param complaintIds Array of complaint IDs to analyze
   * @returns Promise with the analysis results including detailed transaction data
   */
  async analyzeCommonAccounts(complaintIds: string[]) {
    try {
      // Validate input
      if (!complaintIds || !Array.isArray(complaintIds) || complaintIds.length < 2) {
        throw new Error('At least two complaint IDs are required for analysis');
      }

      if (complaintIds.length > 10) {
        throw new Error('Maximum 10 complaints can be analyzed at once');
      }

      logger.debug(`Analyzing common accounts across ${complaintIds.length} complaints`);

      // Ensure we have a CSRF token
      await authService.fetchCsrfToken();

      // Make the API request
      const response = await authService.api.post('/analysis/common-accounts',
        { complaint_ids: complaintIds },
        {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': authService.getCsrfToken()
          },
          timeout: 120000 // 120 seconds timeout for potentially large data with detailed transactions
        }
      );

      if (!response.data || !response.data.success) {
        throw new Error('Failed to analyze complaints');
      }

      logger.debug('Common accounts analysis completed successfully');
      return response.data.data;
    } catch (error: any) {
      logger.error('Error analyzing common accounts:', error);

      // Log more detailed error information
      if (error.response) {
        logger.error('Error response data:', error.response.data);
        logger.error('Error response status:', error.response.status);
      } else if (error.request) {
        logger.error('Error request:', error.request);
      } else {
        logger.error('Error message:', error.message);
      }

      throw error;
    }
  },

  /**
   * Optimized version: Analyze multiple complaints to find common accounts with only transaction data
   * This method requests only the necessary transaction data for analysis, reducing network traffic and improving performance
   *
   * @param complaintIds Array of complaint IDs to analyze
   * @returns Promise with the analysis results including only transaction data
   */
  async analyzeCommonAccountsOptimized(complaintIds: string[]) {
    try {
      // Validate input
      if (!complaintIds || !Array.isArray(complaintIds) || complaintIds.length < 2) {
        throw new Error('At least two complaint IDs are required for analysis');
      }

      if (complaintIds.length > 10) {
        throw new Error('Maximum 10 complaints can be analyzed at once');
      }

      logger.debug(`Analyzing common accounts (optimized) across ${complaintIds.length} complaints`);

      // Ensure we have a CSRF token
      await authService.fetchCsrfToken();

      // Make the API request with a query parameter to indicate we only want transaction data
      const response = await authService.api.post('/analysis/common-accounts',
        {
          complaint_ids: complaintIds,
          optimized: true,  // This flag indicates we only want transaction data
          fields_needed: [
            "complaint_number",
            "transactions.sender_account",
            "transactions.sender_bank",
            "transactions.receiver_account",
            "transactions.receiver_bank",
            "transactions.amount",
            "transactions.date",
            "transactions.txn_id",
            "transactions.receiver_info",
            "transactions.txn_type"
          ]
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'X-CSRF-Token': authService.getCsrfToken()
          },
          timeout: 60000 // Reduced timeout since we're fetching less data
        }
      );

      if (!response.data || !response.data.success) {
        throw new Error('Failed to analyze complaints');
      }

      logger.debug('Optimized common accounts analysis completed successfully');

      // Log the size of the response data
      const dataSize = JSON.stringify(response.data.data).length;
      logger.debug(`Response data size: ${(dataSize / 1024).toFixed(2)} KB`);

      return response.data.data;
    } catch (error: any) {
      logger.error('Error analyzing common accounts (optimized):', error);

      // Log more detailed error information
      if (error.response) {
        logger.error('Error response data:', error.response.data);
        logger.error('Error response status:', error.response.status);
      } else if (error.request) {
        logger.error('Error request:', error.request);
      } else {
        logger.error('Error message:', error.message);
      }

      // Fall back to the non-optimized version if the optimized endpoint fails
      logger.debug('Falling back to non-optimized analysis method');
      return this.analyzeCommonAccounts(complaintIds);
    }
  }
};

export default complaintService;

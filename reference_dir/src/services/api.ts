import axios, { AxiosError } from 'axios';
import authService from './authService';

// Use environment variable or default to localhost
export const API_BASE_URL = import.meta.env.VITE_API_URL;

// Error response interface
interface ErrorResponse {
  detail: string;
  status: number;
  message?: string;
}

// Enhanced in-memory cache for API responses with security improvements
// This implementation uses only in-memory storage and doesn't persist sensitive data
interface CacheItem {
  data: any;
  timestamp: number;
  expiresIn: number; // milliseconds
  lastAccessed: number; // timestamp of last access for LRU
  size: number; // approximate size in bytes
  sensitivity: 'public' | 'private'; // Indicates if data contains sensitive information
}

// List of cache keys that should be considered sensitive (contains PII or other sensitive data)
const SENSITIVE_DATA_PATTERNS = [
  'complaint_', // All complaint data
  'user_', // User data
  'auth_', // Authentication data
  'profile_', // Profile data
  'graph_', // Graph data (contains transaction details)
  'csv_', // CSV data (contains transaction details)
  'notice_', // Notice data
  'summary_' // Summary data
];

class ApiCache {
  private cache: Record<string, CacheItem> = {};
  private maxCacheSize: number = 5 * 1024 * 1024; // Reduced to 5MB max cache size
  private currentSize: number = 0;
  private maxItems: number = 50; // Reduced maximum number of items to cache
  private cleanupInterval: number | null = null;

  constructor() {
    // Set up more frequent cleanup every 2 minutes
    this.cleanupInterval = window.setInterval(() => this.cleanup(), 2 * 60 * 1000);

    // Clean up on page unload
    window.addEventListener('beforeunload', () => {
      if (this.cleanupInterval !== null) {
        clearInterval(this.cleanupInterval);
      }
      // Clear all sensitive data on page unload
      this.clearSensitiveData();
    });
  }

  // Determine if a key contains sensitive data
  private isSensitiveData(key: string): boolean {
    return SENSITIVE_DATA_PATTERNS.some(pattern => key.includes(pattern));
  }

  // Clear all sensitive data from cache
  clearSensitiveData(): void {
    const keysToRemove: string[] = [];

    // Identify sensitive data
    Object.keys(this.cache).forEach(key => {
      if (this.isSensitiveData(key) || this.cache[key].sensitivity === 'private') {
        keysToRemove.push(key);
      }
    });

    // Remove sensitive data
    keysToRemove.forEach(key => {
      this.remove(key);
    });

  }

  // Estimate size of data in bytes
  private estimateSize(data: any): number {
    try {
      const jsonString = JSON.stringify(data);
      // UTF-16 characters use 2 bytes per character
      return jsonString.length * 2;
    } catch (e) {
      // If we can't stringify, use a conservative estimate
      return 10000; // 10KB default
    }
  }

  // Set a cache item with expiration
  set(key: string, data: any, expiresIn: number = 5 * 60 * 1000, options: { sensitivity?: 'public' | 'private' } = {}): void {
    // Determine sensitivity based on key pattern or explicit option
    const sensitivity = options.sensitivity || (this.isSensitiveData(key) ? 'private' : 'public');

    // Don't cache sensitive data for longer than 2 minutes
    if (sensitivity === 'private' && expiresIn > 2 * 60 * 1000) {
      expiresIn = 2 * 60 * 1000; // 2 minutes max for sensitive data
    }

    // Calculate size of data
    const size = this.estimateSize(data);

    // If this single item is too large (>20% of max), don't cache it
    if (size > this.maxCacheSize * 0.2) {
      return;
    }

    // If we already have this item, subtract its size first
    if (this.cache[key]) {
      this.currentSize -= this.cache[key].size;
    }

    // Make room if needed
    this.ensureSpace(size);

    // Add to cache
    this.cache[key] = {
      data,
      timestamp: Date.now(),
      expiresIn,
      lastAccessed: Date.now(),
      size,
      sensitivity
    };

    this.currentSize += size;

    // Log sensitive data caching (for security auditing)
    if (sensitivity === 'private') {
    }
  }

  // Get a cache item if it exists and is not expired
  get(key: string): any | null {
    const item = this.cache[key];
    if (!item) return null;

    // Check if expired
    if (Date.now() - item.timestamp > item.expiresIn) {
      // Remove expired item
      this.remove(key);
      return null;
    }

    // Update last accessed time for LRU
    item.lastAccessed = Date.now();

    // Log access to sensitive data (for security auditing)
    if (item.sensitivity === 'private') {
    }

    return item.data;
  }

  // Remove a specific item from cache
  remove(key: string): void {
    if (this.cache[key]) {
      // Log removal of sensitive data (for security auditing)
      if (this.cache[key].sensitivity === 'private') {
      }

      this.currentSize -= this.cache[key].size;

      // Securely delete sensitive data by overwriting with null before deletion
      if (this.cache[key].sensitivity === 'private') {
        this.cache[key].data = null;
      }

      delete this.cache[key];
    }
  }

  // Clear all cache or items matching a prefix
  clear(prefix?: string): void {
    if (prefix) {


      // Keep track of how many items we're removing
      let removedCount = 0;
      let removedKeys: string[] = [];

      // First, identify all keys to remove
      Object.keys(this.cache).forEach(key => {
        if (key.startsWith(prefix)) {
          removedKeys.push(key);
        }
      });

      // Then remove them all
      removedKeys.forEach(key => {
        this.remove(key);
        removedCount++;
      });

    } else {

      // Securely delete sensitive data by overwriting with null before deletion
      Object.keys(this.cache).forEach(key => {
        if (this.cache[key].sensitivity === 'private') {
          this.cache[key].data = null;
        }
      });

      this.cache = {};
      this.currentSize = 0;
    }
  }

  // Make space for new items using LRU eviction
  private ensureSpace(newItemSize: number): void {
    // First, remove expired items
    this.cleanup();

    // If we still need space, remove least recently used items
    if (this.currentSize + newItemSize > this.maxCacheSize || Object.keys(this.cache).length >= this.maxItems) {
      // Sort items by last accessed time (oldest first)
      const items = Object.entries(this.cache)
        .sort(([, a], [, b]) => a.lastAccessed - b.lastAccessed);

      // Remove items until we have enough space
      while (items.length > 0 && (this.currentSize + newItemSize > this.maxCacheSize ||
             Object.keys(this.cache).length >= this.maxItems)) {
        const [key] = items.shift()!;
        this.remove(key);
      }
    }
  }

  // Clean up expired items
  cleanup(): void {
    const now = Date.now();
    let removedCount = 0;
    let removedSize = 0;

    Object.keys(this.cache).forEach(key => {
      const item = this.cache[key];
      if (now - item.timestamp > item.expiresIn) {
        removedSize += item.size;
        this.remove(key);
        removedCount++;
      }
    });

    if (removedCount > 0) {
    }
  }
}

// Create a singleton instance
export const apiCache = new ApiCache();

// Create axios instance with default config
const api = axios.create({
  baseURL: API_BASE_URL,
  withCredentials: true, // Important for cookies
});

// Add request interceptor for CSRF token
api.interceptors.request.use((config) => {
  // Get CSRF token from cookie
  const csrfToken = document.cookie
    .split('; ')
    .find(row => row.startsWith('csrf_token='))
    ?.split('=')[1];

  if (csrfToken) {
    config.headers['X-CSRF-Token'] = csrfToken;
  }

  return config;
});

// Remove duplicate response interceptor - handled in authService.ts
// This prevents conflicts and NS_BINDING_ABORTED errors

// Enhanced response interceptor for error handling
// Optimized for production with 500 users
api.interceptors.response.use(
  (response) => response,
  async (error) => {
    // Track failed requests for analytics (reduced logging)

    // Handle different error scenarios
    if (error.response) {
      // The request was made and the server responded with a status code
      // that falls out of the range of 2xx

      // Handle token expiration (401 Unauthorized)
      if (error.response.status === 401) {
        const originalRequest = error.config;

        // Skip retry if explicitly disabled (for auth check requests)
        if (originalRequest._skipRetry) {
          return Promise.reject(error);
        }

        // Only attempt token refresh once to prevent infinite loops
        if (!originalRequest._retry) {
          originalRequest._retry = true;

          try {
            // Get CSRF token for the refresh request
            const csrfToken = await authService.fetchCsrfToken();

            // Prepare headers for token refresh
            const headers: Record<string, string> = {
              'Content-Type': 'application/json'
            };

            if (csrfToken) {
              headers['X-CSRF-Token'] = csrfToken;
            }

            // Attempt to refresh token
            // We don't need to send the refresh token in the request body
            // as it's in the HttpOnly cookie
            const response = await axios.post(
              `${API_BASE_URL}/auth/refresh`,
              {}, // Empty body, token is in cookie
              {
                headers,
                withCredentials: true, // Important for cookies
                timeout: 10000 // 10 second timeout
              }
            );

            if (response.status === 200) {
              // Tokens are now stored as HttpOnly cookies by the backend
              // Update last activity time for session tracking
              sessionStorage.setItem('last_activity', Date.now().toString());

              // Update user data if provided
              if (response.data.user) {
                sessionStorage.setItem('user', JSON.stringify(response.data.user));
              }

              // Retry the original request - cookies will be sent automatically
              return axios(originalRequest);
            } else {
              authService.logout();
              return Promise.reject(error);
            }
          } catch (refreshError: any) {
            // Handle session conflicts specifically
            if (refreshError.response?.status === 409) {
              // Session conflict detected - let the auth context handle it
              error.sessionConflict = true;
              error.conflictData = refreshError.response.data;
              return Promise.reject(error);
            }

            // If refresh fails, logout with appropriate message
            sessionStorage.setItem('logout_reason', 'session_expired');
            authService.logout();

            // Enhance the error message for better UX
            if (refreshError.response?.status === 429) {
              error.userMessage = 'Too many requests. Please try again later.';
            } else {
              error.userMessage = 'Your session has expired. Please log in again.';
            }

            return Promise.reject(error);
          }
        }
      }

      // Handle session conflicts (409 Conflict)
      else if (error.response.status === 409) {
        const conflictData = error.response.data;
        if (conflictData?.code === 'SESSION_CONFLICT') {
          // Mark this as a session conflict for the auth context to handle
          error.sessionConflict = true;
          error.conflictData = conflictData;
          error.userMessage = 'Another session is active on a different device.';
        }
      }

      // Handle rate limiting (429 Too Many Requests)
      else if (error.response.status === 429) {

        // Extract retry-after header if available
        const retryAfter = error.response.headers['retry-after'];
        let waitTime = 30; // Default 30 seconds

        if (retryAfter && !isNaN(parseInt(retryAfter))) {
          waitTime = parseInt(retryAfter);
        }

        // Add user-friendly message
        error.userMessage = `Rate limit exceeded. Please try again in ${waitTime} seconds.`;
      }

      // Handle server errors (5xx)
      else if (error.response.status >= 500) {
        error.userMessage = 'The server encountered an error. Please try again later.';

        // For 503 Service Unavailable, suggest maintenance
        if (error.response.status === 503) {
          error.userMessage = 'The service is temporarily unavailable. This may be due to maintenance.';
        }
      }

      // Handle validation errors (400 Bad Request)
      else if (error.response.status === 400) {

        // Try to extract validation error details
        if (error.response.data?.errors || error.response.data?.detail) {
          const details = error.response.data.errors || error.response.data.detail;
          if (typeof details === 'string') {
            error.userMessage = details;
          } else if (Array.isArray(details)) {
            error.userMessage = details.join('. ');
          } else if (typeof details === 'object') {
            error.userMessage = 'Validation error: ' +
              Object.entries(details)
                .map(([key, value]) => `${key}: ${value}`)
                .join('; ');
          }
        } else {
          error.userMessage = 'Invalid request. Please check your input.';
        }
      }

      // Handle forbidden errors (403)
      else if (error.response.status === 403) {
        error.userMessage = 'You do not have permission to perform this action.';
      }

      // Handle not found errors (404)
      else if (error.response.status === 404) {
        error.userMessage = 'The requested resource was not found.';
      }
    }
    // Handle network errors
    else if (error.request) {
      error.userMessage = 'Network error. Please check your internet connection.';

      // Check if it's a timeout
      if (error.code === 'ECONNABORTED') {
        error.userMessage = 'The request timed out. Please try again.';
      }
    }
    // Handle other errors
    else {
      error.userMessage = 'An unexpected error occurred.';
    }

    // Pass the enhanced error to the caller
    return Promise.reject(error);
  }
);

/**
 * Upload a complaint file
 */
export const uploadComplaint = async (
  formData: FormData,
  options?: { onProgress?: (progress: number) => void }
): Promise<{ success: boolean; data?: any; complaint_id?: string; error?: string }> => {
  try {
    const response = await authService.api.post('/complaints/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      onUploadProgress: (progressEvent) => {
        if (options?.onProgress && progressEvent.total) {
          const progress = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          options.onProgress(progress);
        }
      }
    });

    apiCache.clear('complaints_list');

    return {
      success: true,
      data: response.data,
      complaint_id: response.data.complaint_id
    };
  } catch (error) {
    const axiosError = error as AxiosError<ErrorResponse>;
    return {
      success: false,
      error: axiosError.response?.data?.detail || axiosError.message || 'Upload failed'
    };
  }
};

/**
 * Get a list of complaints with caching and pagination support
 *
 * @param forceRefresh Whether to force a refresh from the server
 * @param page Page number (1-based)
 * @param limit Number of items per page
 * @returns Promise with complaint data
 */
export const getComplaints = async (
  forceRefresh: boolean = false,
  page: number = 1,
  limit: number = 10
): Promise<{ success: boolean; data?: any; error?: string }> => {
  try {
    const cacheKey = `complaints_list_page${page}_limit${limit}`;

    if (forceRefresh) {
      apiCache.remove(cacheKey);
      apiCache.clear('complaints_list');
    }

    if (!forceRefresh) {
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        return {
          success: true,
          data: cachedData
        };
      }
    }

    const response = await authService.api.get('/complaints', {
      params: { page, limit }
    });

    apiCache.set(cacheKey, response.data);

    return {
      success: true,
      data: response.data
    };
  } catch (error) {
    const axiosError = error as AxiosError<ErrorResponse>;
    return {
      success: false,
      error: axiosError.response?.data?.detail || axiosError.message || 'Failed to fetch complaints'
    };
  }
};

/**
 * Get a single complaint by ID with caching
 */
export const getComplaintById = async (
  id: string,
  forceRefresh: boolean = false
): Promise<{ success: boolean; data?: any; error?: string }> => {
  try {
    const cacheKey = `complaint_${id}`;

    if (!forceRefresh) {
      const cachedData = apiCache.get(cacheKey);
      if (cachedData) {
        return {
          success: true,
          data: cachedData
        };
      }
    }

    const response = await authService.api.get(`/complaints`, {
      params: { complaint_id: id }
    });

    apiCache.set(cacheKey, response.data);

    return {
      success: true,
      data: response.data
    };
  } catch (error) {
    const axiosError = error as AxiosError<ErrorResponse>;
    return {
      success: false,
      error: axiosError.response?.data?.detail || axiosError.message || 'Failed to fetch complaint'
    };
  }
};

/**
 * Smart API client that uses enhanced error handling
 * This wrapper provides user-friendly error messages and cooldown logic
 */
export const smartApi = {
  async get(url: string, config?: any) {
    try {
      return await api.get(url, config);
    } catch (error) {
      // Let the error manager handle user-friendly error processing
      // The actual error display will be handled by components using showSmartError
      throw error;
    }
  },

  async post(url: string, data?: any, config?: any) {
    try {
      return await api.post(url, data, config);
    } catch (error) {
      throw error;
    }
  },

  async put(url: string, data?: any, config?: any) {
    try {
      return await api.put(url, data, config);
    } catch (error) {
      throw error;
    }
  },

  async delete(url: string, config?: any) {
    try {
      return await api.delete(url, config);
    } catch (error) {
      throw error;
    }
  },

  async patch(url: string, data?: any, config?: any) {
    try {
      return await api.patch(url, data, config);
    } catch (error) {
      throw error;
    }
  }
};

export default api;

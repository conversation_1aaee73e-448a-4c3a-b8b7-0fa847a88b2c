import React, { useState, useEffect, useRef } from 'react';
import { useAuth } from '../context/AuthContext';
import { useAlert } from '../context/TailwindAlertContext';
import { FiAlertTriangle, FiMail, FiLogOut, FiClock, FiX } from 'react-icons/fi';
import Button from './ui/Button';
import Card from './ui/Card';
import Modal from './ui/Modal';

interface SubscriptionWarningProps {
  className?: string;
}

const SubscriptionWarning: React.FC<SubscriptionWarningProps> = ({ className = '' }) => {
  const { user, logout } = useAuth();
  const { showSuccess, showError } = useAlert();
  const [isExpired, setIsExpired] = useState(false);
  const [daysRemaining, setDaysRemaining] = useState<number | null>(null);
  const [showWarning, setShowWarning] = useState(false);
  const [showModal, setShowModal] = useState(false);
  const [autoLogoutCountdown, setAutoLogoutCountdown] = useState<number | null>(null);
  const logoutTimerRef = useRef<number | null>(null);
  const countdownTimerRef = useRef<number | null>(null);

  // Clear timers on unmount
  useEffect(() => {
    return () => {
      if (logoutTimerRef.current) {
        clearTimeout(logoutTimerRef.current);
      }
      if (countdownTimerRef.current) {
        clearInterval(countdownTimerRef.current);
      }
    };
  }, []);

  useEffect(() => {
    if (!user) return;

    const checkSubscriptionStatus = () => {
      const now = new Date();
      const subscriptionExpires = user.subscription_expires ? new Date(user.subscription_expires) : null;

      if (!user.paid || !subscriptionExpires) {
        // User is not paid or no expiration date - show expired modal
        setIsExpired(true);
        setShowWarning(true);
        setShowModal(true);
        setDaysRemaining(null);
        startAutoLogoutTimer();
        return;
      }

      // Calculate days remaining
      const timeDiff = subscriptionExpires.getTime() - now.getTime();
      const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));

      if (daysDiff <= 0) {
        // Subscription has expired - show modal and start auto-logout
        setIsExpired(true);
        setShowWarning(true);
        setShowModal(true);
        setDaysRemaining(0);
        startAutoLogoutTimer();
      } else if (daysDiff <= 7) {
        // Subscription expires within 7 days - show warning banner only
        setIsExpired(false);
        setShowWarning(true);
        setShowModal(false);
        setDaysRemaining(daysDiff);
        clearAutoLogoutTimer();
      } else {
        // Subscription is active with more than 7 days remaining
        setIsExpired(false);
        setShowWarning(false);
        setShowModal(false);
        setDaysRemaining(daysDiff);
        clearAutoLogoutTimer();
      }
    };

    checkSubscriptionStatus();

    // Check every minute for real-time updates
    const interval = setInterval(checkSubscriptionStatus, 60000);

    return () => clearInterval(interval);
  }, [user]);

  const startAutoLogoutTimer = () => {
    // Clear any existing timers
    clearAutoLogoutTimer();

    // Start 15-second countdown
    setAutoLogoutCountdown(15);

    // Update countdown every second
    countdownTimerRef.current = setInterval(() => {
      setAutoLogoutCountdown(prev => {
        if (prev === null || prev <= 1) {
          return null;
        }
        return prev - 1;
      });
    }, 1000);

    // Auto-logout after 15 seconds
    logoutTimerRef.current = setTimeout(async () => {
      try {
        await logout();
        showError('Your subscription has expired. You have been logged out automatically.');
      } catch (error) {
        showError('Session expired. Please log in again.');
        window.location.href = '/login';
      }
    }, 15000);
  };

  const clearAutoLogoutTimer = () => {
    if (logoutTimerRef.current) {
      clearTimeout(logoutTimerRef.current);
      logoutTimerRef.current = null;
    }
    if (countdownTimerRef.current) {
      clearInterval(countdownTimerRef.current);
      countdownTimerRef.current = null;
    }
    setAutoLogoutCountdown(null);
  };

  const handleContactDeveloper = () => {
    // Clear auto-logout timer when user takes action
    clearAutoLogoutTimer();

    const subject = encodeURIComponent('NCRP Matrix Subscription Renewal Request');
    const body = encodeURIComponent(`Hello,

I would like to renew my NCRP Matrix subscription.

Account Details:
- Email: ${user?.email}
- Name: ${user?.first_name} ${user?.last_name || ''}
- Current Status: ${isExpired ? 'Expired' : `Expires in ${daysRemaining} days`}

Please provide me with renewal options and payment details.

Thank you!`);

    const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
    window.open(mailtoLink, '_blank');
    showSuccess('Email client opened. Please send the email to request subscription renewal.');
  };

  const handleLogout = async () => {
    // Clear auto-logout timer
    clearAutoLogoutTimer();

    try {
      await logout();
      showSuccess('Logged out successfully');
    } catch (error) {
      showError('Error logging out. Please try again.');
    }
  };

  const getWarningMessage = () => {
    if (isExpired) {
      return {
        title: '🚨 Subscription Expired',
        message: autoLogoutCountdown
          ? `Your subscription has expired. You will be logged out automatically in ${autoLogoutCountdown} seconds.`
          : 'Your subscription has expired. Please renew to continue using NCRP Matrix features.',
        urgency: 'high'
      };
    } else if (daysRemaining !== null && daysRemaining <= 3) {
      return {
        title: '⚠️ Subscription Expiring Soon',
        message: `Your subscription expires in ${daysRemaining} day${daysRemaining !== 1 ? 's' : ''}. Please renew to avoid service interruption.`,
        urgency: 'medium'
      };
    } else if (daysRemaining !== null && daysRemaining <= 7) {
      return {
        title: '📅 Subscription Reminder',
        message: `Your subscription expires in ${daysRemaining} days. Consider renewing soon.`,
        urgency: 'low'
      };
    }
    return null;
  };

  if (!showWarning || !user) {
    return null;
  }

  const warningInfo = getWarningMessage();
  if (!warningInfo) return null;

  const getWarningStyles = () => {
    switch (warningInfo.urgency) {
      case 'high':
        return {
          bg: 'bg-red-50 dark:bg-red-900/20',
          border: 'border-red-200 dark:border-red-800',
          text: 'text-red-800 dark:text-red-200',
          icon: 'text-red-600 dark:text-red-400'
        };
      case 'medium':
        return {
          bg: 'bg-orange-50 dark:bg-orange-900/20',
          border: 'border-orange-200 dark:border-orange-800',
          text: 'text-orange-800 dark:text-orange-200',
          icon: 'text-orange-600 dark:text-orange-400'
        };
      case 'low':
        return {
          bg: 'bg-yellow-50 dark:bg-yellow-900/20',
          border: 'border-yellow-200 dark:border-yellow-800',
          text: 'text-yellow-800 dark:text-yellow-200',
          icon: 'text-yellow-600 dark:text-yellow-400'
        };
      default:
        return {
          bg: 'bg-gray-50 dark:bg-gray-900/20',
          border: 'border-gray-200 dark:border-gray-800',
          text: 'text-gray-800 dark:text-gray-200',
          icon: 'text-gray-600 dark:text-gray-400'
        };
    }
  };

  const styles = getWarningStyles();

  return (
    <>
      {/* Modal for expired subscriptions */}
      {showModal && isExpired && (
        <Modal
          isOpen={showModal}
          onClose={() => {
            // Don't allow closing modal for expired subscriptions
            if (!isExpired) {
              setShowModal(false);
            }
          }}
          title={warningInfo.title}
          size="md"
          className="z-[9999]"
        >
          <div className="p-6">
            <div className="flex items-start space-x-4 mb-6">
              <div className="flex-shrink-0 text-red-500">
                <FiAlertTriangle className="w-8 h-8" />
              </div>
              <div className="flex-1">
                <p className="text-gray-700 dark:text-gray-300 mb-4">
                  {warningInfo.message}
                </p>

                {user.subscription_expires && (
                  <p className="text-sm text-gray-500 dark:text-gray-400 mb-4">
                    Subscription expired: {new Date(user.subscription_expires).toLocaleDateString()}
                  </p>
                )}

                {autoLogoutCountdown && (
                  <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3 mb-4">
                    <p className="text-red-800 dark:text-red-200 text-sm font-medium">
                      ⏰ Auto-logout in {autoLogoutCountdown} seconds
                    </p>
                  </div>
                )}
              </div>
            </div>

            <div className="flex flex-col sm:flex-row gap-3">
              <Button
                onClick={handleContactDeveloper}
                className="flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white flex-1"
              >
                <FiMail className="w-4 h-4" />
                <span>Contact for Renewal</span>
              </Button>

              <Button
                onClick={handleLogout}
                variant="outlined"
                className="flex items-center justify-center space-x-2 flex-1"
              >
                <FiLogOut className="w-4 h-4" />
                <span>Logout Now</span>
              </Button>
            </div>
          </div>
        </Modal>
      )}

      {/* Banner warning for non-expired subscriptions */}
      {showWarning && !showModal && (
        <Card className={`${className} ${styles.bg} ${styles.border} border-2`}>
          <Card.Content className="p-4">
            <div className="flex items-start space-x-3">
              <div className={`flex-shrink-0 ${styles.icon}`}>
                <FiClock className="w-6 h-6" />
              </div>

              <div className="flex-1 min-w-0">
                <h3 className={`text-lg font-semibold ${styles.text} mb-2`}>
                  {warningInfo.title}
                </h3>
                <p className={`text-sm ${styles.text} mb-4`}>
                  {warningInfo.message}
                </p>

                {user.subscription_expires && (
                  <p className={`text-xs ${styles.text} opacity-75 mb-4`}>
                    Subscription expires: {new Date(user.subscription_expires).toLocaleDateString()}
                  </p>
                )}

                <div className="flex flex-col sm:flex-row gap-3">
                  <Button
                    onClick={handleContactDeveloper}
                    className="flex items-center justify-center space-x-2 bg-blue-600 hover:bg-blue-700 text-white"
                    size="sm"
                  >
                    <FiMail className="w-4 h-4" />
                    <span>Contact for Renewal</span>
                  </Button>

                  <Button
                    onClick={handleLogout}
                    variant="outlined"
                    className="flex items-center justify-center space-x-2"
                    size="sm"
                  >
                    <FiLogOut className="w-4 h-4" />
                    <span>Logout</span>
                  </Button>
                </div>
              </div>

              {/* Only show close button for low urgency warnings */}
              {warningInfo.urgency === 'low' && (
                <button
                  onClick={() => setShowWarning(false)}
                  className={`flex-shrink-0 ${styles.icon} hover:opacity-75 transition-opacity`}
                  aria-label="Dismiss warning"
                >
                  <FiX className="w-5 h-5" />
                </button>
              )}
            </div>
          </Card.Content>
        </Card>
      )}
    </>
  );
};

export default SubscriptionWarning;

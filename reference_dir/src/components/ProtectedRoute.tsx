import React, { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../context/AuthContext';

import { useAlert } from '../context/TailwindAlertContext';
import SessionConflictDialog from './SessionConflictDialog';
import LoadingScreen from './common/LoadingScreen';
import SubscriptionWarning from './SubscriptionWarning';


interface ProtectedRouteProps {
  children: React.ReactNode;
  requirePaid?: boolean;
}

/**
 * A component that protects routes requiring authentication
 * Redirects to login page if user is not authenticated
 * Shows paid access required message if route requires paid access
 *
 * @param children The components to render if authenticated
 * @param requirePaid Whether this route requires paid access
 */
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  requirePaid = false
}) => {
  const { isAuthenticated, loading, user, logout, refreshUserData, postLoginLoading, setPostLoginLoading } = useAuth();
  const navigate = useNavigate();
  const { showError } = useAlert();
  const [isChecking, setIsChecking] = useState(true);
  const [showSessionConflict, setShowSessionConflict] = useState(false);
  const [activeSessionInfo, setActiveSessionInfo] = useState<{id: string, lastActive?: string} | undefined>();

  useEffect(() => {
    const checkAuth = async () => {
      try {
        setIsChecking(true);

        if (!loading) {

          // First check if user is authenticated
          if (!isAuthenticated) {
            try {
              await logout();
              navigate('/login', { replace: true });
              return;
            } catch (error) {
              navigate('/login', { replace: true });
              return;
            }
          }

          // If we need to refresh user data
          if (!user) {
            try {
              const userData = await refreshUserData();

              if (!userData) {
                await logout();
                navigate('/login', { replace: true });
                return;
              }
            } catch (error: any) {
              // Check if this is a session conflict error
              if (error.response?.status === 409 && error.response?.data?.code === 'SESSION_CONFLICT') {

                // Show session conflict dialog
                setActiveSessionInfo({
                  id: error.response.data.active_session.id,
                  lastActive: error.response.data.active_session.last_active
                });
                setShowSessionConflict(true);
                setIsChecking(false);

                // Don't proceed with other checks until conflict is resolved
                return;
              } else {
                // For other errors, redirect to login
                showError('Authentication error. Please try logging in again.');
                navigate('/login', { replace: true });
                return;
              }
            }
          }

          // Then check if email is verified
          if (user && !user.email_verified) {
            navigate('/resend-verification', { replace: true });
            return;
          }

          // Finally check if paid access is required
          if (requirePaid) {
            // Ensure user and paid status are properly defined
            if (!user || typeof user.paid !== 'boolean') {
              await refreshUserData();
              return;
            }

            // Check if subscription has expired
            const now = new Date();
            const subscriptionExpires = user.subscription_expires ? new Date(user.subscription_expires) : null;

            if (!user.paid || (subscriptionExpires && subscriptionExpires < now)) {
              showError('Your subscription has expired. Please renew to continue using this feature.');
              return;
            }
          }

          // If we get here, all checks passed
        }
      } catch (error) {
        showError('Authentication error. Please try logging in again.');
        navigate('/login', { replace: true });
      } finally {
        setIsChecking(false);
      }
    };

    checkAuth();
  }, [isAuthenticated, loading, navigate, user, logout, requirePaid, showError, refreshUserData]);

  // Show post-login loading screen
  if (postLoginLoading) {
    return (
      <LoadingScreen
        message="Setting up your workspace..."
        duration={4500}
        onComplete={() => {
          setPostLoginLoading(false);
        }}
      />
    );
  }

  // Show loading state while checking authentication
  if (loading || isChecking) {
    return (
      <LoadingScreen
        message="Verifying authentication..."
        duration={3000}
      />
    );
  }

  // GLOBAL SUBSCRIPTION CHECK - Block ALL access if subscription is expired
  if (user) {
    const now = new Date();
    const subscriptionExpires = user.subscription_expires ? new Date(user.subscription_expires) : null;
    const isSubscriptionExpired = !user.paid || (subscriptionExpires && subscriptionExpires < now);

    // If subscription is expired, show the subscription warning modal globally
    // This blocks access to ALL protected routes, not just paid ones
    if (isSubscriptionExpired) {
      return (
        <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
          <SubscriptionWarning className="max-w-2xl w-full mx-4" />
        </div>
      );
    }
  }

  // Legacy check removed - now handled by global subscription check above

  // Handle session conflict dialog close
  const handleSessionConflictClose = () => {
    setShowSessionConflict(false);
    // Refresh the page to retry authentication
    window.location.reload();
  };

  // If all checks pass, render the children with session conflict dialog if needed
  return (
    <>
      {children}
      {showSessionConflict && (
        <SessionConflictDialog
          isOpen={showSessionConflict}
          onClose={handleSessionConflictClose}
          activeSessionInfo={activeSessionInfo}
        />
      )}
    </>
  );
};

export default ProtectedRoute;

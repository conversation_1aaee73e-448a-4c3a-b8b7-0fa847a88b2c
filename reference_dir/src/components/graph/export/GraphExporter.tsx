import React, { useState, useCallback } from 'react';
import { Node, Edge, getNodesBounds, getViewportForBounds } from 'reactflow';

interface GraphExporterProps {
  nodes: Node[];
  edges: Edge[];
  complaintId: string;
  isDarkMode: boolean;
  onTemporaryExpand?: () => void;
  onRestoreState?: () => void;
  onExportStateChange?: (isExporting: boolean) => void;
  onExportProgressChange?: (progress: { current: number; total: number; currentPage?: string }) => void;
  reactFlowInstance?: any; // ReactFlow instance for viewport control
}



type ExportMode = 'single' | 'multi';

interface ExportProgress {
  current: number;
  total: number;
  currentPage?: string;
  skippedPages?: string[];
  timeoutWarning?: boolean;
}

export const GraphExporter: React.FC<GraphExporterProps> = ({
  nodes,
  edges,
  complaintId,
  onTemporaryExpand,
  onRestoreState,
  onExportStateChange,
  onExportProgressChange,
  reactFlowInstance
}) => {
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState<ExportProgress>({ current: 0, total: 100 });
  const [showModeSelector, setShowModeSelector] = useState(false);
  const [exportMode, setExportMode] = useState<ExportMode>('single');

  // Helper functions to update state and notify parent
  const updateExportState = useCallback((exporting: boolean) => {
    setIsExporting(exporting);
    onExportStateChange?.(exporting);
  }, [onExportStateChange]);

  const updateExportProgress = useCallback((progress: ExportProgress) => {
    setExportProgress(progress);
    onExportProgressChange?.(progress);
  }, [onExportProgressChange]);

  // Timeout utility function
  const createTimeoutPromise = useCallback((timeoutMs: number, message: string): Promise<never> => {
    return new Promise((_, reject) => {
      setTimeout(() => {
        reject(new Error(`Timeout: ${message} (${timeoutMs / 1000}s)`));
      }, timeoutMs);
    });
  }, []);

  // Wrapper function to add timeout to any async operation
  const withTimeout = useCallback((
    operation: Promise<any>,
    timeoutMs: number,
    timeoutMessage: string
  ): Promise<any> => {
    return Promise.race([
      operation,
      createTimeoutPromise(timeoutMs, timeoutMessage)
    ]);
  }, [createTimeoutPromise]);

  // Helper function to get sender nodes
  const getSenderNodes = useCallback((): Node[] => {
    return nodes.filter(node => node.data?.isSenderNode === true);
  }, [nodes]);

  // Helper function to get all descendants of a node
  const getNodeDescendants = useCallback((parentId: string): Node[] => {
    const descendants: Node[] = [];
    const visited = new Set<string>();

    const findChildren = (nodeId: string) => {
      if (visited.has(nodeId)) return;
      visited.add(nodeId);

      const children = nodes.filter(node => node.data?.parentId === nodeId);
      children.forEach(child => {
        descendants.push(child);
        findChildren(child.id);
      });
    };

    findChildren(parentId);
    return descendants;
  }, [nodes]);

  // Helper function to get edges for specific nodes
  const getEdgesForNodes = useCallback((nodeIds: string[]): Edge[] => {
    const nodeIdSet = new Set(nodeIds);
    return edges.filter(edge =>
      nodeIdSet.has(edge.source) && nodeIdSet.has(edge.target)
    );
  }, [edges]);



  // Helper function to optimize node styling for export
  const optimizeNodesForExport = useCallback((): (() => void) => {
    const reactFlowElement = document.querySelector('.react-flow') as HTMLElement;
    if (!reactFlowElement) {
      throw new Error('Graph container not found');
    }

    const originalStyles = new Map();

    // Get all node elements
    const nodeElements = reactFlowElement.querySelectorAll('.react-flow__node');

    nodeElements.forEach((nodeEl: any) => {
      const nodeId = nodeEl.getAttribute('data-id');
      if (nodeId) {
        // Store original styles
        const computedStyle = window.getComputedStyle(nodeEl);
        originalStyles.set(nodeId, {
          backgroundColor: nodeEl.style.backgroundColor || computedStyle.backgroundColor,
          backgroundImage: nodeEl.style.backgroundImage || computedStyle.backgroundImage,
          backdropFilter: nodeEl.style.backdropFilter || computedStyle.backdropFilter,
          boxShadow: nodeEl.style.boxShadow || computedStyle.boxShadow,
          border: nodeEl.style.border || computedStyle.border,
          borderRadius: nodeEl.style.borderRadius || computedStyle.borderRadius
        });

        // Apply export-optimized styles - clean and minimal
        nodeEl.style.backgroundColor = 'white';
        nodeEl.style.backgroundImage = 'none';
        nodeEl.style.backdropFilter = 'none';
        nodeEl.style.boxShadow = 'none';
        nodeEl.style.border = 'none'; // Remove borders for cleaner look
        nodeEl.style.borderRadius = '4px';

        // Optimize text elements within nodes
        const textElements = nodeEl.querySelectorAll('*');
        textElements.forEach((textEl: any) => {
          if (textEl.style) {
            const originalTextStyles = {
              color: textEl.style.color || window.getComputedStyle(textEl).color,
              textShadow: textEl.style.textShadow || window.getComputedStyle(textEl).textShadow,
              backgroundColor: textEl.style.backgroundColor || window.getComputedStyle(textEl).backgroundColor
            };
            originalStyles.set(`${nodeId}_text_${textEl.tagName}_${Array.from(textEl.parentNode?.children || []).indexOf(textEl)}`, originalTextStyles);

            textEl.style.color = '#000000';
            textEl.style.textShadow = 'none';
            if (textEl.style.backgroundColor && textEl.style.backgroundColor !== 'transparent') {
              textEl.style.backgroundColor = 'transparent';
            }
          }
        });
      }
    });

    // Return restore function
    return () => {
      nodeElements.forEach((nodeEl: any) => {
        const nodeId = nodeEl.getAttribute('data-id');
        if (nodeId && originalStyles.has(nodeId)) {
          const styles = originalStyles.get(nodeId);
          nodeEl.style.backgroundColor = styles.backgroundColor;
          nodeEl.style.backgroundImage = styles.backgroundImage;
          nodeEl.style.backdropFilter = styles.backdropFilter;
          nodeEl.style.boxShadow = styles.boxShadow;
          nodeEl.style.border = styles.border;
          nodeEl.style.borderRadius = styles.borderRadius;

          // Restore text styles
          const textElements = nodeEl.querySelectorAll('*');
          textElements.forEach((textEl: any, index: number) => {
            const textStyleKey = `${nodeId}_text_${textEl.tagName}_${index}`;
            if (originalStyles.has(textStyleKey)) {
              const textStyles = originalStyles.get(textStyleKey);
              textEl.style.color = textStyles.color;
              textEl.style.textShadow = textStyles.textShadow;
              textEl.style.backgroundColor = textStyles.backgroundColor;
            }
          });
        }
      });
    };
  }, []);

  // Advanced graph preparation with precise layout handling
  const prepareGraphForCapture = useCallback(async (specificNodes?: Node[]): Promise<{ restore: () => void; containerWidth: number; containerHeight: number }> => {
    const reactFlowElement = document.querySelector('.react-flow') as HTMLElement;
    if (!reactFlowElement) {
      throw new Error('ReactFlow element not found');
    }

    // Store original states
    const originalViewport = reactFlowInstance?.getViewport?.() || { x: 0, y: 0, zoom: 1 };
    const originalReactFlowStyles = {
      position: reactFlowElement.style.position,
      top: reactFlowElement.style.top,
      left: reactFlowElement.style.left,
      width: reactFlowElement.style.width,
      height: reactFlowElement.style.height,
      transform: reactFlowElement.style.transform,
      overflow: reactFlowElement.style.overflow,
      zIndex: reactFlowElement.style.zIndex
    };

    // Store original body and layout styles
    const originalBodyStyles = {
      overflow: document.body.style.overflow,
      margin: document.body.style.margin,
      padding: document.body.style.padding
    };

    // Find and completely hide the sidebar container
    const sidebarElement = document.querySelector('aside') as HTMLElement;
    const originalSidebarStyles = sidebarElement ? {
      display: sidebarElement.style.display,
      visibility: sidebarElement.style.visibility,
      width: sidebarElement.style.width,
      transform: sidebarElement.style.transform
    } : null;

    // Find the main content container that has margin-left due to sidebar
    const mainContentElement = document.querySelector('main')?.parentElement as HTMLElement;
    const originalMainContentStyles = mainContentElement ? {
      marginLeft: mainContentElement.style.marginLeft,
      width: mainContentElement.style.width,
      maxWidth: mainContentElement.style.maxWidth
    } : null;

    // Completely hide sidebar and reset main content layout
    if (sidebarElement) {
      sidebarElement.style.display = 'none';
      sidebarElement.style.visibility = 'hidden';
      sidebarElement.style.width = '0';
      sidebarElement.style.transform = 'translateX(-100%)';
    }

    // Reset main content to full width without sidebar offset
    if (mainContentElement) {
      mainContentElement.style.marginLeft = '0';
      mainContentElement.style.width = '100%';
      mainContentElement.style.maxWidth = '100vw';
    }

    // Hide ReactFlow UI controls
    const reactFlowControls = [
      ...reactFlowElement.querySelectorAll('.react-flow__controls, .react-flow__minimap, .react-flow__panel')
    ];
    const originalControlsDisplay: string[] = [];
    reactFlowControls.forEach((el: any, index) => {
      originalControlsDisplay[index] = el.style.display;
      el.style.display = 'none';
    });

    // Reset body styles to eliminate layout interference
    document.body.style.overflow = 'visible';
    document.body.style.margin = '0';
    document.body.style.padding = '0';

    // Get nodes to include (filter out hidden nodes)
    const nodesToInclude = specificNodes || nodes.filter(node => !node.hidden);

    if (nodesToInclude.length === 0) {
      throw new Error('No visible nodes to export');
    }

    // Use ReactFlow's official getNodesBounds utility
    const bounds = getNodesBounds(nodesToInclude);

    // Add padding to bounds to ensure no clipping
    const padding = 50;
    const paddedBounds = {
      x: bounds.x - padding,
      y: bounds.y - padding,
      width: bounds.width + (2 * padding),
      height: bounds.height + (2 * padding)
    };

    // Calculate optimal container size for high quality capture
    const maxWidth = 2400; // High resolution for quality
    const maxHeight = 1800;

    // Calculate scale to fit content optimally
    const scaleX = maxWidth / paddedBounds.width;
    const scaleY = maxHeight / paddedBounds.height;
    const optimalScale = Math.min(scaleX, scaleY, 2); // Cap at 2x for quality

    const containerWidth = paddedBounds.width * optimalScale;
    const containerHeight = paddedBounds.height * optimalScale;

    // Calculate sidebar offset to shift graph left
    const sidebarWidth = sidebarElement ? sidebarElement.getBoundingClientRect().width : 0;

    // Use ReactFlow's getViewportForBounds with our optimized dimensions
    const viewport = getViewportForBounds(paddedBounds, containerWidth, containerHeight, 0.1, 4, 0);

    // Shift the viewport to the left to compensate for sidebar space
    const adjustedViewport = {
      ...viewport,
      x: viewport.x - (sidebarWidth / 2) // Shift left by half sidebar width for centering
    };


    // Position ReactFlow element to use full screen width without sidebar interference
    reactFlowElement.style.position = 'fixed';
    reactFlowElement.style.top = '0px';
    reactFlowElement.style.left = '0px'; // Start from absolute left edge (no sidebar offset)
    reactFlowElement.style.width = `${containerWidth}px`;
    reactFlowElement.style.height = `${containerHeight}px`;
    reactFlowElement.style.overflow = 'visible';
    reactFlowElement.style.zIndex = '9999';
    reactFlowElement.style.transform = 'none';
    reactFlowElement.style.margin = '0';
    reactFlowElement.style.padding = '0';

    // Force full width layout
    reactFlowElement.style.maxWidth = '100vw';
    reactFlowElement.style.minWidth = '100vw';

    // Apply the adjusted viewport (shifted left to compensate for sidebar)
    if (reactFlowInstance?.setViewport) {
      reactFlowInstance.setViewport(adjustedViewport, { duration: 0 });
    }

    // Wait for viewport and layout to stabilize
    await new Promise(resolve => setTimeout(resolve, 1000));

    return {
      containerWidth,
      containerHeight,
      restore: () => {
        // Restore original viewport
        if (reactFlowInstance?.setViewport) {
          reactFlowInstance.setViewport(originalViewport, { duration: 0 });
        }

        // Restore ReactFlow element styles
        Object.entries(originalReactFlowStyles).forEach(([key, value]) => {
          (reactFlowElement.style as any)[key] = value;
        });

        // Restore sidebar styles
        if (sidebarElement && originalSidebarStyles) {
          Object.entries(originalSidebarStyles).forEach(([key, value]) => {
            (sidebarElement.style as any)[key] = value;
          });
        }

        // Restore main content styles
        if (mainContentElement && originalMainContentStyles) {
          Object.entries(originalMainContentStyles).forEach(([key, value]) => {
            (mainContentElement.style as any)[key] = value;
          });
        }

        // Restore body styles
        Object.entries(originalBodyStyles).forEach(([key, value]) => {
          (document.body.style as any)[key] = value;
        });

        // Restore ReactFlow controls
        reactFlowControls.forEach((el: any, index) => {
          if (originalControlsDisplay[index] !== undefined) {
            el.style.display = originalControlsDisplay[index];
          }
        });
      }
    };
  }, [reactFlowInstance, nodes]);



  // Enhanced tree isolation and layout preparation for multi-page export
  const prepareIsolatedTreeForCapture = useCallback(async (senderNode: Node): Promise<{
    treeNodes: Node[];
    treeEdges: Edge[];
    restore: () => void;
    containerWidth: number;
    containerHeight: number;
  }> => {
    const reactFlowElement = document.querySelector('.react-flow') as HTMLElement;
    if (!reactFlowElement) {
      throw new Error('ReactFlow element not found');
    }

    // Get all nodes for this specific tree: sender + descendants only (skip metadata for cleaner pages)
    const senderDescendants = getNodeDescendants(senderNode.id);
    const treeNodes = [senderNode, ...senderDescendants];
    const treeNodeIds = treeNodes.map(node => node.id);
    const treeEdges = getEdgesForNodes(treeNodeIds);

    // Tree isolation completed for sender

    // Store original states for restoration
    const originalStates = new Map();
    const treeNodeIdSet = new Set(treeNodeIds);
    const treeEdgeIdSet = new Set(treeEdges.map(edge => edge.id));

    // Hide all nodes not in this tree
    const allNodes = reactFlowElement.querySelectorAll('.react-flow__node');
    allNodes.forEach((nodeEl: any) => {
      const nodeId = nodeEl.getAttribute('data-id');
      if (nodeId) {
        originalStates.set(`node-${nodeId}`, {
          display: nodeEl.style.display,
          visibility: nodeEl.style.visibility
        });

        if (treeNodeIdSet.has(nodeId)) {
          nodeEl.style.display = '';
          nodeEl.style.visibility = 'visible';
        } else {
          nodeEl.style.display = 'none';
          nodeEl.style.visibility = 'hidden';
        }
      }
    });

    // Hide all edges not in this tree
    const allEdges = reactFlowElement.querySelectorAll('.react-flow__edge');
    allEdges.forEach((edgeEl: any) => {
      const edgeId = edgeEl.getAttribute('data-id');
      if (edgeId) {
        originalStates.set(`edge-${edgeId}`, {
          display: edgeEl.style.display,
          visibility: edgeEl.style.visibility
        });

        if (treeEdgeIdSet.has(edgeId)) {
          edgeEl.style.display = '';
          edgeEl.style.visibility = 'visible';
        } else {
          edgeEl.style.display = 'none';
          edgeEl.style.visibility = 'hidden';
        }
      }
    });

    // Wait for DOM updates to take effect
    await new Promise(resolve => setTimeout(resolve, 300));

    // Now calculate bounds using only the visible tree nodes
    const bounds = getNodesBounds(treeNodes);

    // Add minimal padding to bounds
    const padding = 30;
    const paddedBounds = {
      x: bounds.x - padding,
      y: bounds.y - padding,
      width: bounds.width + (2 * padding),
      height: bounds.height + (2 * padding)
    };

    // Calculate optimal container size for this specific tree
    const maxWidth = 2000;
    const maxHeight = 1400;

    const scaleX = maxWidth / paddedBounds.width;
    const scaleY = maxHeight / paddedBounds.height;
    const optimalScale = Math.min(scaleX, scaleY, 1.5); // Cap at 1.5x for quality

    const containerWidth = paddedBounds.width * optimalScale;
    const containerHeight = paddedBounds.height * optimalScale;

    // Get viewport that focuses on this isolated tree
    const viewport = getViewportForBounds(paddedBounds, containerWidth, containerHeight, 0.1, 4, 0);


    // Apply the viewport to focus on the isolated tree
    if (reactFlowInstance?.setViewport) {
      reactFlowInstance.setViewport(viewport, { duration: 0 });
    }

    // Wait for viewport to stabilize
    await new Promise(resolve => setTimeout(resolve, 500));

    // Return tree data and restore function
    return {
      treeNodes,
      treeEdges,
      containerWidth,
      containerHeight,
      restore: () => {
        // Restore original node and edge visibility
        originalStates.forEach((state, key) => {
          const [type, id] = key.split('-');
          const selector = type === 'node' ? '.react-flow__node' : '.react-flow__edge';
          const element = reactFlowElement.querySelector(`${selector}[data-id="${id}"]`) as HTMLElement;
          if (element) {
            element.style.display = state.display;
            element.style.visibility = state.visibility;
          }
        });
      }
    };
  }, [nodes, edges, getNodeDescendants, getEdgesForNodes, reactFlowInstance]);

  // Helper function to wait for DOM updates to complete
  const waitForDOMUpdates = useCallback(async (maxWaitTime: number = 2000): Promise<void> => {
    return new Promise((resolve) => {
      let attempts = 0;
      const maxAttempts = maxWaitTime / 100; // Check every 100ms

      const checkStability = () => {
        attempts++;

        // Check if ReactFlow viewport has stabilized
        const viewportElement = document.querySelector('.react-flow__viewport') as HTMLElement;
        if (!viewportElement) {
          if (attempts < maxAttempts) {
            setTimeout(checkStability, 100);
          } else {
            resolve();
          }
          return;
        }

        // Check if all nodes are properly positioned and visible
        const nodeElements = viewportElement.querySelectorAll('.react-flow__node');
        let allNodesStable = true;

        nodeElements.forEach((nodeEl: any) => {
          const rect = nodeEl.getBoundingClientRect();
          if (rect.width === 0 || rect.height === 0) {
            allNodesStable = false;
          }
        });

        if (allNodesStable || attempts >= maxAttempts) {
          resolve();
        } else {
          setTimeout(checkStability, 100);
        }
      };

      // Start checking after a brief initial delay
      setTimeout(checkStability, 100);
    });
  }, []);

  // Helper function to temporarily expand all nodes
  const temporarilyExpandAllNodes = useCallback(async (): Promise<() => void> => {
    const reactFlowElement = document.querySelector('.react-flow') as HTMLElement;
    if (!reactFlowElement) {
      throw new Error('Graph container not found');
    }

    // Store original node visibility states
    const originalNodeStates = new Map();
    const nodeElements = reactFlowElement.querySelectorAll('[data-id]');

    nodeElements.forEach((nodeEl: any) => {
      const nodeId = nodeEl.getAttribute('data-id');
      if (nodeId) {
        originalNodeStates.set(nodeId, {
          display: nodeEl.style.display,
          visibility: nodeEl.style.visibility,
          opacity: nodeEl.style.opacity
        });
        // Temporarily show all nodes for export
        nodeEl.style.display = '';
        nodeEl.style.visibility = 'visible';
        nodeEl.style.opacity = '1';
      }
    });

    // Call external expand function if provided
    if (onTemporaryExpand) {
      onTemporaryExpand();
    }

    // Wait for DOM updates to complete and layout to stabilize
    await waitForDOMUpdates(3000); // Increased timeout for complex graphs

    // Return restore function
    return () => {
      originalNodeStates.forEach((state, nodeId) => {
        const nodeEl = reactFlowElement.querySelector(`[data-id="${nodeId}"]`) as HTMLElement;
        if (nodeEl) {
          nodeEl.style.display = state.display;
          nodeEl.style.visibility = state.visibility;
          nodeEl.style.opacity = state.opacity;
        }
      });

      if (onRestoreState) {
        onRestoreState();
      }
    };
  }, [onTemporaryExpand, onRestoreState, waitForDOMUpdates]);

  // Enhanced single page export with virtual container and timeout
  const exportSinglePagePDF = async () => {
    const SINGLE_PAGE_TIMEOUT = 30000; // 30 seconds timeout

    try {
      await withTimeout(
        (async () => {
          const [{ default: html2canvas }, { default: jsPDF }] = await Promise.all([
            import('html2canvas'),
            import('jspdf')
          ]);

          updateExportProgress({ current: 10, total: 100, currentPage: 'Preparing export...' });

          const reactFlowElement = document.querySelector('.react-flow') as HTMLElement;
          if (!reactFlowElement) {
            throw new Error('Graph container not found');
          }

          // Apply all optimizations with improved timing
          updateExportProgress({ current: 20, total: 100, currentPage: 'Expanding nodes...' });
          const restoreNodeState = await temporarilyExpandAllNodes();

          updateExportProgress({ current: 35, total: 100, currentPage: 'Optimizing styles...' });
          const restoreStyles = optimizeNodesForExport();

          updateExportProgress({ current: 50, total: 100, currentPage: 'Preparing graph for capture...' });
          const { restore: restoreContainer, containerWidth, containerHeight } = await prepareGraphForCapture();

          try {
            updateExportProgress({ current: 70, total: 100, currentPage: 'Stabilizing layout...' });

            // Wait for layout to stabilize
            await new Promise(resolve => setTimeout(resolve, 800));

            updateExportProgress({ current: 80, total: 100, currentPage: 'Capturing high-quality graph...' });

            // Capture with ultra-high quality settings and precise bounds
            const canvas = await html2canvas(reactFlowElement, {
              backgroundColor: 'white',
              scale: 4, // Ultra-high scale for maximum quality
              useCORS: true,
              allowTaint: true,
              logging: false,
              removeContainer: false,
              foreignObjectRendering: true,
              imageTimeout: 0,
              width: containerWidth,
              height: containerHeight,
              x: 0,
              y: 0,
              scrollX: 0,
              scrollY: 0,
              windowWidth: containerWidth,
              windowHeight: containerHeight
            });

            // Canvas capture completed

            updateExportProgress({ current: 85, total: 100, currentPage: 'Creating PDF...' });

            // Create PDF with A4 landscape format
            const pdf = new jsPDF({
              orientation: 'landscape',
              unit: 'mm',
              format: 'a4'
            });

            // Use maximum JPEG quality for crisp output
            const imgData = canvas.toDataURL('image/jpeg', 1.0);
            const pdfWidth = pdf.internal.pageSize.getWidth();
            const pdfHeight = pdf.internal.pageSize.getHeight();

            // Calculate optimal scaling to maximize PDF space usage
            const margin = 2; // Minimal margin
            const headerHeight = 8; // Space for header
            const availableWidth = pdfWidth - (2 * margin);
            const availableHeight = pdfHeight - headerHeight - margin;

            // Calculate scaling to fit perfectly within available space
            const scaleX = availableWidth / canvas.width;
            const scaleY = availableHeight / canvas.height;
            const scale = Math.min(scaleX, scaleY);

            const scaledWidth = canvas.width * scale;
            const scaledHeight = canvas.height * scale;

            // Center the image for optimal presentation
            const x = (pdfWidth - scaledWidth) / 2;
            const y = headerHeight + ((availableHeight - scaledHeight) / 2);

            pdf.addImage(imgData, 'JPEG', x, y, scaledWidth, scaledHeight);

            // Clean header
            pdf.setFontSize(8);
            pdf.setTextColor(0);
            pdf.text(`Complaint ${complaintId} - Complete Graph`, margin, 5);

            updateExportProgress({ current: 95, total: 100, currentPage: 'Saving file...' });

            const fileName = `graph-complete-${complaintId}-${new Date().toISOString().split('T')[0]}.pdf`;
            pdf.save(fileName);

            updateExportProgress({ current: 100, total: 100, currentPage: 'Complete!' });

            // Trigger page reload after PDF is generated and opened in new tab
            setTimeout(() => {
              window.location.reload();
            }, 2000); // Wait 2 seconds to ensure PDF download/opening completes

          } finally {
            // Restore all states in reverse order with proper timing
            restoreContainer();

            // Wait a moment before restoring styles to ensure container is restored
            setTimeout(() => {
              restoreStyles();
            }, 100);

            // Wait a bit more before restoring node state to ensure everything is back to normal
            setTimeout(() => {
              restoreNodeState();
            }, 200);
          }
        })(),
        SINGLE_PAGE_TIMEOUT,
        'Single page export process exceeded time limit'
      );
    } catch (error) {
      if (error instanceof Error && error.message.includes('Timeout')) {
        updateExportProgress({
          current: 100,
          total: 100,
          currentPage: 'Export timed out after 30 seconds',
          timeoutWarning: true
        });
        throw new Error('Export process timed out. The graph may be too complex for single-page export. Try multi-page mode instead.');
      }
      throw error;
    }
  };

  // Enhanced multi-page export with proper tree isolation, layout, and per-page timeout
  const exportMultiPagePDF = async () => {
    const PAGE_TIMEOUT = 30000; // 30 seconds timeout per page

    const [{ default: html2canvas }, { default: jsPDF }] = await Promise.all([
      import('html2canvas'),
      import('jspdf')
    ]);

    const senderNodes = getSenderNodes();
    if (senderNodes.length === 0) {
      throw new Error('No sender nodes found for multi-page export');
    }

    const totalPages = senderNodes.length;
    const skippedPages: string[] = [];
    updateExportProgress({ current: 0, total: totalPages, currentPage: 'Initializing...', skippedPages });

    const reactFlowElement = document.querySelector('.react-flow') as HTMLElement;
    if (!reactFlowElement) {
      throw new Error('Graph container not found');
    }

    // Store original viewport for restoration
    const originalViewport = reactFlowInstance?.getViewport?.() || { x: 0, y: 0, zoom: 1 };

    // Apply initial optimizations
    const restoreNodeState = await temporarilyExpandAllNodes();
    const restoreStyles = optimizeNodesForExport();

    // Prepare container layout (hide sidebar, etc.)
    const { restore: restoreContainerLayout } = await prepareGraphForCapture();

    try {
      const pdf = new jsPDF({
        orientation: 'landscape',
        unit: 'mm',
        format: 'a4'
      });

      const pdfWidth = pdf.internal.pageSize.getWidth();
      const pdfHeight = pdf.internal.pageSize.getHeight();

      // Optimal margins for quality presentation
      const margin = 2;
      const headerHeight = 8;
      const availableWidth = pdfWidth - (2 * margin);
      const availableHeight = pdfHeight - headerHeight - margin;

      let isFirstPage = true;

      for (let i = 0; i < senderNodes.length; i++) {
        const senderNode = senderNodes[i];
        const senderName = senderNode.data?.label || senderNode.data?.account || `Sender ${i + 1}`;

        updateExportProgress({
          current: i + 1,
          total: totalPages,
          currentPage: `Processing ${senderName}...`,
          skippedPages
        });

        try {
          // Wrap each page processing in a timeout
          await withTimeout(
            (async () => {
              // Step 1: Prepare isolated tree with proper layout and bounds calculation
              const {
                // treeNodes,
                // treeEdges,
                restore: restoreTreeIsolation,
                containerWidth: treeContainerWidth,
                containerHeight: treeContainerHeight
              } = await prepareIsolatedTreeForCapture(senderNode);

              // Tree processing completed for sender

              try {
                // Step 2: Set container size for this specific tree
                reactFlowElement.style.width = `${treeContainerWidth}px`;
                reactFlowElement.style.height = `${treeContainerHeight}px`;

                // Step 3: Wait for layout to stabilize
                await new Promise(resolve => setTimeout(resolve, 600));

                // Step 4: Capture with ultra-high quality settings
                const canvas = await html2canvas(reactFlowElement, {
                  backgroundColor: 'white',
                  scale: 4, // Ultra-high scale for maximum quality
                  useCORS: true,
                  allowTaint: true,
                  logging: false,
                  removeContainer: false,
                  foreignObjectRendering: true,
                  imageTimeout: 0,
                  width: treeContainerWidth,
                  height: treeContainerHeight,
                  x: 0,
                  y: 0,
                  scrollX: 0,
                  scrollY: 0,
                  windowWidth: treeContainerWidth,
                  windowHeight: treeContainerHeight
                });

                // Tree canvas capture completed

                // Add new page if not first
                if (!isFirstPage) {
                  pdf.addPage();
                }
                isFirstPage = false;

                // Calculate optimal scaling to maximize page usage
                const scaleX = availableWidth / canvas.width;
                const scaleY = availableHeight / canvas.height;
                const scale = Math.min(scaleX, scaleY);

                const scaledWidth = canvas.width * scale;
                const scaledHeight = canvas.height * scale;

                // Center the image for optimal presentation
                const x = (pdfWidth - scaledWidth) / 2;
                const y = headerHeight + ((availableHeight - scaledHeight) / 2);

                // Use maximum JPEG quality for crisp output
                const imgData = canvas.toDataURL('image/jpeg', 1.0);
                pdf.addImage(imgData, 'JPEG', x, y, scaledWidth, scaledHeight);

                // Clean header with essential information
                pdf.setFontSize(8);
                pdf.setTextColor(0);
                pdf.text(`${senderName} | Complaint ${complaintId} | Page ${i + 1}/${totalPages}`, margin, 5);

              } finally {
                // Step 5: Restore tree isolation for this iteration
                restoreTreeIsolation();
              }
            })(),
            PAGE_TIMEOUT,
            `Page ${i + 1} (${senderName}) processing exceeded time limit`
          );
        } catch (error) {
          // Handle timeout for this specific page
          if (error instanceof Error && error.message.includes('Timeout')) {
            // Page export timed out, skipping
            skippedPages.push(senderName);
            updateExportProgress({
              current: i + 1,
              total: totalPages,
              currentPage: `Skipped ${senderName} (timeout)`,
              skippedPages,
              timeoutWarning: true
            });
            // Continue to next page instead of failing entire export
            continue;
          } else {
            // Re-throw non-timeout errors
            throw error;
          }
        }
      }

      updateExportProgress({
        current: totalPages,
        total: totalPages,
        currentPage: 'Saving PDF...',
        skippedPages
      });

      const fileName = `graph-multipage-${complaintId}-${new Date().toISOString().split('T')[0]}.pdf`;
      pdf.save(fileName);

      // Show completion message with skip information
      const completionMessage = skippedPages.length > 0
        ? `Complete! (${skippedPages.length} pages skipped due to timeout)`
        : 'Complete!';

      updateExportProgress({
        current: totalPages,
        total: totalPages,
        currentPage: completionMessage,
        skippedPages,
        timeoutWarning: skippedPages.length > 0
      });

      // Trigger page reload after PDF is generated and opened in new tab
      setTimeout(() => {
        window.location.reload();
      }, 2000); // Wait 2 seconds to ensure PDF download/opening completes

    } finally {
      // Restore all states in reverse order with proper timing
      restoreContainerLayout();

      // Restore original viewport
      if (reactFlowInstance?.setViewport) {
        reactFlowInstance.setViewport(originalViewport, { duration: 0 });
      }

      setTimeout(() => {
        restoreStyles();
      }, 100);

      setTimeout(() => {
        restoreNodeState();
      }, 200);
    }
  };



  // Main export function
  const exportToPDF = async () => {
    if (nodes.length === 0) {
      alert('No graph data to export');
      return;
    }

    updateExportState(true);
    updateExportProgress({ current: 0, total: 100 });

    try {
      if (exportMode === 'single') {
        await exportSinglePagePDF();
      } else {
        await exportMultiPagePDF();
      }

      // Reset progress after a short delay
      setTimeout(() => {
        updateExportProgress({ current: 0, total: 100 });
        updateExportState(false);
        setShowModeSelector(false);
      }, 1500);

    } catch (error) {
      alert(`Failed to export graph: ${error instanceof Error ? error.message : 'Unknown error'}`);
      updateExportState(false);
      updateExportProgress({ current: 0, total: 100 });
      setShowModeSelector(false);
    }
  };

  return (
    <div className="relative">
      {/* Export Mode Selector */}
      {showModeSelector && !isExporting && (
        <div className="fixed top-16 right-4 p-3 rounded-lg shadow-lg border z-[9999]" style={{
          minWidth: '250px',
          backgroundColor: 'var(--theme-bg-card)',
          color: 'var(--theme-text)',
          borderColor: 'var(--theme-border)'
        }}>
          <div className="text-sm font-medium mb-2">Export Mode</div>

          <div className="space-y-2">
            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                name="exportMode"
                value="single"
                checked={exportMode === 'single'}
                onChange={(e) => setExportMode(e.target.value as ExportMode)}
                className="text-blue-500"
              />
              <div>
                <div className="font-medium">Single Page</div>
                <div className="text-xs opacity-75">Complete graph on one page (30s timeout)</div>
              </div>
            </label>

            <label className="flex items-center gap-2 cursor-pointer">
              <input
                type="radio"
                name="exportMode"
                value="multi"
                checked={exportMode === 'multi'}
                onChange={(e) => setExportMode(e.target.value as ExportMode)}
                className="text-blue-500"
              />
              <div>
                <div className="font-medium">Multi-Page</div>
                <div className="text-xs opacity-75">One page per sender tree ({getSenderNodes().length} pages, 30s per page)</div>
              </div>
            </label>
          </div>

          <div className="flex gap-2 mt-3">
            <button
              onClick={exportToPDF}
              className="flex-1 px-3 py-1.5 rounded text-sm font-medium transition-colors duration-200"
              style={{
                backgroundColor: 'var(--theme-button)',
                color: 'white'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--theme-button-hover)';
                e.currentTarget.style.boxShadow = '0 0 8px var(--theme-glow)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--theme-button)';
                e.currentTarget.style.boxShadow = 'none';
              }}
            >
              Export PDF
            </button>
            <button
              onClick={() => setShowModeSelector(false)}
              className="px-3 py-1.5 rounded text-sm transition-colors duration-200"
              style={{
                backgroundColor: 'var(--theme-bg-primary)',
                color: 'var(--theme-text)',
                border: '1px solid var(--theme-border)'
              }}
              onMouseEnter={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--theme-accent)';
              }}
              onMouseLeave={(e) => {
                e.currentTarget.style.backgroundColor = 'var(--theme-bg-primary)';
              }}
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* Main Export Button */}
      <button
        onClick={() => setShowModeSelector(!showModeSelector)}
        disabled={isExporting || nodes.length === 0}
        className={`p-0.5 rounded transition-colors duration-200 ${
          isExporting || nodes.length === 0
            ? 'opacity-50 cursor-not-allowed'
            : ''
        }`}
        style={{
          backgroundColor: isExporting || nodes.length === 0 ? 'var(--theme-bg-card)' : 'var(--theme-button)',
          color: isExporting || nodes.length === 0 ? 'var(--theme-text)' : 'white'
        }}
        onMouseEnter={(e) => {
          if (!isExporting && nodes.length > 0) {
            e.currentTarget.style.backgroundColor = 'var(--theme-button-hover)';
            e.currentTarget.style.boxShadow = '0 0 8px var(--theme-glow)';
          }
        }}
        onMouseLeave={(e) => {
          if (!isExporting && nodes.length > 0) {
            e.currentTarget.style.backgroundColor = 'var(--theme-button)';
            e.currentTarget.style.boxShadow = 'none';
          }
        }}
        title={isExporting ? 'Exporting...' : 'Export to PDF'}
      >
        {isExporting ? (
          <div className="flex items-center gap-1">
            <svg className="animate-spin h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
              <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
              <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            <span className="text-xs">
              {exportMode === 'multi'
                ? `${exportProgress.current}/${exportProgress.total}`
                : `${Math.round((exportProgress.current / exportProgress.total) * 100)}%`
              }
            </span>
          </div>
        ) : (
          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        )}
      </button>

      {/* Enhanced Progress Indicator with Timeout Information */}
      {isExporting && (
        <div className="fixed top-16 right-4 p-2 rounded shadow-lg border z-[9999]" style={{
          minWidth: '200px',
          backgroundColor: 'var(--theme-bg-card)',
          color: 'var(--theme-text)',
          borderColor: exportProgress.timeoutWarning ? '#f59e0b' : 'var(--theme-border)'
        }}>
          <div className="text-xs font-medium mb-1">
            {exportProgress.currentPage || 'Processing...'}
          </div>
          <div className="w-full h-2 rounded-full overflow-hidden" style={{
            backgroundColor: 'var(--theme-bg-primary)'
          }}>
            <div
              className="h-full transition-all duration-300 ease-out"
              style={{
                width: `${(exportProgress.current / exportProgress.total) * 100}%`,
                backgroundColor: exportProgress.timeoutWarning ? '#f59e0b' : 'var(--theme-button)'
              }}
            />
          </div>
          <div className="text-xs mt-1 opacity-75">
            {exportMode === 'multi'
              ? `Page ${exportProgress.current} of ${exportProgress.total}`
              : `${Math.round((exportProgress.current / exportProgress.total) * 100)}% complete`
            }
          </div>
          {exportProgress.timeoutWarning && (
            <div className="text-xs mt-1 text-yellow-600 dark:text-yellow-400">
              ⚠️ Timeout detected (30s limit)
            </div>
          )}
          {exportProgress.skippedPages && exportProgress.skippedPages.length > 0 && (
            <div className="text-xs mt-1 text-yellow-600 dark:text-yellow-400">
              Skipped: {exportProgress.skippedPages.length} pages
            </div>
          )}
        </div>
      )}
    </div>
  );
};

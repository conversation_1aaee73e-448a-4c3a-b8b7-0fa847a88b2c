/* Edge Animation Styles */

/* Smooth transitions for edge dots */
.edge-dot {
  transition: opacity 0.3s ease-in-out;
}

.edge-dot-small {
  transition: opacity 0.3s ease-in-out;
}

/* Hover effects for edges */
.react-flow__edge-path:hover + .edge-dot {
  opacity: 1 !important;
  r: 4;
}

.react-flow__edge-path:hover + .edge-dot + .edge-dot-small {
  opacity: 0.8 !important;
  r: 3;
}

/* Ensure smooth edge rendering */
.react-flow__edge {
  pointer-events: stroke;
}

.react-flow__edge-path {
  stroke-linecap: round;
  stroke-linejoin: round;
  transition: stroke-width 0.2s ease-in-out;
}

.react-flow__edge-path:hover {
  stroke-width: 3;
}

/* Dark mode specific styles */
.dark-flow .edge-dot {
  filter: drop-shadow(0 0 2px rgba(129, 140, 248, 0.5));
}

.dark-flow .edge-dot-small {
  filter: drop-shadow(0 0 1px rgba(129, 140, 248, 0.3));
}

/* Light mode specific styles */
.light-flow .edge-dot {
  filter: drop-shadow(0 0 2px rgba(96, 165, 250, 0.5));
}

.light-flow .edge-dot-small {
  filter: drop-shadow(0 0 1px rgba(96, 165, 250, 0.3));
}

/* Animation performance optimization */
.edge-dot,
.edge-dot-small {
  will-change: transform;
  transform-origin: center;
}

/* Ensure edges render above background but below nodes */
.react-flow__edge {
  z-index: 1;
}

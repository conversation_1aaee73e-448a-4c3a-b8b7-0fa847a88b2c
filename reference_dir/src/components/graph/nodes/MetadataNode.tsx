import React, { useState } from 'react';
import { NodeProps, <PERSON>le, Position } from 'reactflow';
import { useThemeContext } from '../../../context/TailwindThemeContext';

export const MetadataNode: React.FC<NodeProps> = ({ data }) => {
  const { isDark: isDarkMode } = useThemeContext();
  const [isExpanded, setIsExpanded] = useState(true);

  // Respect global expansion state if provided
  React.useEffect(() => {
    if (data.forceExpanded !== undefined) {
      setIsExpanded(data.forceExpanded);
    }
  }, [data.forceExpanded]);

  // Function to toggle expanded/collapsed state
  const toggleExpanded = (e: React.MouseEvent) => {
    e.stopPropagation();
    setIsExpanded(!isExpanded);
  };

  // Function to toggle individual node collapse (hide/show children)
  const toggleNodeCollapse = (e: React.MouseEvent) => {
    e.stopPropagation();
    if (data.onToggleCollapse) {
      data.onToggleCollapse();
    }
  };

  // Extract label from data or use default
  const nodeLabel = data?.label || "Complaint Information";

  // Format amount for display
  const formatAmount = (amount: string | number | undefined) => {
    if (!amount) return "0";

    // If it's already a number, format it
    if (typeof amount === 'number') {
      return amount.toLocaleString('en-IN');
    }

    // If it's a string, try to parse it as a number
    const cleanAmount = amount.toString().replace(/[^\d.]/g, '');
    const numAmount = parseFloat(cleanAmount);

    if (!isNaN(numAmount)) {
      return numAmount.toLocaleString('en-IN');
    }

    // If parsing fails, return the original string
    return amount;
  };

  return (
    <div
      className={`p-3 rounded-lg border-2 shadow-md w-[350px] ${isDarkMode ? 'bg-indigo-900/30 border-indigo-600' : 'bg-indigo-50 border-indigo-400'}`}
      style={{ cursor: 'move' }}
    >
      {/* Add source handle without specific ID */}
      <Handle
        type="source"
        position={Position.Bottom}
        style={{ background: isDarkMode ? '#818cf8' : '#6366f1', width: '10px', height: '10px' }}
      />

      {/* Header with controls */}
      <div className="flex justify-between items-center mb-2">
        <div
          className={`font-medium text-xl pb-1 border-b ${isDarkMode ? 'border-indigo-700 text-indigo-300' : 'border-indigo-300 text-indigo-800'} flex-grow cursor-pointer`}
          onClick={toggleExpanded}
          title={isExpanded ? "Click to collapse" : "Click to expand"}
        >
          {nodeLabel}
          <span className="ml-2 text-xs">
            {isExpanded ?
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
              </svg>
              :
              <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 inline" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            }
          </span>
        </div>
        {/* Individual Node Collapse/Expand Button */}
        <div className="flex gap-1 ml-2">
          <button
            onClick={toggleNodeCollapse}
            className={`p-0.5 rounded ${isDarkMode ? 'hover:bg-indigo-900/50 text-indigo-400' : 'hover:bg-indigo-100 text-indigo-600'}`}
            title={data.isCollapsed ? "Expand all sender nodes" : "Collapse all sender nodes"}
          >
            {data.isCollapsed ? (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
              </svg>
            ) : (
              <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 12H6" />
              </svg>
            )}
          </button>
        </div>
      </div>

      {/* Always show complaint number and total amount */}
      <div className="flex flex-col gap-2 text-base">
        {(data?.complaint_number || data?.COMPLAINT_NUMBER) && (
          <div className="flex justify-between items-start">
            <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}>Complaint #:</span>
            <span className={`${isDarkMode ? 'text-white' : 'text-gray-800'} text-right break-words text-sm leading-tight max-w-[200px]`}>
              {data.complaint_number || data.COMPLAINT_NUMBER}
            </span>
          </div>
        )}

        {(data?.total_amount || data?.amount || data?.TOTAL_FRAUD_AMOUNT) && (
          <div className="flex justify-between">
            <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Total Amount:</span>
            <span className={`font-medium ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
              ₹{formatAmount(data.total_amount || data.amount || data.TOTAL_FRAUD_AMOUNT)}
            </span>
          </div>
        )}

        {/* Additional details - only show if expanded */}
        {isExpanded && (
          <>
            {(data?.date || data?.date_of_complaint || data?.COMPLAINT_DATE) && (
              <div className="flex justify-between">
                <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} font-medium`}>Date:</span>
                <span className={`${isDarkMode ? 'text-white' : 'text-gray-800'}`}>{data.date || data.date_of_complaint || data.COMPLAINT_DATE}</span>
              </div>
            )}

            {(data?.account || data?.complainant_name || data?.VICTIM_NAME) && (
              <div className="flex justify-between items-start">
                <span className={`${isDarkMode ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}>Complainant:</span>
                <span className={`${isDarkMode ? 'text-white' : 'text-gray-800'} text-right break-words text-sm leading-tight max-w-[200px]`}>
                  {data.complainant_name || data.VICTIM_NAME || data.account}
                </span>
              </div>
            )}
          </>
        )}
      </div>
    </div>
  );
};

import React from 'react';
import { useNavigate } from 'react-router-dom';
import { FiLock, FiMail } from 'react-icons/fi';
import { useAuth } from '../context/AuthContext';
import { useAlert } from '../context/TailwindAlertContext';
import Button from './ui/Button';

interface PaidAccessRequiredProps {
  featureName?: string;
}

const PaidAccessRequired: React.FC<PaidAccessRequiredProps> = ({ featureName = 'This feature' }) => {
  const navigate = useNavigate();
  const { user } = useAuth();
  const { showSuccess } = useAlert();

  const handleContactForRenewal = () => {
    const subject = encodeURIComponent('NCRP Matrix Subscription Renewal Request');
    const body = encodeURIComponent(`Hello,

I would like to renew my NCRP Matrix subscription to continue accessing premium features.

Account Details:
- Email: ${user?.email}
- Name: ${user?.first_name} ${user?.last_name || ''}
- Current Status: Subscription Expired

Please provide me with renewal options and payment details.

Thank you!`);

    const mailtoLink = `mailto:<EMAIL>?subject=${subject}&body=${body}`;
    window.open(mailtoLink, '_blank');
    showSuccess('Email client opened. Please send the email to request subscription renewal.');
  };

  const isExpired = user?.subscription_expires ? new Date(user.subscription_expires) < new Date() : false;

  return (
    <div className="flex justify-center items-center min-h-[60vh] p-4">
      <div className="bg-white/95 dark:bg-gray-800/95 backdrop-blur-sm rounded-lg shadow-lg p-6 max-w-xl w-full text-center border border-gray-100 dark:border-gray-700 animate-fade-in">
        <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-red-100 dark:bg-red-900/30 flex items-center justify-center">
          <FiLock className="w-8 h-8 text-red-600 dark:text-red-400" />
        </div>

        <h1 className="text-xl font-bold mb-3 bg-clip-text text-transparent bg-gradient-to-r from-red-600 to-red-800 dark:from-red-400 dark:to-red-600">
          {isExpired ? 'Subscription Expired' : 'Premium Feature'}
        </h1>

        <p className="text-gray-700 dark:text-gray-300 mb-3">
          {isExpired
            ? 'Your subscription has expired. Please renew to continue accessing premium features.'
            : `${featureName} requires a paid subscription to access.`
          }
        </p>

        {user?.subscription_expires && (
          <p className="text-sm text-gray-500 dark:text-gray-400 mb-3">
            Subscription expired: {new Date(user.subscription_expires).toLocaleDateString()}
          </p>
        )}

        <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">
          Contact support to renew your subscription and unlock all premium features.
        </p>

        <div className="mt-6 flex flex-wrap justify-center gap-3">
          <Button
            variant="primary"
            onClick={handleContactForRenewal}
            className="flex items-center space-x-2"
          >
            <FiMail className="w-4 h-4" />
            <span>Contact for Renewal</span>
          </Button>

          <Button
            variant="outlined"
            onClick={() => navigate('/dashboard')}
          >
            Go to Dashboard
          </Button>
        </div>
      </div>
    </div>
  );
};

export default PaidAccessRequired;

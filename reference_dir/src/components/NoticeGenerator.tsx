import React, { useState } from 'react';
import { FiDownload, FiFileText, FiRefreshCw } from 'react-icons/fi';
import Button from './ui/Button';
import Card from './ui/Card';
import { useAlert } from '../context/TailwindAlertContext';
import authService from '../services/authService';

interface NoticeGeneratorProps {
  complaintId: string;
  complaintNumber?: string;
}

const NoticeGenerator: React.FC<NoticeGeneratorProps> = ({ complaintId, complaintNumber }) => {
  const [isGenerating, setIsGenerating] = useState(false);
  const { showSuccess, showError } = useAlert();

  const handleDownloadNotice = async () => {
    try {
      setIsGenerating(true);

      // Use the secure API client instead of fetch
      const response = await authService.api.get(`/notices/download/${complaintId}`, {
        responseType: 'blob',
        headers: {
          'Accept': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        },
      });

      // Create a download link for the blob
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `Notice_${complaintNumber || complaintId}.docx`);

      // Append to body, click, and remove
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the URL object
      window.URL.revokeObjectURL(url);

      showSuccess('Notice download started');
    } catch (error) {
      showError('Failed to download notice');
    } finally {
      setIsGenerating(false);
    }
  };

  const handleGenerateNewNotice = async () => {
    try {
      setIsGenerating(true);

      // Use the secure API client instead of fetch
      const response = await authService.api.get(`/notices/generate/${complaintId}`, {
        responseType: 'blob',
        headers: {
          'Accept': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        },
      });

      // Create a download link for the blob
      const blob = new Blob([response.data], {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', `Notice_${complaintNumber || complaintId}.docx`);

      // Append to body, click, and remove
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      // Clean up the URL object
      window.URL.revokeObjectURL(url);

      showSuccess('New notice generated and download started');
    } catch (error) {
      showError('Failed to generate notice');
    } finally {
      setIsGenerating(false);
    }
  };

  return (
    <Card className="mb-6">
      <Card.Header>
        <div className="flex items-center">
          <FiFileText className="w-5 h-5 text-blue-500 mr-2" />
          <h2 className="text-lg font-medium">Notice Generation</h2>
        </div>
      </Card.Header>
      <Card.Content>
        <p className="text-sm text-gray-600 dark:text-gray-400 mb-4">
          Generate and download a notice document based on the complaint data. The notice will include transaction details and can be used for official communications.
        </p>

        <div className="flex flex-wrap gap-3">
          <Button
            variant="primary"
            startIcon={<FiDownload />}
            onClick={handleDownloadNotice}
            loading={isGenerating}
            disabled={isGenerating}
          >
            Download Notice
          </Button>

          <Button
            variant="outlined"
            startIcon={<FiRefreshCw />}
            onClick={handleGenerateNewNotice}
            loading={isGenerating}
            disabled={isGenerating}
          >
            Generate New Notice
          </Button>
        </div>
      </Card.Content>
    </Card>
  );
};

export default NoticeGenerator;

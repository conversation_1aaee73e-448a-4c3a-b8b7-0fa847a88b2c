import React from 'react';
import { twMerge } from 'tailwind-merge';
import { FiChevronLeft, FiChevronRight } from 'react-icons/fi';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
  showPageNumbers?: boolean;
  size?: 'sm' | 'md' | 'lg';
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  className,
  showPageNumbers = true,
  size = 'md',
}) => {
  // Don't render pagination if there's only one page
  if (totalPages <= 1) return null;

  // Generate page numbers to display
  const getPageNumbers = () => {
    const pageNumbers: (number | string)[] = [];

    // Always show first page
    pageNumbers.push(1);

    // Calculate range around current page
    let rangeStart = Math.max(2, currentPage - 1);
    let rangeEnd = Math.min(totalPages - 1, currentPage + 1);

    // Add ellipsis after first page if needed
    if (rangeStart > 2) {
      pageNumbers.push('...');
    }

    // Add pages in range
    for (let i = rangeStart; i <= rangeEnd; i++) {
      pageNumbers.push(i);
    }

    // Add ellipsis before last page if needed
    if (rangeEnd < totalPages - 1) {
      pageNumbers.push('...');
    }

    // Always show last page if more than one page
    if (totalPages > 1) {
      pageNumbers.push(totalPages);
    }

    return pageNumbers;
  };

  // Size styles
  const sizeStyles = {
    sm: 'text-xs',
    md: 'text-sm',
    lg: 'text-base',
  };

  // Button size styles
  const buttonSizeStyles = {
    sm: 'h-7 w-7',
    md: 'h-8 w-8',
    lg: 'h-9 w-9',
  };

  return (
    <div className={twMerge('flex items-center justify-center mt-4', className)}>
      {/* Previous page button with cyberpunk styling */}
      <button
        onClick={() => currentPage > 1 && onPageChange(currentPage - 1)}
        disabled={currentPage === 1}
        className={`${buttonSizeStyles[size]} flex items-center justify-center rounded-lg mr-2
          ${currentPage === 1
            ? 'text-gray-400 cursor-not-allowed bg-gray-100/80 dark:bg-[rgba(30,41,59,0.4)] dark:text-gray-600'
            : 'text-gray-700 hover:bg-gray-100/80 dark:text-gray-300 dark:hover:bg-[rgba(0,170,255,0.1)] bg-white/90 dark:bg-[rgba(16,16,30,0.6)] hover:-translate-y-0.5 transform transition-all duration-200 dark:hover:shadow-glow-blue-sm'
          } border border-gray-200/50 dark:border-[rgba(0,170,255,0.2)] transition-all duration-200 backdrop-blur-sm`}
        aria-label="Previous page"
      >
        <FiChevronLeft className="w-4 h-4" />
      </button>

      {/* Page numbers */}
      {showPageNumbers && getPageNumbers().map((page, index) => (
        <React.Fragment key={index}>
          {typeof page === 'number' ? (
            <button
              onClick={() => onPageChange(page)}
              className={`${buttonSizeStyles[size]} flex items-center justify-center rounded-lg mx-1 ${sizeStyles[size]} font-medium
                ${currentPage === page
                  ? 'bg-gradient-blue text-white dark:shadow-glow-blue-sm transform transition-all duration-200'
                  : 'text-gray-700 hover:bg-gray-100/80 dark:text-gray-300 dark:hover:bg-[rgba(0,170,255,0.1)] bg-white/90 dark:bg-[rgba(16,16,30,0.6)] hover:-translate-y-0.5 transform transition-all duration-200'
                } border ${currentPage === page ? 'border-primary-400 dark:border-primary-500' : 'border-gray-200/50 dark:border-[rgba(0,170,255,0.2)]'} transition-all duration-200 backdrop-blur-sm`}
              aria-label={`Page ${page}`}
              aria-current={currentPage === page ? 'page' : undefined}
            >
              {page}
            </button>
          ) : (
            <span className={`${buttonSizeStyles[size]} flex items-center justify-center mx-1 ${sizeStyles[size]} text-gray-500 dark:text-primary-400/50`}>
              {page}
            </span>
          )}
        </React.Fragment>
      ))}

      {/* Next page button with cyberpunk styling */}
      <button
        onClick={() => currentPage < totalPages && onPageChange(currentPage + 1)}
        disabled={currentPage === totalPages}
        className={`${buttonSizeStyles[size]} flex items-center justify-center rounded-lg ml-2
          ${currentPage === totalPages
            ? 'text-gray-400 cursor-not-allowed bg-gray-100/80 dark:bg-[rgba(30,41,59,0.4)] dark:text-gray-600'
            : 'text-gray-700 hover:bg-gray-100/80 dark:text-gray-300 dark:hover:bg-[rgba(0,170,255,0.1)] bg-white/90 dark:bg-[rgba(16,16,30,0.6)] hover:-translate-y-0.5 transform transition-all duration-200 dark:hover:shadow-glow-blue-sm'
          } border border-gray-200/50 dark:border-[rgba(0,170,255,0.2)] transition-all duration-200 backdrop-blur-sm`}
        aria-label="Next page"
      >
        <FiChevronRight className="w-4 h-4" />
      </button>
    </div>
  );
};

export default Pagination;

import React from 'react';
import { twMerge } from 'tailwind-merge';
import Button from './Button';
import { FiX } from 'react-icons/fi';

interface DialogProps {
  open: boolean;
  onClose: () => void;
  children: React.ReactNode;
  className?: string;
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
}

export const Dialog: React.FC<DialogProps> = ({
  open,
  onClose,
  children,
  className,
  maxWidth = 'md',
  ...props
}) => {
  if (!open) return null;

  // Handle backdrop click
  const handleBackdropClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  // Handle escape key press
  React.useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose();
      }
    };

    document.addEventListener('keydown', handleEscape);
    return () => {
      document.removeEventListener('keydown', handleEscape);
    };
  }, [onClose]);

  // Prevent body scrolling when dialog is open
  React.useEffect(() => {
    if (open) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = '';
    }

    return () => {
      document.body.style.overflow = '';
    };
  }, [open]);

  // Max width classes
  const maxWidthClasses = {
    xs: 'max-w-xs',
    sm: 'max-w-sm',
    md: 'max-w-md',
    lg: 'max-w-lg',
    xl: 'max-w-xl',
    '2xl': 'max-w-2xl',
    full: 'max-w-full',
  };

  return (
    <div
      className="fixed inset-0 z-50 bg-black/70 backdrop-blur-md animate-fade-in overflow-y-auto"
      onClick={handleBackdropClick}
      style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        padding: '1rem',
        minHeight: '100vh'
      }}
    >
      <div
        className={twMerge(
          'bg-white/90 dark:bg-[rgba(16,16,30,0.8)] backdrop-blur-lg rounded-xl shadow-xl dark:shadow-glow-blue-sm w-full border border-gray-100/50 dark:border-[rgba(0,170,255,0.3)] animate-slide-up relative',
          maxWidthClasses[maxWidth],
          className
        )}
        style={{
          display: 'flex',
          flexDirection: 'column',
          maxHeight: '90vh',
          overflow: 'hidden'
        }}
        {...props}
      >
        {/* Subtle scan line effect in dark mode */}
        <div className="absolute inset-0 overflow-hidden rounded-xl opacity-0 dark:opacity-10 pointer-events-none">
          <div className="w-full h-full animate-scan-line bg-gradient-to-b from-transparent via-primary-400/20 to-transparent"></div>
        </div>

        {children}
      </div>
    </div>
  );
};

interface DialogTitleProps {
  children: React.ReactNode;
  className?: string;
  onClose?: () => void;
}

export const DialogTitle: React.FC<DialogTitleProps> = ({
  children,
  className,
  onClose,
  ...props
}) => {
  return (
    <div
      className={twMerge(
        'flex items-center justify-between px-6 py-4 border-b border-gray-200/30 dark:border-[rgba(0,170,255,0.2)] bg-gray-50/50 dark:bg-[rgba(16,16,30,0.5)]',
        className
      )}
      {...props}
    >
      <h2 className="text-xl font-bold text-gray-900 dark:text-white dark:cyber-text-gradient">{children}</h2>
      {onClose && (
        <button
          onClick={onClose}
          className="p-1.5 rounded-full text-gray-600 hover:text-gray-800 dark:text-primary-400 dark:hover:text-primary-300 bg-gray-100 hover:bg-gray-200 dark:bg-[rgba(0,170,255,0.1)] dark:hover:bg-[rgba(0,170,255,0.2)] focus:outline-none transition-all duration-200 transform hover:scale-110 dark:hover:shadow-glow-blue-sm"
        >
          <FiX className="w-4 h-4" />
        </button>
      )}
    </div>
  );
};

interface DialogContentProps {
  children: React.ReactNode;
  className?: string;
}

export const DialogContent: React.FC<DialogContentProps> = ({
  children,
  className,
  ...props
}) => {
  return (
    <div
      className={twMerge('px-6 py-4 flex-1 overflow-y-auto', className)}
      style={{
        display: 'flex',
        flexDirection: 'column'
      }}
      {...props}
    >
      {children}
    </div>
  );
};

interface DialogActionsProps {
  children: React.ReactNode;
  className?: string;
}

export const DialogActions: React.FC<DialogActionsProps> = ({
  children,
  className,
  ...props
}) => {
  return (
    <div
      className={twMerge(
        'flex flex-col sm:flex-row sm:justify-end gap-2 sm:gap-3 px-6 py-4 border-t border-gray-200/30 dark:border-[rgba(0,170,255,0.2)] bg-gray-50/50 dark:bg-[rgba(16,16,30,0.5)]',
        className
      )}
      {...props}
    >
      {children}
    </div>
  );
};

interface ConfirmDialogProps {
  open: boolean;
  onClose: () => void;
  onConfirm: () => void;
  title: React.ReactNode;
  content: React.ReactNode;
  confirmText?: string;
  cancelText?: string;
  confirmVariant?: 'primary' | 'danger';
  maxWidth?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | 'full';
  loading?: boolean;
  isLoading?: boolean;
  disableActions?: boolean;
}

// Using React.memo to fix the "Expected static flag was missing" error
export const ConfirmDialog = React.memo(({
  open,
  onClose,
  onConfirm,
  title,
  content,
  confirmText = 'Confirm',
  cancelText = 'Cancel',
  confirmVariant = 'primary',
  maxWidth = 'sm',
  loading = false,
  isLoading = false, // New prop
  disableActions = false, // New prop
}: ConfirmDialogProps) => {
  // Use either loading or isLoading for backward compatibility
  const isButtonLoading = loading || isLoading;
  const areButtonsDisabled = disableActions || isButtonLoading;

  return (
    <Dialog open={open} onClose={onClose} maxWidth={maxWidth}>
      <DialogTitle onClose={!areButtonsDisabled ? onClose : undefined}>{title}</DialogTitle>
      <DialogContent>{content}</DialogContent>
      <DialogActions>
        <Button variant="outlined" onClick={onClose} disabled={areButtonsDisabled}>
          {cancelText}
        </Button>
        <Button variant={confirmVariant} onClick={onConfirm} loading={isButtonLoading}>
          {confirmText}
        </Button>
      </DialogActions>
    </Dialog>
  );
});

// Add display name to help with debugging
ConfirmDialog.displayName = 'ConfirmDialog';

export default Object.assign(Dialog, {
  Title: DialogTitle,
  Content: DialogContent,
  Actions: DialogActions,
});

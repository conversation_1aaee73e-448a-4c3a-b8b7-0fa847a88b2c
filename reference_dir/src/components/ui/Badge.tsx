import React from 'react';
import { twMerge } from 'tailwind-merge';

export interface BadgeProps {
  children: React.ReactNode;
  variant?: 'default' | 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'info';
  size?: 'sm' | 'md' | 'lg';
  rounded?: boolean;
  className?: string;
}

const Badge: React.FC<BadgeProps> = ({
  children,
  variant = 'default',
  size = 'md',
  rounded = false,
  className,
  ...props
}) => {
  // Base styles with cyberpunk aesthetics
  const baseStyles = 'inline-flex items-center font-medium transition-all duration-300 border backdrop-blur-sm';

  // Size styles
  const sizeStyles = {
    sm: 'px-2.5 py-1 text-xs',
    md: 'px-3 py-1.5 text-sm',
    lg: 'px-4 py-2 text-base',
  };

  // Variant styles with cyberpunk-inspired colors
  const variantStyles = {
    default: 'bg-gray-100/80 text-gray-800 border-gray-200 dark:bg-[rgba(30,41,59,0.4)] dark:text-gray-300 dark:border-[rgba(0,170,255,0.3)]',

    primary: 'bg-primary-100/80 text-primary-800 border-primary-200 dark:bg-[rgba(0,170,255,0.15)] dark:text-primary-300 dark:border-[rgba(0,170,255,0.4)]',

    secondary: 'bg-secondary-100/80 text-secondary-800 border-secondary-200 dark:bg-[rgba(255,0,255,0.15)] dark:text-secondary-300 dark:border-[rgba(255,0,255,0.4)]',

    success: 'bg-success-100/80 text-success-800 border-success-200 dark:bg-[rgba(0,255,136,0.15)] dark:text-success-300 dark:border-[rgba(0,255,136,0.4)]',

    warning: 'bg-warning-100/80 text-warning-800 border-warning-200 dark:bg-[rgba(255,153,0,0.15)] dark:text-warning-300 dark:border-[rgba(255,153,0,0.4)]',

    danger: 'bg-error-100/80 text-error-800 border-error-200 dark:bg-[rgba(255,0,102,0.15)] dark:text-error-300 dark:border-[rgba(255,0,102,0.4)]',

    info: 'bg-info-100/80 text-info-800 border-info-200 dark:bg-[rgba(0,221,255,0.15)] dark:text-info-300 dark:border-[rgba(0,221,255,0.4)]',
  };

  // Border radius
  const roundedStyles = rounded ? 'rounded-full' : 'rounded';

  // Combine all styles
  const badgeStyles = twMerge(
    baseStyles,
    sizeStyles[size],
    variantStyles[variant],
    roundedStyles,
    className
  );

  return (
    <span className={badgeStyles} {...props}>
      {children}
    </span>
  );
};

export default Badge;

import React, { ButtonHTMLAttributes, forwardRef } from 'react';
import { twMerge } from 'tailwind-merge';

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outlined' | 'text' | 'danger' | 'success';
  size?: 'sm' | 'md' | 'lg';
  fullWidth?: boolean;
  loading?: boolean;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
}

const Button = forwardRef<HTMLButtonElement, ButtonProps>(
  (
    {
      children,
      className,
      variant = 'primary',
      size = 'md',
      fullWidth = false,
      disabled = false,
      loading = false,
      startIcon,
      endIcon,
      ...props
    },
    ref
  ) => {
    // Base styles - enhanced with cyberpunk aesthetics
    const baseStyles = 'inline-flex items-center justify-center rounded-md font-medium transition-all duration-200 focus:outline-none transform hover:scale-[1.02] active:scale-[0.98] shadow-md hover:-translate-y-0.5 relative overflow-hidden';

    // Size styles
    const sizeStyles = {
      sm: 'px-3 py-1.5 text-sm',
      md: 'px-4 py-2 text-base',
      lg: 'px-6 py-3 text-lg',
    };

    // Variant styles with theme colors
    const variantStyles = {
      // Primary button
      primary: 'text-white focus:ring-2 disabled:opacity-70',

      // Secondary button
      secondary: 'text-white focus:ring-2 disabled:opacity-70',

      // Outlined button
      outlined: 'border-2 bg-transparent focus:ring-2 hover:shadow-sm',

      // Text button with minimal styling
      text: 'bg-transparent shadow-none hover:shadow-none',

      // Danger button
      danger: 'text-white focus:ring-2 disabled:opacity-70',

      // Success button
      success: 'text-white focus:ring-2 disabled:opacity-70',
    };

    // Width styles
    const widthStyles = fullWidth ? 'w-full' : '';

    // Disabled styles
    const disabledStyles = disabled ? 'cursor-not-allowed opacity-60' : '';

    // Loading state
    const loadingState = loading ? 'relative text-transparent transition-none hover:text-transparent' : '';

    // Combine all styles
    const buttonStyles = twMerge(
      baseStyles,
      sizeStyles[size],
      variantStyles[variant],
      widthStyles,
      disabledStyles,
      loadingState,
      className
    );

    // Get theme-specific styles
    const getThemeStyles = () => {
      switch (variant) {
        case 'primary':
          return {
            backgroundColor: 'var(--theme-button)',
            borderColor: 'var(--theme-button)',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1), 0 0 8px var(--theme-glow)'
          };
        case 'secondary':
          return {
            backgroundColor: 'var(--theme-bg-card)',
            color: 'var(--theme-text)',
            borderColor: 'var(--theme-border)',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1)'
          };
        case 'outlined':
          return {
            color: 'var(--theme-text)',
            borderColor: 'var(--theme-border)',
            boxShadow: '0 2px 4px rgba(0, 0, 0, 0.05)'
          };
        case 'text':
          return {
            color: 'var(--theme-text)'
          };
        case 'danger':
          return {
            backgroundColor: 'var(--theme-error-color)',
            borderColor: 'var(--theme-error-color)',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1), 0 0 8px rgba(244, 67, 54, 0.3)'
          };
        case 'success':
          return {
            backgroundColor: 'var(--theme-success-color)',
            borderColor: 'var(--theme-success-color)',
            boxShadow: '0 4px 6px rgba(0, 0, 0, 0.1), 0 0 8px rgba(76, 175, 80, 0.3)'
          };
        default:
          return {};
      }
    };

    return (
      <button
        ref={ref}
        className={buttonStyles}
        style={getThemeStyles()}
        disabled={disabled || loading}
        {...props}
      >
        {startIcon && <span className="mr-2">{startIcon}</span>}
        {children}
        {endIcon && <span className="ml-2">{endIcon}</span>}

        {loading && (
          <div className="absolute inset-0 flex items-center justify-center">
            <svg
              className="h-5 w-5 animate-spin text-white"
              style={{
                color: (variant === 'outlined' || variant === 'text') ? 'var(--theme-accent)' : 'white'
              }}
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 24 24"
            >
              <circle
                className="opacity-20"
                cx="12"
                cy="12"
                r="10"
                stroke="currentColor"
                strokeWidth="3"
              />
              <path
                className="opacity-90"
                fill="currentColor"
                d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
              />
            </svg>

            {/* Subtle glow effect behind spinner */}
            <div className="absolute inset-0 flex items-center justify-center opacity-70">
              <div className="w-8 h-8 rounded-full shadow-glow-sm" style={{ boxShadow: `0 0 10px var(--theme-glow)` }}></div>
            </div>
          </div>
        )}
      </button>
    );
  }
);

Button.displayName = 'Button';

export default Button;

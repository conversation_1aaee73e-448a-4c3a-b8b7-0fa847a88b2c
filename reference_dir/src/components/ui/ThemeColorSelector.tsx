import React from 'react';
import { useThemeContext } from '../../context/TailwindThemeContext';
import { twMerge } from 'tailwind-merge';
import { FiCheck } from 'react-icons/fi';

interface ThemeColorSelectorProps {
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showLabels?: boolean;
}

const ThemeColorSelector: React.FC<ThemeColorSelectorProps> = ({
  className,
  size = 'md',
  showLabels = true,
}) => {
  const { themeColor, setThemeColor } = useThemeContext();

  const handleThemeChange = (colorId: string) => {
    setThemeColor(colorId as any);
  };

  // Size styles
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-10 h-10',
  };

  // Theme color options - Matrix theme removed
  const themeColors = [
    // Dark cyberpunk theme (improved from Matrix)
    { id: 'darkmode', name: 'Dark Cyberpunk', color: '#00aaff', bgColor: '#0a0a0f', textColor: '#e8f4ff' },
    { id: 'mint', name: 'Mint Green', color: '#d8efe0', bgColor: '#a8d9bc', textColor: '#333333' },

    // Light themes
    { id: 'lightblue', name: 'Light Blue', color: '#d6e8f5', bgColor: '#a8cce6', textColor: '#333333' },
    { id: 'lightgray', name: 'Light Gray', color: '#e6e6e6', bgColor: '#d9d9d9', textColor: '#333333' },
    { id: 'orange', name: 'Orange', color: '#fed7aa', bgColor: '#fb923c', textColor: '#333333' },
  ];

  return (
    <div className={twMerge('flex flex-col space-y-2', className)}>
      {showLabels && (
        <div className="text-sm font-medium mb-1" style={{ color: 'var(--theme-text)' }}>
          Theme Color
        </div>
      )}
      <div className="flex flex-wrap gap-2">
        {themeColors.map((color) => (
          <button
            key={color.id}
            onClick={() => handleThemeChange(color.id)}
            className={twMerge(
              'rounded-full transition-all duration-200 flex items-center justify-center',
              'border-2 hover:scale-110 relative',
              sizeClasses[size],
              themeColor === color.id ? 'ring-2 ring-offset-2 ring-opacity-50' : ''
            )}
            style={{
              backgroundColor: color.bgColor,
              borderColor: color.color,
              boxShadow: themeColor === color.id ? `0 0 0 2px ${color.color}` : 'none',
            }}
            title={color.name}
            aria-label={`Set theme color to ${color.name}`}
          >
            {themeColor === color.id && (
              <FiCheck
                className="text-gray-700"
                style={{ color: color.textColor }}
                size={size === 'sm' ? 12 : size === 'md' ? 16 : 20}
              />
            )}
          </button>
        ))}
      </div>


    </div>
  );
};

export default ThemeColorSelector;

import React, { forwardRef, InputHTMLAttributes } from 'react';
import { twMerge } from 'tailwind-merge';

export interface TextFieldProps extends InputHTMLAttributes<HTMLInputElement> {
  label?: string;
  helperText?: string;
  error?: boolean;
  fullWidth?: boolean;
  startAdornment?: React.ReactNode;
  endAdornment?: React.ReactNode;
  startIcon?: React.ReactNode;
  endIcon?: React.ReactNode;
  variant?: 'outlined' | 'filled' | 'standard';
}

const TextField = forwardRef<HTMLInputElement, TextFieldProps>(
  (
    {
      label,
      helperText,
      error = false,
      fullWidth = false,
      className,
      startAdornment,
      endAdornment,
      startIcon,
      endIcon,
      variant = 'outlined',
      disabled = false,
      required = false,
      ...props
    },
    ref
  ) => {
    // Base container styles
    const containerStyles = twMerge(
      'relative',
      fullWidth ? 'w-full' : '',
      className
    );

    // Base input styles with cyberpunk aesthetics
    const baseInputStyles = 'block w-full text-sm focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 transition-all duration-300 flex items-center';

    // Variant-specific styles with cyberpunk aesthetics
    const variantStyles = {
      outlined: 'border rounded-lg shadow-sm focus:ring-2 focus:ring-offset-0 dark:backdrop-blur-md',
      filled: 'border-0 border-b-2 bg-gray-100/80 dark:bg-[rgba(30,41,59,0.4)] focus:ring-0 dark:backdrop-blur-md',
      standard: 'border-0 border-b-2 focus:ring-0 dark:backdrop-blur-sm',
    };

    // Error and color styles with cyberpunk aesthetics
    const colorStyles = error
      ? 'border-error-500 focus:border-error-500 focus:ring-error-500 dark:border-error-400 dark:focus:border-error-400 dark:focus:ring-error-400 dark:shadow-glow-red-sm'
      : 'border-gray-300 focus:border-primary-500 focus:ring-primary-500 dark:border-[rgba(0,170,255,0.3)] dark:focus:border-primary-400 dark:focus:ring-primary-400 dark:focus:shadow-glow-blue-sm';

    // Padding based on adornments - using CSS variable for customization
    const iconSpacing = 'var(--input-icon-spacing, 10px)';
    const paddingStyles = (startAdornment || startIcon) && (endAdornment || endIcon)
      ? `pl-${iconSpacing} pr-4`
      : startAdornment || startIcon
      ? `pl-${iconSpacing}`
      : endAdornment || endIcon
      ? `pr-4`
      : '';

    // Combine input styles
    const inputStyles = twMerge(
      baseInputStyles,
      variantStyles[variant],
      colorStyles,
      paddingStyles,
      variant === 'outlined' ? 'px-4 py-2' : 'px-0 py-2',
      'bg-white/90 dark:bg-[rgba(16,16,30,0.6)] text-gray-900 dark:text-white placeholder:text-gray-500 dark:placeholder:text-gray-300 placeholder:font-normal placeholder:align-middle'
    );

    // Label styles with cyberpunk aesthetics
    const labelStyles = twMerge(
      'block text-sm font-medium mb-1.5',
      error
        ? 'text-error-500 dark:text-error-400 dark:drop-shadow-red'
        : 'text-gray-700 dark:text-gray-200 dark:drop-shadow-text-dark',
      disabled ? 'opacity-50' : ''
    );

    // Helper text styles with cyberpunk aesthetics
    const helperTextStyles = twMerge(
      'mt-1.5 text-xs',
      error
        ? 'text-error-500 dark:text-error-400 dark:drop-shadow-red'
        : 'text-gray-500 dark:text-gray-400 dark:drop-shadow-text-dark'
    );

    return (
      <div className={containerStyles}>
        {label && (
          <label htmlFor={props.id} className={labelStyles}>
            {label}
            {required && <span className="ml-1 text-red-500">*</span>}
          </label>
        )}

        <div className="relative">
          {(startAdornment || startIcon) && (
            <div className="absolute top-1/2 -translate-y-1/2 left-0 flex items-center pl-2.5 pointer-events-none text-gray-500 dark:text-gray-400 z-10" style={{ paddingLeft: 'calc(var(--input-icon-spacing, 10px) / 4)' }}>
              <span className="flex items-center justify-center w-4 h-4 transition-all duration-300">
                {startAdornment || startIcon}
              </span>
            </div>
          )}

          <input
            ref={ref}
            className={inputStyles}
            disabled={disabled}
            aria-invalid={error ? 'true' : 'false'}
            style={{
              lineHeight: '1.5',
              verticalAlign: 'middle',
              paddingLeft: startAdornment || startIcon ? 'var(--input-icon-spacing, 10px)' : undefined
            }}
            {...props}
          />

          {(endAdornment || endIcon) && (
            <div className="absolute top-1/2 -translate-y-1/2 right-0 flex items-center pr-0.5 pointer-events-none text-gray-500 dark:text-gray-400">
              <span className="flex items-center justify-center w-4 h-4 transition-all duration-300">
                {endAdornment || endIcon}
              </span>
            </div>
          )}
        </div>

        {helperText && <p className={helperTextStyles}>{helperText}</p>}
      </div>
    );
  }
);

TextField.displayName = 'TextField';

export default TextField;

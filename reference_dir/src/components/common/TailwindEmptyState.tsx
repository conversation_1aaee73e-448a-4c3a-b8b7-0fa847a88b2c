import React from 'react';
import Button from '../ui/Button';

interface EmptyStateProps {
  title: string;
  description: string;
  icon?: React.ReactNode;
  action?: {
    label: string;
    onClick: () => void;
  };
}

const TailwindEmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  icon,
  action
}) => {
  return (
    <div className="flex flex-col items-center justify-center text-center py-8 px-4 animate-fade-in">
      {icon && (
        <div className="mb-4 text-gray-400 dark:text-gray-500">
          {React.cloneElement(icon as React.ReactElement, {
            className: 'w-16 h-16'
          } as any)}
        </div>
      )}

      <h2 className="text-xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-gray-900 to-gray-700 dark:from-white dark:to-gray-300">
        {title}
      </h2>

      <p className="text-gray-500 dark:text-gray-400 max-w-md mb-6">
        {description}
      </p>

      {action && (
        <Button
          variant="primary"
          onClick={action.onClick}
        >
          {action.label}
        </Button>
      )}
    </div>
  );
};

export default TailwindEmptyState;

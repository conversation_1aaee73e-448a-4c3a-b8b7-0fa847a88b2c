import React, { useEffect, useState } from 'react';
import { useThemeContext } from '../../context/TailwindThemeContext';
import logoImage from '../../assets/logo.webp';

interface LoadingScreenProps {
  message?: string;
  duration?: number; // Duration in milliseconds
  onComplete?: () => void;
}

const LoadingScreen: React.FC<LoadingScreenProps> = ({
  message = "Loading NCRP Matrix...",
  duration = 4500,
  onComplete
}) => {
  const { isDark } = useThemeContext();
  const [progress, setProgress] = useState(0);
  const [currentMessage, setCurrentMessage] = useState(message);

  const loadingMessages = [
    "Initializing NCRP Matrix...",
    "Loading security protocols...",
    "Establishing secure connection...",
    "Configuring user workspace...",
    "Loading dashboard components...",
    "Preparing data visualization...",
    "Finalizing setup...",
    "Almost ready..."
  ];

  useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => {
        const newProgress = prev + (100 / (duration / 100));
        if (newProgress >= 100) {
          clearInterval(interval);
          setTimeout(() => {
            onComplete?.();
          }, 300);
          return 100;
        }
        return newProgress;
      });
    }, 100);

    // Update loading messages
    const messageInterval = setInterval(() => {
      setCurrentMessage(prev => {
        const currentIndex = loadingMessages.indexOf(prev);
        const nextIndex = (currentIndex + 1) % loadingMessages.length;
        return loadingMessages[nextIndex];
      });
    }, duration / loadingMessages.length);

    return () => {
      clearInterval(interval);
      clearInterval(messageInterval);
    };
  }, [duration, onComplete]);

  return (
    <div className={`fixed inset-0 z-50 flex items-center justify-center ${
      isDark
        ? 'bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900'
        : 'bg-gradient-to-br from-blue-50 via-white to-purple-50'
    }`}>
      {/* Animated background particles */}
      <div className="absolute inset-0 overflow-hidden">
        {[...Array(20)].map((_, i) => (
          <div
            key={i}
            className={`absolute w-2 h-2 rounded-full animate-pulse ${
              isDark ? 'bg-blue-400/20' : 'bg-blue-500/10'
            }`}
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
              animationDelay: `${Math.random() * 2}s`,
              animationDuration: `${2 + Math.random() * 3}s`
            }}
          />
        ))}
      </div>

      {/* Main loading content */}
      <div className="relative z-10 text-center">
        {/* Logo with animation */}
        <div className="mb-8 flex justify-center">
          <div className="relative">
            {/* Outer rotating ring */}
            <div className={`absolute inset-0 w-40 h-40 rounded-full border-4 border-transparent ${
              isDark
                ? 'border-t-blue-400 border-r-purple-400'
                : 'border-t-blue-500 border-r-purple-500'
            } animate-spin`} />

            {/* Inner pulsing ring */}
            <div className={`absolute inset-2 w-36 h-36 rounded-full border-2 border-transparent ${
              isDark
                ? 'border-b-green-400 border-l-cyan-400'
                : 'border-b-green-500 border-l-cyan-500'
            } animate-spin animate-reverse`} style={{ animationDuration: '3s' }} />

            {/* Logo container */}
            <div className="relative w-40 h-40 flex items-center justify-center">
              <div className={`w-28 h-28 rounded-full flex items-center justify-center ${
                isDark
                  ? 'bg-gray-800/80 backdrop-blur-sm'
                  : 'bg-white/80 backdrop-blur-sm'
              } shadow-2xl border-2 ${
                isDark ? 'border-blue-400/20' : 'border-blue-500/20'
              }`}>
                <img
                  src={logoImage}
                  alt="NCRP Matrix Logo"
                  className="w-20 h-20 object-contain rounded-full animate-pulse"
                  style={{ animationDuration: '2s' }}
                />
              </div>
            </div>
          </div>
        </div>

        {/* App title */}
        <h1 className={`text-4xl font-bold mb-4 ${
          isDark
            ? 'text-transparent bg-clip-text bg-gradient-to-r from-blue-400 to-purple-400'
            : 'text-transparent bg-clip-text bg-gradient-to-r from-blue-600 to-purple-600'
        }`}>
          NCRP Matrix
        </h1>

        {/* Subtitle */}
        <p className={`text-lg mb-8 ${
          isDark ? 'text-gray-300' : 'text-gray-600'
        }`}>
          NCRP Complaint Visualisation & Analysis
        </p>

        {/* Loading message */}
        <div className="mb-6">
          <p className={`text-sm font-medium ${
            isDark ? 'text-blue-300' : 'text-blue-600'
          } animate-pulse`}>
            {currentMessage}
          </p>
        </div>

        {/* Progress bar */}
        <div className="w-80 mx-auto">
          <div className={`w-full h-2 rounded-full ${
            isDark ? 'bg-gray-700' : 'bg-gray-200'
          } overflow-hidden`}>
            <div
              className={`h-full rounded-full transition-all duration-300 ease-out ${
                isDark
                  ? 'bg-gradient-to-r from-blue-400 to-purple-400'
                  : 'bg-gradient-to-r from-blue-500 to-purple-500'
              }`}
              style={{ width: `${progress}%` }}
            />
          </div>
          <div className={`text-xs mt-2 ${
            isDark ? 'text-gray-400' : 'text-gray-500'
          }`}>
            {Math.round(progress)}%
          </div>
        </div>

        {/* Loading dots */}
        <div className="flex justify-center space-x-2 mt-6">
          {[...Array(3)].map((_, i) => (
            <div
              key={i}
              className={`w-2 h-2 rounded-full ${
                isDark ? 'bg-blue-400' : 'bg-blue-500'
              } animate-bounce`}
              style={{ animationDelay: `${i * 0.2}s` }}
            />
          ))}
        </div>
      </div>
    </div>
  );
};

export default LoadingScreen;

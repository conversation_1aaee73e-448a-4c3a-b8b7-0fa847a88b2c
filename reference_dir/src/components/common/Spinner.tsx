import React from 'react';
import { twMerge } from 'tailwind-merge';

interface SpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  color?: 'primary' | 'secondary' | 'success' | 'error' | 'warning' | 'info';
  className?: string;
}

const Spinner: React.FC<SpinnerProps> = ({
  size = 'md',
  color = 'primary',
  className,
}) => {
  // Size styles
  const sizeStyles = {
    sm: 'h-4 w-4',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
  };

  // Color styles
  const colorStyles = {
    primary: 'text-primary-500 dark:text-primary-400',
    secondary: 'text-purple-500 dark:text-purple-400',
    success: 'text-green-500 dark:text-green-400',
    error: 'text-red-500 dark:text-red-400',
    warning: 'text-amber-500 dark:text-amber-400',
    info: 'text-blue-500 dark:text-blue-400',
  };

  // Shadow styles for dark mode
  const shadowStyles = {
    primary: 'dark:drop-shadow-blue',
    secondary: 'dark:drop-shadow-purple',
    success: 'dark:drop-shadow-green',
    error: 'dark:drop-shadow-red',
    warning: 'dark:drop-shadow-amber',
    info: 'dark:drop-shadow-blue',
  };

  return (
    <div className="relative">
      <svg
        className={twMerge(
          'animate-spin',
          sizeStyles[size],
          colorStyles[color],
          shadowStyles[color],
          className
        )}
        xmlns="http://www.w3.org/2000/svg"
        fill="none"
        viewBox="0 0 24 24"
      >
        <circle
          className="opacity-25"
          cx="12"
          cy="12"
          r="10"
          stroke="currentColor"
          strokeWidth="4"
        />
        <path
          className="opacity-75"
          fill="currentColor"
          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
        />
      </svg>
      
      {/* Subtle glow effect in dark mode */}
      <div className="absolute inset-0 flex items-center justify-center dark:opacity-70 opacity-0 pointer-events-none">
        <div className={`${sizeStyles[size]} rounded-full ${
          color === 'primary' ? 'dark:shadow-glow-blue-sm' :
          color === 'secondary' ? 'dark:shadow-glow-purple-sm' :
          color === 'success' ? 'dark:shadow-glow-green-sm' :
          color === 'error' ? 'dark:shadow-glow-red-sm' :
          color === 'warning' ? 'dark:shadow-glow-amber-sm' :
          'dark:shadow-glow-blue-sm'
        }`}></div>
      </div>
    </div>
  );
};

export default Spinner;

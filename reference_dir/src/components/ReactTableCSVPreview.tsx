import React, { useState, useMemo, useEffect, useCallback } from 'react';
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  createColumnHelper,
  VisibilityState,
} from '@tanstack/react-table';
import { FiEdit2, FiTrash2, FiCheck, FiX, FiDownload } from 'react-icons/fi';
import Card from './ui/Card';
import Button from './ui/Button';
import TextField from './ui/TextField';

// Define the row type
interface CSVRow {
  id: string;
  [key: string]: any;
}

// Define the metadata for row operations
interface RowOperationMetadata {
  action: 'update' | 'delete';
  rowId: string;
  updatedRow?: any;
  deletedRow?: any;
  updatedRows: any[];
  headers: string[];
  isSingleRowOperation?: boolean;
  complaintMetadata?: any;
}

// Define the props for the component
interface ReactTableCSVPreviewProps {
  csvData: string | null;
  onSave?: (data: string, metadata?: RowOperationMetadata) => Promise<any> | void;
  onDownload?: () => void;
  isLoading?: boolean;
  hiddenColumns?: string[];
  onTotalsCalculated?: (totalHold: number, totalWithdrawal: number) => void;
  complaintMetadata?: any;
}

const ReactTableCSVPreview: React.FC<ReactTableCSVPreviewProps> = ({
  csvData,
  onSave,
  onDownload,
  isLoading: externalLoading = false,
  hiddenColumns = [],
  onTotalsCalculated,
  complaintMetadata
}) => {
  // State for the component
  const [rows, setRows] = useState<CSVRow[]>([]);
  const [headers, setHeaders] = useState<string[]>([]);
  const [editableRow, setEditableRow] = useState<string | null>(null);
  const [editedValues, setEditedValues] = useState<Record<string, any>>({});
  const [editingCell, setEditingCell] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [internalLoading, setInternalLoading] = useState(false);
  const [columnVisibility, setColumnVisibility] = useState<VisibilityState>({});
  const [minAmount, setMinAmount] = useState<number>(0);
  const [maxAmount, setMaxAmount] = useState<number>(0);
  const [amountFilterType, setAmountFilterType] = useState<'none' | 'range'>('none');

  // Combined loading state (either external or internal)
  const isLoading = externalLoading || internalLoading;

  // Helper function to parse CSV lines
  const parseCSVLine = useCallback((line: string): string[] => {
    const result: string[] = [];
    let inQuotes = false;
    let currentValue = '';

    for (let i = 0; i < line.length; i++) {
      const char = line[i];

      if (char === '"') {
        inQuotes = !inQuotes;
      } else if (char === ',' && !inQuotes) {
        result.push(currentValue.trim());
        currentValue = '';
      } else {
        currentValue += char;
      }
    }

    // Add the last value
    result.push(currentValue.trim());
    return result;
  }, []);

  // Helper function to parse CSV data and update rows
  const parseAndUpdateRows = useCallback((csvData: string) => {
    if (!csvData) return;

    try {
      const lines = csvData.split('\n').filter(line => line.trim() !== '');
      if (lines.length === 0) return;

      // Process the header line
      let headerLine = lines[0];

      // Parse the headers
      const headerValues = parseCSVLine(headerLine);

      // Ensure all headers are unique and valid
      const newHeaders = headerValues.map((header, index) => {
        // If header is empty or duplicate, generate a placeholder
        if (!header || headerValues.indexOf(header) !== index) {
          return `Column_${index + 1}`;
        }
        return header;
      });

      // Parse the data rows
      const newRows = lines.slice(1).map((line, index) => {
        const values = parseCSVLine(line);
        const row: CSVRow = { id: `row-${index}` };

        // Ensure all headers have a value (empty string if not provided)
        newHeaders.forEach((header, i) => {
          row[header] = i < values.length ? values[i] : '';
        });

        return row;
      });

      // Debug logging removed for production

      // Update the headers and rows with the new data
      setHeaders(newHeaders);
      setRows(newRows);
    } catch (e) {
      // Error logging handled by error boundary
    }
  }, [parseCSVLine]);

  // Convert rows back to CSV format
  const rowsToCSV = useCallback((rowsToConvert: CSVRow[], headersToUse: string[]): string => {
    // Start with the headers
    let csv = headersToUse.join(',') + '\n';

    // Add each row
    rowsToConvert.forEach(row => {
      const rowValues = headersToUse.map(header => {
        // Get the value for this header
        const value = row[header];

        // If the value contains a comma, quote it
        if (value && String(value).includes(',')) {
          return `"${value}"`;
        }

        return value || '';
      });

      csv += rowValues.join(',') + '\n';
    });

    return csv;
  }, []);

  // Parse CSV data when it changes with chunked processing
  useEffect(() => {
    if (!csvData) {
      setRows([]);
      setHeaders([]);
      return;
    }

    // Set loading state to true when starting to process data
    setInternalLoading(true);

    // Process large CSV data in chunks to prevent browser freeze
    const processLargeCSV = async (data: string) => {
      try {
        // Clean the CSV data - remove any non-printable characters
        const cleanedCsvData = data.replace(/[\x00-\x09\x0B\x0C\x0E-\x1F\x7F]/g, '');

        // Check if data is too large (>1MB) and needs chunked processing
        if (cleanedCsvData.length > 1024 * 1024) {
          // Process in smaller chunks with delays
          const lines = cleanedCsvData.split('\n');
          const chunkSize = 100; // Process 100 lines at a time

          for (let i = 0; i < lines.length; i += chunkSize) {
            const chunk = lines.slice(i, i + chunkSize).join('\n');

            // Process chunk
            if (i === 0) {
              // First chunk contains headers
              parseAndUpdateRows(chunk);
            } else {
              // Subsequent chunks - append to existing data
              parseAndUpdateRows(cleanedCsvData.split('\n').slice(0, i + chunkSize).join('\n'));
            }

            // Yield control to prevent blocking
            await new Promise(resolve => setTimeout(resolve, 10));
          }
        } else {
          // Small data - process normally
          parseAndUpdateRows(cleanedCsvData);
        }

        setInternalLoading(false);
      } catch (error) {
        setInternalLoading(false);
      }
    };

    // Use requestIdleCallback if available, otherwise setTimeout
    if ('requestIdleCallback' in window) {
      const idleCallback = window.requestIdleCallback(() => {
        processLargeCSV(csvData);
      });

      return () => {
        window.cancelIdleCallback(idleCallback);
      };
    } else {
      const processTask = setTimeout(() => {
        processLargeCSV(csvData);
      }, 0);

      return () => {
        clearTimeout(processTask);
      };
    }
  }, [csvData, parseAndUpdateRows]);

  // Set up column visibility based on hiddenColumns prop
  useEffect(() => {
    if (headers.length > 0 && hiddenColumns.length > 0) {
      const newVisibility: VisibilityState = {};

      // Set visibility for each header
      headers.forEach(header => {
        // If the header is in hiddenColumns, set visibility to false
        newVisibility[header] = !hiddenColumns.includes(header);
      });

      setColumnVisibility(newVisibility);
    }
  }, [headers, hiddenColumns]);

  // Helper function to parse amount from string
  const parseAmount = (amountStr: string): number => {
    if (!amountStr) return 0;
    // Remove currency symbols, commas, and spaces, then parse as float
    const cleanAmount = String(amountStr).replace(/[₹,\s]/g, '').trim();
    return parseFloat(cleanAmount) || 0;
  };

  // Filter rows based on search term and amount filters
  const filteredRows = useMemo(() => {
    let filtered = rows;

    // Apply search term filter
    if (searchTerm) {
      const searchTermLower = searchTerm.toLowerCase();
      // Only search in these important columns if they exist
      const searchableColumns = ['Txn ID', 'Sender Account', 'Receiver Account', 'Amount', 'Txn Type'];

      filtered = filtered.filter(row => {
        // First check important columns
        for (const col of searchableColumns) {
          if (row[col] && String(row[col]).toLowerCase().includes(searchTermLower)) {
            return true;
          }
        }

        // If not found in important columns, check all other columns
        return Object.entries(row).some(([key, value]) => {
          // Skip id and already checked columns
          if (key === 'id' || searchableColumns.includes(key)) return false;
          return String(value).toLowerCase().includes(searchTermLower);
        });
      });
    }

    // Apply amount filter if enabled
    if (amountFilterType === 'range' && (minAmount > 0 || maxAmount > 0)) {
      filtered = filtered.filter(row => {
        const amount = parseAmount(String(row['Amount'] || '0'));

        // Check minimum amount
        if (minAmount > 0 && amount < minAmount) {
          return false;
        }

        // Check maximum amount
        if (maxAmount > 0 && amount > maxAmount) {
          return false;
        }

        return true;
      });
    }

    return filtered;
  }, [rows, searchTerm, amountFilterType, minAmount, maxAmount]);

  // Calculate total amount on hold and total withdrawal
  const { totalHold, totalWithdrawal } = useMemo(() => {
    // Use reduce for a single pass through the data
    return rows.reduce((totals, row) => {
      const txnType = String(row['Txn Type'] || '').toLowerCase();
      const amountStr = String(row['Amount'] || '0');
      // Extract numeric value from amount string
      const amount = parseFloat(amountStr.replace(/[^\d.]/g, '')) || 0;

      if (txnType.includes('hold')) {
        totals.totalHold += amount;
      } else if (txnType.includes('withdrawal') || txnType.includes('atm')) {
        totals.totalWithdrawal += amount;
      }

      return totals;
    }, { totalHold: 0, totalWithdrawal: 0 });
  }, [rows]);

  // Call the callback with totals when they change
  useEffect(() => {
    if (onTotalsCalculated) {
      onTotalsCalculated(totalHold, totalWithdrawal);
    }
  }, [totalHold, totalWithdrawal, onTotalsCalculated]);

  // Handler for editing a row
  const handleEditRow = useCallback((rowId: string) => {
    // Edit action initiated

    // Find the row to edit
    const rowToEdit = rows.find(row => row.id === rowId);
    if (!rowToEdit) {
      // Row not found for editing
      return;
    }

    // Row set to edit mode

    // Set the editable row
    setEditableRow(rowId);

    // Reset edited values
    setEditedValues({});
  }, [rows]);

  // Handler for canceling an edit
  const handleCancelEdit = useCallback(() => {
    // Cancelling edit mode
    // Reset the editable row and edited values
    setEditableRow(null);
    setEditedValues({});
    setEditingCell(null);
  }, [editableRow]);

  // Handler for saving a row
  const handleSaveRow = useCallback((rowId: string) => {
    // Save operation initiated

    // Find the row to update
    const rowIndex = rows.findIndex(row => row.id === rowId);
    if (rowIndex === -1) {
      // Row not found for saving
      return;
    }

    // Create a copy of the rows
    const updatedRows = [...rows];

    // Update the row with the edited values
    updatedRows[rowIndex] = {
      ...updatedRows[rowIndex],
      ...editedValues
    };

    // Create a copy of the original rows for rollback if needed
    const originalRows = [...rows];

    // Update the rows state optimistically
    setRows(updatedRows);

    // Reset the editable row and edited values
    setEditableRow(null);
    setEditedValues({});
    setEditingCell(null);

    // Set loading state
    setInternalLoading(true);

    // Convert the updated rows to CSV
    const updatedCSV = rowsToCSV(updatedRows, headers);

    // Call the onSave callback with the updated CSV and metadata
    if (onSave) {
      // Calling save callback for update

      // Create metadata for the operation
      const metadata: RowOperationMetadata = {
        action: 'update',
        rowId,
        updatedRow: updatedRows[rowIndex],
        updatedRows,
        headers,
        isSingleRowOperation: true,
        complaintMetadata
      };

      // Metadata prepared for save operation

      // Call the onSave callback
      const saveResult = onSave(updatedCSV, metadata);
      // Save callback completed

      // Handle Promise if returned
      if (saveResult && typeof saveResult.then === 'function') {
        saveResult.then((response: any) => {
          // Save operation successful
          // Check if the response contains updated CSV data
          if (response && response.csv_data_base64) {
            try {
              const decodedCsv = atob(response.csv_data_base64);
              // Updated CSV data received from server

              // Use the helper function to parse and update rows
              parseAndUpdateRows(decodedCsv);
            } catch (e) {
              // Error decoding CSV data from response
            }
          } else if (response && response.data && response.data.csv_data_base64) {
            try {
              const decodedCsv = atob(response.data.csv_data_base64);
              // Updated CSV data received from server.data

              // Use the helper function to parse and update rows
              parseAndUpdateRows(decodedCsv);
            } catch (e) {
              // Error decoding CSV data from response.data
            }
          } else {
            // If no updated data is returned, keep the optimistic update
            // No updated CSV data in response, keeping optimistic update
            // Keep the updated rows as is - don't revert to original
          }

          // Clear loading state after a short delay to ensure smooth UI
          setTimeout(() => {
            setInternalLoading(false);
          }, 500);
        }).catch(() => {
          // Revert to original rows on error
          setRows(originalRows);
          setInternalLoading(false);
        });
      } else {
        // If no promise is returned, clear loading state
        setInternalLoading(false);
      }
    }
  }, [rows, headers, editedValues, rowsToCSV, onSave, complaintMetadata, parseAndUpdateRows]);

  // Handler for deleting a row
  const handleDeleteRow = useCallback((rowId: string) => {
    // Find the row to delete
    const rowIndex = rows.findIndex(row => row.id === rowId);
    if (rowIndex === -1) {
      return;
    }

    // Create a copy of the rows
    const updatedRows = [...rows];

    // Store the deleted row for metadata
    const deletedRow = updatedRows[rowIndex];

    // Remove the row
    updatedRows.splice(rowIndex, 1);

    // Create a copy of the original rows for rollback if needed
    const originalRows = [...rows];

    // Update the rows state optimistically
    setRows(updatedRows);

    // Set loading state
    setInternalLoading(true);

    // Convert the updated rows to CSV
    const updatedCSV = rowsToCSV(updatedRows, headers);

    // Call the onSave callback with the updated CSV and metadata
    if (onSave) {
      // Calling save callback for delete operation

      // Create metadata for the operation
      const metadata: RowOperationMetadata = {
        action: 'delete',
        rowId,
        deletedRow,
        updatedRows,
        headers,
        isSingleRowOperation: true,
        complaintMetadata
      };

      // Metadata prepared for delete operation

      // Call the onSave callback
      const saveResult = onSave(updatedCSV, metadata);
      // Delete callback completed

      // Handle Promise if returned
      if (saveResult && typeof saveResult.then === 'function') {
        saveResult.then((response: any) => {
          // Delete operation successful
          // Check if the response contains updated CSV data
          if (response && response.csv_data_base64) {
            try {
              const decodedCsv = atob(response.csv_data_base64);
              // Updated CSV data received from server after delete

              // Use the helper function to parse and update rows
              parseAndUpdateRows(decodedCsv);
            } catch (e) {
              // Error decoding CSV data from response after delete
            }
          } else if (response && response.data && response.data.csv_data_base64) {
            try {
              const decodedCsv = atob(response.data.csv_data_base64);
              // Updated CSV data received from server.data after delete

              // Use the helper function to parse and update rows
              parseAndUpdateRows(decodedCsv);
            } catch (e) {
              // Error decoding CSV data from response.data after delete
            }
          } else {
            // If no updated data is returned, keep the optimistic update
            // No updated CSV data in response after delete, keeping optimistic update
            // Keep the updated rows as is - don't revert to original
          }

          // Clear loading state after a short delay to ensure smooth UI
          setTimeout(() => {
            setInternalLoading(false);
          }, 500);
        }).catch(() => {
          // Revert to original rows on error
          setRows(originalRows);
          setInternalLoading(false);
        });
      } else {
        // If no promise is returned, clear loading state
        setInternalLoading(false);
      }
    }
  }, [rows, headers, rowsToCSV, onSave, complaintMetadata, parseAndUpdateRows]);

  // Create columns for the table
  const columns = useMemo(() => {
    if (headers.length === 0) return [];

    const columnHelper = createColumnHelper<CSVRow>();

    // Create a map of transaction types to color classes for faster lookup
    const txnTypeColorMap = {
      'money transfer to': 'bg-amber-500/20 text-gray-900 dark:text-gray-900',
      'upi': 'bg-blue-500/20 text-gray-900 dark:text-gray-900',
      'atm': 'bg-red-500/20 text-gray-900 dark:text-gray-900',
      'withdrawal': 'bg-red-500/20 text-gray-900 dark:text-gray-900',
      'hold': 'bg-green-500/20 text-gray-900 dark:text-gray-900',
      'others': 'bg-gray-500/20 text-gray-900 dark:text-gray-900'
    };

    const dataColumns = headers.map(header => {
      // Remove all hardcoded column sizes for flexible layout
      return columnHelper.accessor(header, {
        header: () => header,
        cell: ({ row, getValue }) => {
          const value = getValue();
          const isEditing = editableRow === row.original.id;

          if (isEditing) {
            const cellId = `${row.original.id}-${header}`;
            const isCurrentlyEditing = editingCell === cellId;

            return (
              <input
                type="text"
                value={editedValues[header] !== undefined ? editedValues[header] : value}
                onChange={(e) => setEditedValues({
                  ...editedValues,
                  [header]: e.target.value
                })}
                onFocus={() => setEditingCell(cellId)}
                autoFocus={isCurrentlyEditing}
                className="w-full p-1 border border-gray-300 dark:border-gray-700 rounded bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
              />
            );
          }

          // Apply special styling for transaction type cells
          if (header === 'Txn Type' && value) {
            const valueLower = String(value).toLowerCase();
            let colorClass = '';

            // Check for each transaction type and apply the appropriate color
            for (const [type, className] of Object.entries(txnTypeColorMap)) {
              if (valueLower.includes(type)) {
                colorClass = className;
                break;
              }
            }

            if (colorClass) {
              return (
                <span className={`px-2 py-1 rounded-full ${colorClass}`}>
                  {value}
                </span>
              );
            }
          }

          // Default rendering for non-edited, non-special cells
          return <span>{value}</span>;
        }
      });
    });

    // Add action column for edit/delete buttons
    const actionColumn = columnHelper.display({
      id: 'actions',
      header: () => 'Actions',
      // Remove hardcoded size for actions column
      cell: ({ row }) => {
        const isEditing = editableRow === row.original.id;

        if (isEditing) {
          return (
            <div className="flex space-x-2">
              <button
                onClick={() => handleSaveRow(row.original.id)}
                className="p-1 text-green-600 hover:text-green-800 dark:text-green-400 dark:hover:text-green-300"
                title="Save"
              >
                <FiCheck size={16} />
              </button>
              <button
                onClick={() => handleCancelEdit()}
                className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                title="Cancel"
              >
                <FiX size={16} />
              </button>
            </div>
          );
        }

        return (
          <div className="flex flex-row justify-center gap-2">
            <button
              onClick={() => handleEditRow(row.original.id)}
              className="p-1 text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
              title="Edit"
              disabled={isLoading}
            >
              <FiEdit2 size={14} />
            </button>
            <button
              onClick={() => handleDeleteRow(row.original.id)}
              className="p-1 text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
              title="Delete"
              disabled={isLoading}
            >
              <FiTrash2 size={14} />
            </button>
          </div>
        );
      }
    });

    // Return all columns with the action column at the end
    return [...dataColumns, actionColumn];
  }, [headers, editableRow, editedValues, editingCell, isLoading, handleSaveRow, handleCancelEdit, handleEditRow, handleDeleteRow]);

  // Set up the table
  const table = useReactTable({
    data: filteredRows,
    columns,
    getCoreRowModel: getCoreRowModel(),
    state: {
      columnVisibility,
    },
  });

  // Render the component
  return (
    <Card className="w-full h-full overflow-hidden">
      {isLoading && (
        <div className="absolute inset-0 bg-white/50 dark:bg-black/50 z-50 flex items-center justify-center">
          <div className="flex flex-col items-center">
            <div className="w-12 h-12 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-300">Processing data...</p>
          </div>
        </div>
      )}
      <Card.Header className="flex flex-col gap-4 p-4">
        {/* Search and Amount Filter Row */}
        <div className="flex flex-col md:flex-row gap-4 items-start md:items-center">
          <div className="flex items-center">
            <TextField
              placeholder="Search transactions..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full max-w-md"
            />
          </div>

          {/* Amount Filter Controls */}
          <div className="flex items-center gap-3 flex-wrap">
            <span className="text-sm font-medium text-gray-700 dark:text-gray-300">Amount Filter:</span>

            {/* Radio buttons */}
            <div className="flex items-center gap-4">
              <label className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300">
                <input
                  type="radio"
                  name="amountFilter"
                  checked={amountFilterType === 'none'}
                  onChange={() => setAmountFilterType('none')}
                  className="text-blue-600 focus:ring-blue-500"
                />
                None
              </label>

              <label className="flex items-center gap-2 text-sm text-gray-700 dark:text-gray-300">
                <input
                  type="radio"
                  name="amountFilter"
                  checked={amountFilterType === 'range'}
                  onChange={() => setAmountFilterType('range')}
                  className="text-blue-600 focus:ring-blue-500"
                />
                Range
              </label>
            </div>

            {amountFilterType === 'range' && (
              <>
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Min:</label>
                  <input
                    type="number"
                    placeholder="0"
                    value={minAmount || ''}
                    onChange={(e) => setMinAmount(Number(e.target.value) || 0)}
                    className="w-24 px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <div className="flex items-center gap-2">
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300">Max:</label>
                  <input
                    type="number"
                    placeholder="∞"
                    value={maxAmount || ''}
                    onChange={(e) => setMaxAmount(Number(e.target.value) || 0)}
                    className="w-24 px-3 py-1 text-sm border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                  />
                </div>
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => {
                    setMinAmount(0);
                    setMaxAmount(0);
                    setAmountFilterType('none');
                  }}
                  className="px-3 py-1 text-sm"
                >
                  Clear
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Transaction Summary and Download */}
        <div className="flex flex-col md:flex-row items-center justify-between gap-4 w-full">
          <div className="flex items-center gap-4">
            <div className="flex flex-col items-center px-6 py-2 rounded-md bg-green-100/30 dark:bg-green-900/30 border border-green-200 dark:border-green-800/50 min-w-[180px]">
              <span className="text-xs font-medium text-green-700 dark:text-green-400">Total Amount on Hold</span>
              <span className="text-sm font-bold text-green-800 dark:text-green-300">₹{totalHold.toLocaleString('en-IN', { maximumFractionDigits: 2 })}</span>
            </div>
            <div className="flex flex-col items-center px-6 py-2 rounded-md bg-red-100/30 dark:bg-red-900/30 border border-red-200 dark:border-red-800/50 min-w-[180px]">
              <span className="text-xs font-medium text-red-700 dark:text-red-400">Total Withdrawal</span>
              <span className="text-sm font-bold text-red-800 dark:text-red-300">₹{totalWithdrawal.toLocaleString('en-IN', { maximumFractionDigits: 2 })}</span>
            </div>
            {/* Show filtered count if filters are active */}
            {(searchTerm || amountFilterType === 'range') && (
              <div className="flex flex-col items-center px-4 py-2 rounded-md bg-blue-100/30 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-800/50">
                <span className="text-xs font-medium text-blue-700 dark:text-blue-400">Filtered Results</span>
                <span className="text-sm font-bold text-blue-800 dark:text-blue-300">{filteredRows.length} of {rows.length}</span>
              </div>
            )}
          </div>

          <div className="flex gap-2">
            {onDownload && (
              <Button
                variant="primary"
                size="sm"
                startIcon={<FiDownload />}
                onClick={onDownload}
                disabled={isLoading || !csvData}
                className="px-2 py-1 text-xs font-medium"
              >
                Download Excel
              </Button>
            )}
          </div>
        </div>
      </Card.Header>
      <Card.Content className="p-0 border-0 overflow-auto">
        <div className="overflow-x-auto w-full">
          {headers.length > 0 ? (
            <>
              <style>
                {`
                  .csv-preview-table {
                    width: 100%;
                    table-layout: auto;
                    border-collapse: collapse;
                    border: 1px solid #e5e7eb;
                    font-size: 0.875rem;
                  }
                  .dark .csv-preview-table {
                    border-color: #374151;
                  }
                  .csv-preview-table th,
                  .csv-preview-table td {
                    padding: 8px;
                    border: 1px solid #e5e7eb;
                    text-align: left;
                    vertical-align: middle;
                    line-height: 1.4;
                    box-sizing: border-box;
                    height: 40px;
                    position: relative;
                    word-break: break-word;
                    overflow-wrap: break-word;
                  }
                  .dark .csv-preview-table th,
                  .dark .csv-preview-table td {
                    border-color: #374151;
                  }
                  .csv-preview-table th {
                    background-color: #f3f4f6;
                    font-weight: 600;
                    color: #4b5563;
                    white-space: nowrap;
                    position: sticky;
                    top: 0;
                    z-index: 10;
                  }
                  .dark .csv-preview-table th {
                    background-color: #1f2937;
                    color: #d1d5db;
                  }
                  .csv-preview-table tbody tr:hover {
                    background-color: #f3f4f6;
                  }
                  .dark .csv-preview-table tbody tr:hover {
                    background-color: #1f2937;
                  }
                  /* Remove specific column widths for Reference, Layer, Amount, Actions */
                  .csv-column-Reference,
                  .csv-column-Layer,
                  .csv-column-Amount,
                  .csv-column-actions {
                    width: auto !important;
                    min-width: 0;
                  }
                  @media (max-width: 768px) {
                    .csv-preview-table {
                      display: block;
                      overflow-x: auto;
                      white-space: nowrap;
                    }
                    .csv-preview-table thead,
                    .csv-preview-table tbody,
                    .csv-preview-table th,
                    .csv-preview-table td,
                    .csv-preview-table tr {
                      display: table-cell;
                    }
                    .csv-preview-table th {
                      display: table-cell;
                    }
                    .csv-preview-table tr {
                      display: table-row;
                    }
                  }
                `}
              </style>
              <table className="csv-preview-table">
                <thead>
                  {table.getHeaderGroups().map(headerGroup => (
                    <tr key={headerGroup.id}>
                      {headerGroup.headers.map(header => (
                        <th key={header.id} className={`csv-column-${header.id}`}>
                          {flexRender(
                            header.column.columnDef.header,
                            header.getContext()
                          )}
                        </th>
                      ))}
                    </tr>
                  ))}
                </thead>
                <tbody>
                  {table.getRowModel().rows.length > 0 ? (
                    table.getRowModel().rows.map(row => (
                      <tr key={row.id}>
                        {row.getVisibleCells().map(cell => (
                          <td key={cell.id} className={`csv-column-${cell.column.id}`}>
                            {flexRender(
                              cell.column.columnDef.cell,
                              cell.getContext()
                            )}
                          </td>
                        ))}
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={columns.length} className="py-4 text-center text-gray-500 dark:text-gray-400">
                        No data available
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </>
          ) : (
            <div className="flex justify-center items-center h-64">
              <p className="text-gray-500 dark:text-gray-400">No data available</p>
            </div>
          )}
        </div>
      </Card.Content>
    </Card>
  );
};

export default ReactTableCSVPreview;

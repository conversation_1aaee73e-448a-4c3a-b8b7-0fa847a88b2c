/**
 * Debug Utilities for Frontend Development
 * 
 * Provides debugging helpers for development mode with TypeScript support.
 * These utilities are designed to be lightweight and only active in development.
 */

import logger from './logger';

// Debug configuration
interface DebugConfig {
  enabled: boolean;
  logLevel: 'debug' | 'info' | 'warn' | 'error';
  showTimestamps: boolean;
  showStackTrace: boolean;
}

// Get debug configuration from environment
const getDebugConfig = (): DebugConfig => ({
  enabled: import.meta.env.MODE === 'development' || import.meta.env.VITE_DEBUG_LOGS === 'true',
  logLevel: 'debug',
  showTimestamps: true,
  showStackTrace: false
});

const debugConfig = getDebugConfig();

/**
 * Enhanced debug logging with context
 */
export const debugLog = (message: string, data?: any, context?: string): void => {
  if (!debugConfig.enabled) return;

  const timestamp = debugConfig.showTimestamps ? `[${new Date().toLocaleTimeString()}]` : '';
  const contextStr = context ? `[${context}]` : '';
  const prefix = `${timestamp}${contextStr} DEBUG:`.trim();

  if (data !== undefined) {
    logger.debug(`${prefix} ${message}`, data);
  } else {
    logger.debug(`${prefix} ${message}`);
  }
};

/**
 * Debug API calls with request/response logging
 */
export const debugApiCall = (
  method: string,
  url: string,
  requestData?: any,
  responseData?: any,
  error?: any
): void => {
  if (!debugConfig.enabled) return;

  const context = 'API';
  
  if (error) {
    debugLog(`${method.toUpperCase()} ${url} - ERROR`, {
      request: requestData,
      error: error.message || error,
      status: error.response?.status,
      statusText: error.response?.statusText
    }, context);
  } else {
    debugLog(`${method.toUpperCase()} ${url} - SUCCESS`, {
      request: requestData,
      response: responseData
    }, context);
  }
};

/**
 * Debug React Query mutations
 */
export const debugMutation = (
  mutationName: string,
  variables?: any,
  result?: any,
  error?: any
): void => {
  if (!debugConfig.enabled) return;

  const context = 'MUTATION';
  
  if (error) {
    debugLog(`${mutationName} - ERROR`, {
      variables,
      error: error.message || error
    }, context);
  } else {
    debugLog(`${mutationName} - SUCCESS`, {
      variables,
      result
    }, context);
  }
};

/**
 * Debug React Query queries
 */
export const debugQuery = (
  queryKey: any,
  result?: any,
  error?: any,
  isLoading?: boolean
): void => {
  if (!debugConfig.enabled) return;

  const context = 'QUERY';
  const keyStr = Array.isArray(queryKey) ? queryKey.join(' > ') : String(queryKey);
  
  if (error) {
    debugLog(`Query [${keyStr}] - ERROR`, {
      error: error.message || error
    }, context);
  } else if (isLoading) {
    debugLog(`Query [${keyStr}] - LOADING`, undefined, context);
  } else {
    debugLog(`Query [${keyStr}] - SUCCESS`, {
      dataType: typeof result,
      hasData: !!result
    }, context);
  }
};

/**
 * Debug component lifecycle events
 */
export const debugComponent = (
  componentName: string,
  event: 'mount' | 'unmount' | 'update' | 'render',
  props?: any,
  state?: any
): void => {
  if (!debugConfig.enabled) return;

  const context = 'COMPONENT';
  
  debugLog(`${componentName} - ${event.toUpperCase()}`, {
    props: props ? Object.keys(props) : undefined,
    state: state ? Object.keys(state) : undefined
  }, context);
};

/**
 * Debug form interactions
 */
export const debugForm = (
  formName: string,
  action: 'submit' | 'validate' | 'change' | 'error',
  data?: any,
  errors?: any
): void => {
  if (!debugConfig.enabled) return;

  const context = 'FORM';
  
  debugLog(`${formName} - ${action.toUpperCase()}`, {
    data,
    errors,
    hasErrors: errors && Object.keys(errors).length > 0
  }, context);
};

/**
 * Debug navigation events
 */
export const debugNavigation = (
  from: string,
  to: string,
  params?: any
): void => {
  if (!debugConfig.enabled) return;

  const context = 'NAVIGATION';
  
  debugLog(`Navigate: ${from} → ${to}`, {
    params
  }, context);
};

/**
 * Debug authentication events
 */
export const debugAuth = (
  action: 'login' | 'logout' | 'refresh' | 'verify' | 'error',
  data?: any,
  error?: any
): void => {
  if (!debugConfig.enabled) return;

  const context = 'AUTH';
  
  if (error) {
    debugLog(`Auth ${action.toUpperCase()} - ERROR`, {
      error: error.message || error,
      data
    }, context);
  } else {
    debugLog(`Auth ${action.toUpperCase()}`, {
      data: data ? { ...data, password: '[REDACTED]', token: '[REDACTED]' } : undefined
    }, context);
  }
};

/**
 * Debug performance measurements
 */
export const debugPerformance = (
  operation: string,
  startTime: number,
  endTime?: number,
  metadata?: any
): void => {
  if (!debugConfig.enabled) return;

  const context = 'PERFORMANCE';
  const duration = endTime ? endTime - startTime : Date.now() - startTime;
  
  debugLog(`${operation} - ${duration}ms`, {
    duration,
    metadata
  }, context);
};

/**
 * Create a performance timer
 */
export const createPerformanceTimer = (operation: string) => {
  if (!debugConfig.enabled) {
    return {
      end: () => {},
      mark: () => {}
    };
  }

  const startTime = Date.now();
  
  return {
    end: (metadata?: any) => {
      debugPerformance(operation, startTime, Date.now(), metadata);
    },
    mark: (label: string, metadata?: any) => {
      debugPerformance(`${operation} - ${label}`, startTime, Date.now(), metadata);
    }
  };
};

/**
 * Debug error with stack trace
 */
export const debugError = (
  error: Error | any,
  context?: string,
  additionalData?: any
): void => {
  if (!debugConfig.enabled) return;

  const contextStr = context || 'ERROR';
  
  logger.error(`[${contextStr}] ${error.message || error}`, {
    error: error.message || error,
    stack: debugConfig.showStackTrace ? error.stack : undefined,
    additionalData
  });
};

/**
 * Debug table for complex data structures
 */
export const debugTable = (
  label: string,
  data: any[],
  columns?: string[]
): void => {
  if (!debugConfig.enabled || !Array.isArray(data)) return;

  console.group(`🔍 ${label}`);
  
  if (columns) {
    console.table(data, columns);
  } else {
    console.table(data);
  }
  
  console.groupEnd();
};

/**
 * Debug memory usage (if available)
 */
export const debugMemory = (label?: string): void => {
  if (!debugConfig.enabled) return;

  // @ts-ignore - performance.memory is not in all browsers
  if (typeof performance !== 'undefined' && performance.memory) {
    const memory = (performance as any).memory;
    debugLog(`Memory Usage${label ? ` - ${label}` : ''}`, {
      used: `${Math.round(memory.usedJSHeapSize / 1024 / 1024)} MB`,
      total: `${Math.round(memory.totalJSHeapSize / 1024 / 1024)} MB`,
      limit: `${Math.round(memory.jsHeapSizeLimit / 1024 / 1024)} MB`
    }, 'MEMORY');
  }
};

/**
 * Conditional debug logging
 */
export const debugIf = (
  condition: boolean,
  message: string,
  data?: any,
  context?: string
): void => {
  if (condition) {
    debugLog(message, data, context);
  }
};

/**
 * Debug group for organizing related logs
 */
export const debugGroup = (
  label: string,
  callback: () => void,
  collapsed: boolean = false
): void => {
  if (!debugConfig.enabled) {
    callback();
    return;
  }

  if (collapsed) {
    console.groupCollapsed(`🔍 ${label}`);
  } else {
    console.group(`🔍 ${label}`);
  }
  
  try {
    callback();
  } finally {
    console.groupEnd();
  }
};

/**
 * Export debug configuration for external use
 */
export const getDebugStatus = (): DebugConfig => debugConfig;

/**
 * Enable/disable debugging at runtime
 */
export const setDebugEnabled = (enabled: boolean): void => {
  debugConfig.enabled = enabled;
};

// Export all debug utilities
export default {
  debugLog,
  debugApiCall,
  debugMutation,
  debugQuery,
  debugComponent,
  debugForm,
  debugNavigation,
  debugAuth,
  debugPerformance,
  createPerformanceTimer,
  debugError,
  debugTable,
  debugMemory,
  debugIf,
  debugGroup,
  getDebugStatus,
  setDebugEnabled
};

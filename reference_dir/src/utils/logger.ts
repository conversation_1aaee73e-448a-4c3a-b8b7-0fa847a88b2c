/**
 * Frontend Logger Utility
 *
 * Provides structured logging with different levels and environment-based configuration.
 * Supports both console logging and optional remote logging for production environments.
 */

// Log levels enum
export enum LogLevel {
  ERROR = 0,
  WARN = 1,
  INFO = 2,
  DEBUG = 3,
}

// Log entry interface
interface LogEntry {
  timestamp: string;
  level: string;
  message: string;
  data?: any;
  context?: string;
  userId?: string;
  sessionId?: string;
}

// Logger configuration interface
interface LoggerConfig {
  level: LogLevel;
  enableConsole: boolean;
  enableRemote: boolean;
  remoteEndpoint?: string;
  context?: string;
}

class Logger {
  private config: LoggerConfig;
  private logBuffer: LogEntry[] = [];
  private maxBufferSize = 100;
  private flushInterval = 30000; // 30 seconds
  private flushTimer?: number;

  constructor() {
    // Initialize configuration from environment variables
    this.config = {
      level: this.getLogLevelFromEnv(),
      enableConsole: true,
      enableRemote: import.meta.env.VITE_REMOTE_LOGGING === 'true',
      remoteEndpoint: import.meta.env.VITE_REMOTE_LOG_ENDPOINT || '/api/logs',
      context: 'frontend'
    };

    // Start flush timer for remote logging
    if (this.config.enableRemote) {
      this.startFlushTimer();
    }

    // Handle page unload to flush remaining logs
    if (typeof window !== 'undefined') {
      window.addEventListener('beforeunload', () => {
        this.flush();
      });
    }
  }

  /**
   * Get log level from environment variables
   */
  private getLogLevelFromEnv(): LogLevel {
    const debugLogs = import.meta.env.VITE_DEBUG_LOGS === 'true';
    const isDevelopment = import.meta.env.MODE === 'development';
    const isProduction = import.meta.env.MODE === 'production';

    // In production, be more restrictive with logging
    if (isProduction) {
      // Only allow debug logs if explicitly enabled in production
      if (debugLogs) {
        console.warn('🚨 Debug logs are enabled in production - this may impact performance');
        return LogLevel.DEBUG;
      }
      // Default to INFO level in production
      return LogLevel.INFO;
    }

    // In development, allow debug logs by default
    if (debugLogs || isDevelopment) {
      return LogLevel.DEBUG;
    }

    return LogLevel.INFO;
  }

  /**
   * Check if a log level should be processed
   */
  private shouldLog(level: LogLevel): boolean {
    return level <= this.config.level;
  }

  /**
   * Create a log entry
   */
  private createLogEntry(level: LogLevel, message: string, data?: any): LogEntry {
    const entry: LogEntry = {
      timestamp: new Date().toISOString(),
      level: LogLevel[level],
      message,
      context: this.config.context
    };

    if (data !== undefined) {
      entry.data = data;
    }

    // Add user context if available
    try {
      const userStr = localStorage.getItem('user');
      if (userStr) {
        const user = JSON.parse(userStr);
        entry.userId = user.id || user.email;
      }
    } catch (e) {
      // Ignore errors when getting user context
    }

    // Add session context if available
    try {
      const sessionId = sessionStorage.getItem('session_id');
      if (sessionId) {
        entry.sessionId = sessionId;
      }
    } catch (e) {
      // Ignore errors when getting session context
    }

    return entry;
  }

  /**
   * Log to console with appropriate styling
   */
  private logToConsole(entry: LogEntry): void {
    if (!this.config.enableConsole) return;

    const timestamp = new Date(entry.timestamp).toLocaleTimeString();
    const prefix = `[${timestamp}] [${entry.level}]`;

    switch (entry.level) {
      case 'ERROR':
        console.error(`%c${prefix}`, 'color: #ef4444; font-weight: bold;', entry.message, entry.data || '');
        break;
      case 'WARN':
        console.warn(`%c${prefix}`, 'color: #f59e0b; font-weight: bold;', entry.message, entry.data || '');
        break;
      case 'INFO':
        console.info(`%c${prefix}`, 'color: #3b82f6; font-weight: bold;', entry.message, entry.data || '');
        break;
      case 'DEBUG':
        console.debug(`%c${prefix}`, 'color: #6b7280; font-weight: bold;', entry.message, entry.data || '');
        break;
      default:
        console.log(`${prefix}`, entry.message, entry.data || '');
    }
  }

  /**
   * Add log entry to buffer for remote logging
   */
  private addToBuffer(entry: LogEntry): void {
    if (!this.config.enableRemote) return;

    this.logBuffer.push(entry);

    // Flush if buffer is full
    if (this.logBuffer.length >= this.maxBufferSize) {
      this.flush();
    }
  }

  /**
   * Start the flush timer for remote logging
   */
  private startFlushTimer(): void {
    this.flushTimer = setInterval(() => {
      this.flush();
    }, this.flushInterval);
  }

  /**
   * Flush log buffer to remote endpoint
   */
  private async flush(): Promise<void> {
    if (!this.config.enableRemote || this.logBuffer.length === 0) return;

    const logsToSend = [...this.logBuffer];
    this.logBuffer = [];

    try {
      const response = await fetch(this.config.remoteEndpoint!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        credentials: 'include',
        body: JSON.stringify({ logs: logsToSend })
      });

      if (!response.ok) {
        // If remote logging fails, log to console as fallback
        console.warn('Failed to send logs to remote endpoint:', response.statusText);
      }
    } catch (error) {
      // If remote logging fails, log to console as fallback
      console.warn('Error sending logs to remote endpoint:', error);
    }
  }

  /**
   * Core logging method
   */
  private logInternal(level: LogLevel, message: string, data?: any): void {
    if (!this.shouldLog(level)) return;

    const entry = this.createLogEntry(level, message, data);

    this.logToConsole(entry);
    this.addToBuffer(entry);
  }

  /**
   * Log error messages
   */
  error(message: string, data?: any): void {
    this.logInternal(LogLevel.ERROR, message, data);
  }

  /**
   * Log warning messages
   */
  warn(message: string, data?: any): void {
    this.logInternal(LogLevel.WARN, message, data);
  }

  /**
   * Log info messages
   */
  info(message: string, data?: any): void {
    this.logInternal(LogLevel.INFO, message, data);
  }

  /**
   * Log debug messages
   */
  debug(message: string, data?: any): void {
    this.logInternal(LogLevel.DEBUG, message, data);
  }

  /**
   * Alias for info (for backward compatibility)
   */
  log(message: string, data?: any): void {
    this.info(message, data);
  }

  /**
   * Set log level dynamically
   */
  setLevel(level: LogLevel): void {
    this.config.level = level;
  }

  /**
   * Get current log level
   */
  getLevel(): LogLevel {
    return this.config.level;
  }

  /**
   * Enable or disable console logging
   */
  setConsoleLogging(enabled: boolean): void {
    this.config.enableConsole = enabled;
  }

  /**
   * Enable or disable remote logging
   */
  setRemoteLogging(enabled: boolean): void {
    this.config.enableRemote = enabled;

    if (enabled && !this.flushTimer) {
      this.startFlushTimer();
    } else if (!enabled && this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = undefined;
    }
  }

  /**
   * Manually flush logs to remote endpoint
   */
  async flushLogs(): Promise<void> {
    await this.flush();
  }

  /**
   * Clear log buffer
   */
  clearBuffer(): void {
    this.logBuffer = [];
  }

  /**
   * Get current buffer size
   */
  getBufferSize(): number {
    return this.logBuffer.length;
  }

  /**
   * Cleanup method to clear timers
   */
  destroy(): void {
    if (this.flushTimer) {
      clearInterval(this.flushTimer);
      this.flushTimer = undefined;
    }
    this.flush(); // Final flush
  }
}

// Create and export singleton instance
const logger = new Logger();

export default logger;
export { Logger };

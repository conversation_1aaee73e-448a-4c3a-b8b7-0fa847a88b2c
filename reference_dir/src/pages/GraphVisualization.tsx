import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { FiArrowLeft, FiFileText } from 'react-icons/fi';

// Import modular graph components
import { GraphContainer } from '../components/graph/GraphContainer';
import { useComplaintData } from '../hooks/useComplaintData';
import { useThemeContext } from '../context/TailwindThemeContext';

// Main component
const GraphVisualization: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { isDark: isDarkMode } = useThemeContext();

  // State for export loading indicator
  const [isExporting, setIsExporting] = useState(false);
  const [exportProgress, setExportProgress] = useState<{ current: number; total: number; currentPage?: string }>({ current: 0, total: 100 });

  // Use our custom hook to fetch complaint data
  const { data: complaintData, loading, error } = useComplaintData(id as string, true);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500 mx-auto"></div>
          <p className={`mt-4 text-lg ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>Loading graph data...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center h-screen">
        <div className="text-center">
          <div className={`text-red-500 text-xl mb-4`}>Error loading graph data</div>
          <p className={`${isDarkMode ? 'text-white' : 'text-gray-800'}`}>{error}</p>
          <button
            onClick={() => navigate('/dashboard')}
            className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
          >
            Back to Dashboard
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="h-screen flex flex-col">
      {/* Header */}
      <div className={`${isDarkMode ? 'bg-gray-800 border-gray-700' : 'bg-white border-gray-200'} border-b px-4 py-3 flex items-center justify-between`}>
        <div className="flex items-center gap-4">
          <button
            onClick={() => navigate('/dashboard')}
            className={`flex items-center gap-2 px-3 py-2 rounded ${isDarkMode ? 'bg-gray-700 hover:bg-gray-600 text-white' : 'bg-gray-100 hover:bg-gray-200 text-gray-800'}`}
          >
            <FiArrowLeft />
            Back to Dashboard
          </button>
          <h1 className={`text-xl font-semibold ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>
            Graph Analysis - Complaint {id}
          </h1>
          {/* Export Loading Indicator */}
          {isExporting && (
            <div className="flex items-center gap-2">
              <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-blue-500"></div>
              <span className={`text-sm ${isDarkMode ? 'text-gray-300' : 'text-gray-600'}`}>
                {exportProgress.currentPage || 'Exporting graph...'}
              </span>
              <span className={`text-xs ${isDarkMode ? 'text-gray-400' : 'text-gray-500'}`}>
                {Math.round((exportProgress.current / exportProgress.total) * 100)}%
              </span>
            </div>
          )}
        </div>
        <div className="flex items-center gap-2">
          <button
            onClick={() => navigate(`/notices/generate/${id}`)}
            disabled={isExporting}
            className={`flex items-center gap-2 px-3 py-2 rounded ${
              isExporting
                ? 'opacity-50 cursor-not-allowed bg-gray-400'
                : isDarkMode
                  ? 'bg-blue-700 hover:bg-blue-600 text-white'
                  : 'bg-blue-500 hover:bg-blue-600 text-white'
            }`}
          >
            <FiFileText />
            Generate Notices
          </button>
        </div>
      </div>

      {/* Graph Container */}
      <div className="flex-1 relative">
        {complaintData ? (
          <GraphContainer
            complaintData={complaintData}
            complaintId={id as string}
            onExportStateChange={setIsExporting}
            onExportProgressChange={setExportProgress}
          />
        ) : (
          <div className="flex items-center justify-center h-full">
            <div className="text-center">
              <p className={`text-lg ${isDarkMode ? 'text-white' : 'text-gray-800'}`}>No graph data available</p>
              <button
                onClick={() => navigate('/dashboard')}
                className="mt-4 px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600"
              >
                Back to Dashboard
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default GraphVisualization;

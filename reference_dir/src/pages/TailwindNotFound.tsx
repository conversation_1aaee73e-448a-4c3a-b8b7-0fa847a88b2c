import React from 'react';
import { useNavigate } from 'react-router-dom';
import Button from '../components/ui/Button';
import { FiHome } from 'react-icons/fi';

const TailwindNotFound: React.FC = () => {
  const navigate = useNavigate();

  return (
    <div className="min-h-screen flex flex-col items-center justify-center p-4 text-center">
      <div className="max-w-md w-full mx-auto bg-white/80 dark:bg-[rgba(16,16,30,0.6)] backdrop-blur-md p-8 rounded-xl shadow-lg border border-blue-200/50 dark:border-[rgba(0,170,255,0.3)] dark:shadow-glow-blue-sm">
        <div className="mb-8">
          <h1 className="text-9xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-600 to-purple-600 dark:from-blue-400 dark:to-purple-400">
            404
          </h1>
          <h2 className="text-2xl font-semibold text-gray-800 dark:text-white mt-4 mb-2">
            Page Not Found
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-8">
            The page you are looking for doesn't exist or has been moved.
          </p>
        </div>

        <Button
          variant="primary"
          onClick={() => navigate('/')}
          startIcon={<FiHome />}
        >
          Go to Home
        </Button>
      </div>
    </div>
  );
};

export default TailwindNotFound;

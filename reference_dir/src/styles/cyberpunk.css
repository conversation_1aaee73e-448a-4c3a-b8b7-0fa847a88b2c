/**
 * NCRP Matrix - Cyberpunk Theme CSS
 * This file contains the cyberpunk styling for the application
 */

@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* CSS Variables for theme consistency */
:root {
  /* Light mode variables */
  --color-bg-primary: #f8faff;
  --color-bg-secondary: #eef2fa;
  --color-bg-tertiary: #e4e9f2;
  --color-bg-card: #ffffff;
  --color-bg-input: #ffffff;

  --color-text-primary: #1a202c;
  --color-text-secondary: #4a5568;
  --color-text-tertiary: #7a8599;
  --color-text-inverse: #f8faff;

  --color-border-light: rgba(228, 233, 242, 0.8);
  --color-border-medium: rgba(209, 216, 230, 0.9);
  --color-border-strong: #a3aec2;

  --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.05), 0 1px 3px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.05), 0 4px 6px rgba(0, 0, 0, 0.05);

  --glass-bg: rgba(255, 255, 255, 0.8);
  --glass-border: rgba(255, 255, 255, 0.5);
  --glass-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.dark {
  /* Improved dark cyberpunk theme variables */
  --color-bg-primary: #0a0a0f;
  --color-bg-secondary: #12121a;
  --color-bg-tertiary: #1a1a25;
  --color-bg-card: rgba(10, 10, 15, 0.85);
  --color-bg-input: rgba(18, 18, 26, 0.9);

  --color-text-primary: #e8f4ff;
  --color-text-secondary: #c2d9f0;
  --color-text-tertiary: #8fa8c4;
  --color-text-inverse: #0a0a0f;

  --color-border-light: rgba(0, 170, 255, 0.25);
  --color-border-medium: rgba(0, 170, 255, 0.4);
  --color-border-strong: rgba(0, 170, 255, 0.6);

  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.4);
  --shadow-md: 0 6px 16px rgba(0, 0, 0, 0.5);
  --shadow-lg: 0 12px 32px rgba(0, 0, 0, 0.6);

  --glass-bg: rgba(10, 10, 15, 0.85);
  --glass-border: rgba(0, 170, 255, 0.3);
  --glass-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);

  /* Enhanced glow effects for improved cyberpunk theme */
  --glow-blue-sm: 0 0 8px rgba(0, 170, 255, 0.6);
  --glow-blue-md: 0 0 16px rgba(0, 170, 255, 0.8);
  --glow-blue-lg: 0 0 24px rgba(0, 170, 255, 1), 0 0 48px rgba(0, 170, 255, 0.6);

  --glow-purple-sm: 0 0 8px rgba(138, 43, 226, 0.6);
  --glow-purple-md: 0 0 16px rgba(138, 43, 226, 0.8);
  --glow-purple-lg: 0 0 24px rgba(138, 43, 226, 1), 0 0 48px rgba(138, 43, 226, 0.6);

  --glow-orange-sm: 0 0 8px rgba(251, 146, 60, 0.6);
  --glow-orange-md: 0 0 16px rgba(251, 146, 60, 0.8);
  --glow-orange-lg: 0 0 24px rgba(251, 146, 60, 1), 0 0 48px rgba(251, 146, 60, 0.6);

  --glow-green-sm: 0 0 8px rgba(0, 255, 149, 0.6);
  --glow-green-md: 0 0 16px rgba(0, 255, 149, 0.8);
  --glow-green-lg: 0 0 24px rgba(0, 255, 149, 1), 0 0 48px rgba(0, 255, 149, 0.6);

  --glow-red-sm: 0 0 8px rgba(255, 0, 102, 0.6);
  --glow-red-md: 0 0 16px rgba(255, 0, 102, 0.8);
  --glow-red-lg: 0 0 24px rgba(255, 0, 102, 1), 0 0 48px rgba(255, 0, 102, 0.6);

  color-scheme: dark;
}

/* Base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
}

html, body, #root {
  width: 100%;
  height: 100%;
  overflow: hidden;
  max-width: 100vw;
}

body {
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  font-size: 16px;
  line-height: 1.5;
  font-weight: 400;
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  letter-spacing: -0.011em;
  color: var(--color-text-primary);
  background-color: var(--color-bg-primary);
}

/* Light mode specific styles */
body {
  background-image:
    linear-gradient(to bottom, rgba(0, 170, 255, 0.02), rgba(255, 255, 255, 0)),
    linear-gradient(rgba(0, 170, 255, 0.02) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 170, 255, 0.02) 1px, transparent 1px);
  background-size: 100% 100%, 50px 50px, 50px 50px;
}

/* Improved dark cyberpunk theme background - only applies to dark mode */
.theme-darkmode body {
  background-image:
    linear-gradient(135deg, rgba(0, 170, 255, 0.03) 0%, rgba(138, 43, 226, 0.03) 100%),
    linear-gradient(rgba(0, 170, 255, 0.08) 1px, transparent 1px),
    linear-gradient(90deg, rgba(0, 170, 255, 0.08) 1px, transparent 1px),
    radial-gradient(circle at 20% 80%, rgba(138, 43, 226, 0.06) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(0, 170, 255, 0.06) 0%, transparent 50%);
  background-size: 100% 100%, 60px 60px, 60px 60px, 400px 400px, 400px 400px;
}

/* Note: Scan line effects are defined in index.css to avoid conflicts */

/* Cyberpunk-inspired utility classes */
.cyber-card {
  background: var(--glass-bg);
  backdrop-filter: blur(10px);
  border: 1px solid var(--glass-border);
  box-shadow: var(--glass-shadow);
  border-radius: 12px;
  overflow: hidden;
  transition: all 0.3s ease-in-out;
  transform: translateY(0);
}

.cyber-card:hover {
  transform: translateY(-5px);
  box-shadow: var(--shadow-lg);
}

/* Cyber text with gradient */
.cyber-text-gradient {
  background: linear-gradient(135deg, #00DDFF 0%, #0088FF 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.cyber-text-gradient-orange {
  background: linear-gradient(135deg, #FB923C 0%, #EA580C 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.cyber-text-gradient-purple {
  background: linear-gradient(135deg, #8A2BE2 0%, #9932CC 50%, #6A0DAD 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

.cyber-text-gradient-green {
  background: linear-gradient(135deg, #66FFBB 0%, #00AA44 100%);
  -webkit-background-clip: text;
  background-clip: text;
  color: transparent;
  display: inline-block;
}

/* Cyber glow effects */
.shadow-glow-blue-sm {
  box-shadow: var(--glow-blue-sm);
}

.shadow-glow-blue {
  box-shadow: var(--glow-blue-md);
}

.shadow-glow-blue-lg {
  box-shadow: var(--glow-blue-lg);
}

.shadow-glow-purple-sm {
  box-shadow: var(--glow-purple-sm);
}

.shadow-glow-purple {
  box-shadow: var(--glow-purple-md);
}

.shadow-glow-purple-lg {
  box-shadow: var(--glow-purple-lg);
}

.shadow-glow-green-sm {
  box-shadow: var(--glow-green-sm);
}

.shadow-glow-green {
  box-shadow: var(--glow-green-md);
}

.shadow-glow-green-lg {
  box-shadow: var(--glow-green-lg);
}

.shadow-glow-red-sm {
  box-shadow: var(--glow-red-sm);
}

.shadow-glow-red {
  box-shadow: var(--glow-red-md);
}

.shadow-glow-red-lg {
  box-shadow: var(--glow-red-lg);
}

/* Gradient backgrounds */
.bg-gradient-blue {
  background: linear-gradient(135deg, #00AAFF 0%, #0088FF 100%);
}

.bg-gradient-purple {
  background: linear-gradient(135deg, #C800FF 0%, #9500BD 100%);
}

.bg-gradient-green {
  background: linear-gradient(135deg, #00FF95 0%, #00CC78 100%);
}

.bg-gradient-red {
  background: linear-gradient(135deg, #FF0066 0%, #CC0052 100%);
}

/* Animations */
.animate-fade-in {
  animation: fadeIn 0.5s ease-out forwards;
}

.animate-slide-up {
  animation: slideUp 0.5s ease-out forwards;
}

.animate-scan-line {
  animation: scanLine 8s linear infinite;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { transform: translateY(20px); opacity: 0; }
  to { transform: translateY(0); opacity: 1; }
}

/* Drop shadows */
.drop-shadow-blue {
  filter: drop-shadow(0 0 5px rgba(0, 170, 255, 0.5));
}

.drop-shadow-purple {
  filter: drop-shadow(0 0 5px rgba(200, 0, 255, 0.5));
}

.drop-shadow-green {
  filter: drop-shadow(0 0 5px rgba(0, 255, 149, 0.5));
}

.drop-shadow-red {
  filter: drop-shadow(0 0 5px rgba(255, 0, 102, 0.5));
}

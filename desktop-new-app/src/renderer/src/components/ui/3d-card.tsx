'use client'

import { cn } from '../../lib/aceternity-utils'
import { useThemeContext } from '../../context/useThemeContext'
import { GlowingEffect } from './glowing-effect'

import React, { useState, useRef, useEffect } from 'react'
import { MouseEnterContext } from '../../context/mouse-enter-context'

export const CardContainer = ({
  children,
  className,
  containerClassName
}: {
  children?: React.ReactNode
  className?: string
  containerClassName?: string
}): React.JSX.Element => {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isMouseEntered, setIsMouseEntered] = useState(false)

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>): void => {
    if (!containerRef.current) return
    const { left, top, width, height } = containerRef.current.getBoundingClientRect()
    const x = (e.clientX - left - width / 2) / 25
    const y = (e.clientY - top - height / 2) / 25
    containerRef.current.style.transform = `rotateY(${x}deg) rotateX(${y}deg)`
  }

  const handleMouseEnter = (): void => {
    setIsMouseEntered(true)
    if (!containerRef.current) return
  }

  const handleMouseLeave = (): void => {
    if (!containerRef.current) return
    setIsMouseEntered(false)
    containerRef.current.style.transform = `rotateY(0deg) rotateX(0deg)`
  }
  return (
    <MouseEnterContext.Provider value={[isMouseEntered, setIsMouseEntered]}>
      <div
        className={cn('py-20 flex items-center justify-center', containerClassName)}
        style={{
          perspective: '1000px'
        }}
      >
        <div
          ref={containerRef}
          onMouseEnter={handleMouseEnter}
          onMouseMove={handleMouseMove}
          onMouseLeave={handleMouseLeave}
          className={cn(
            'flex items-center justify-center relative transition-all duration-200 ease-linear',
            className
          )}
          style={{
            transformStyle: 'preserve-3d'
          }}
        >
          {children}
        </div>
      </div>
    </MouseEnterContext.Provider>
  )
}

export const CardBody = ({
  children,
  className,
  enableGlassmorphism = true,
  enableGlow = true
}: {
  children: React.ReactNode
  className?: string
  enableGlassmorphism?: boolean
  enableGlow?: boolean
}): React.JSX.Element => {
  const { isDark } = useThemeContext()
  const [isMouseEntered] = useMouseEnter()

  return (
    <div
      className={cn(
        'h-96 w-96 [transform-style:preserve-3d] [&>*]:[transform-style:preserve-3d] relative',
        // Glassmorphism base styles
        enableGlassmorphism && [
          'rounded-xl border',
          isDark
            ? 'bg-white/5 border-white/10 backdrop-blur-md'
            : 'bg-white/70 border-gray-200/50 backdrop-blur-md',
          'shadow-xl',
          // Enhanced shadow for depth
          isDark ? 'shadow-black/20' : 'shadow-gray-900/10'
        ],
        // Hover effects
        enableGlassmorphism && [
          'transition-all duration-300 ease-out',
          'hover:shadow-2xl',
          isDark
            ? 'hover:bg-white/10 hover:border-white/20'
            : 'hover:bg-white/80 hover:border-gray-300/60'
        ],
        className
      )}
    >
      {/* Glowing effect overlay */}
      {enableGlow && (
        <GlowingEffect
          blur={10}
          spread={30}
          proximity={0.3}
          glow={isMouseEntered}
          disabled={!isMouseEntered}
          className="absolute inset-0 rounded-xl"
        />
      )}

      {/* Content with proper backdrop blur for text readability */}
      <div
        className={cn(
          'relative z-10 h-full w-full',
          enableGlassmorphism && [
            'rounded-xl',
            // Text backdrop blur for better readability
            '[&_*]:backdrop-blur-sm',
            // Ensure text has proper contrast
            isDark ? '[&_*]:text-white/90' : '[&_*]:text-gray-900/90'
          ]
        )}
      >
        {children}
      </div>

      {/* Additional glassmorphism overlay for enhanced effect */}
      {enableGlassmorphism && (
        <div
          className={cn(
            'absolute inset-0 rounded-xl pointer-events-none',
            'bg-gradient-to-br',
            isDark
              ? 'from-white/5 via-transparent to-white/5'
              : 'from-white/20 via-transparent to-white/20',
            'opacity-50'
          )}
        />
      )}
    </div>
  )
}

export const CardItem = ({
  as: Tag = 'div',
  children,
  className,
  translateX = 0,
  translateY = 0,
  translateZ = 0,
  rotateX = 0,
  rotateY = 0,
  rotateZ = 0,
  enableGlassmorphism = false,
  enableHoverGlow = false,
  ...rest
}: {
  as?: React.ElementType
  children: React.ReactNode
  className?: string
  translateX?: number | string
  translateY?: number | string
  translateZ?: number | string
  rotateX?: number | string
  rotateY?: number | string
  rotateZ?: number | string
  enableGlassmorphism?: boolean
  enableHoverGlow?: boolean
  [key: string]: unknown
}): React.JSX.Element => {
  const ref = useRef<HTMLDivElement>(null)
  const [isMouseEntered] = useMouseEnter()
  const { isDark } = useThemeContext()

  useEffect(() => {
    if (!ref.current) return
    if (isMouseEntered) {
      ref.current.style.transform = `translateX(${translateX}px) translateY(${translateY}px) translateZ(${translateZ}px) rotateX(${rotateX}deg) rotateY(${rotateY}deg) rotateZ(${rotateZ}deg)`
    } else {
      ref.current.style.transform = `translateX(0px) translateY(0px) translateZ(0px) rotateX(0deg) rotateY(0deg) rotateZ(0deg)`
    }
  }, [isMouseEntered, translateX, translateY, translateZ, rotateX, rotateY, rotateZ])

  return (
    <Tag
      ref={ref}
      className={cn(
        'w-fit transition-all duration-300 ease-linear relative',
        // Glassmorphism styles for CardItem
        enableGlassmorphism && [
          'rounded-lg border',
          isDark
            ? 'bg-white/5 border-white/10 backdrop-blur-sm'
            : 'bg-white/60 border-gray-200/40 backdrop-blur-sm',
          'shadow-lg',
          isDark ? 'shadow-black/10' : 'shadow-gray-900/5'
        ],
        // Hover glow effects
        enableHoverGlow && [
          'transition-all duration-300',
          isMouseEntered && [
            'shadow-xl',
            isDark
              ? 'shadow-cyan-500/20 border-cyan-400/30'
              : 'shadow-blue-500/20 border-blue-400/30'
          ]
        ],
        className
      )}
      {...rest}
    >
      {/* Hover glow effect */}
      {enableHoverGlow && isMouseEntered && (
        <div
          className={cn(
            'absolute inset-0 rounded-lg pointer-events-none',
            'bg-gradient-to-r',
            isDark
              ? 'from-cyan-500/10 via-blue-500/5 to-purple-500/10'
              : 'from-blue-500/10 via-indigo-500/5 to-purple-500/10',
            'animate-pulse'
          )}
        />
      )}

      {/* Content */}
      <div className="relative z-10">{children}</div>
    </Tag>
  )
}

import { useMouseEnter } from '../../hooks/use-mouse-enter'

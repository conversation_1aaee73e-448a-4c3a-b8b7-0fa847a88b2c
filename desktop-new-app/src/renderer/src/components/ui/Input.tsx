import * as React from 'react'
import { cn } from '../../lib/aceternity-utils'

interface InputProps extends React.InputHTMLAttributes<HTMLInputElement> {}

const Input = React.forwardRef<HTMLInputElement, InputProps>(
  ({ className, type, ...props }, ref) => {
    return (
      <input
        type={type}
        className={cn(
          'flex h-10 w-full rounded-lg border backdrop-blur-sm px-3 py-2 text-sm transition-all duration-200',
          'file:border-0 file:bg-transparent file:text-sm file:font-medium',
          'focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-offset-2',
          'disabled:cursor-not-allowed disabled:opacity-50',
          'bg-white/70 border-gray-200/50 text-gray-900 placeholder:text-gray-500',
          'dark:bg-white/10 dark:border-white/20 dark:text-white dark:placeholder:text-gray-400',
          'focus-visible:ring-blue-500 focus-visible:border-blue-500',
          'hover:border-gray-300 dark:hover:border-white/30',
          className
        )}
        ref={ref}
        {...props}
      />
    )
  }
)
Input.displayName = 'Input'

export { Input }

import { MarkerType } from '@xyflow/react'
import dagre from 'dagre'

// Define types for graph nodes and edges
type GraphNode = {
  id: string
  type: string
  data: any
  position: { x: number; y: number }
  layer?: number
}

// Define a type that matches ReactFlow's Edge type but with our custom properties
type GraphEdge = {
  id: string
  source: string
  target: string
  type?: string
  animated?: boolean
  label?: string
  data?: any
  markerEnd?: {
    type: MarkerType
    width: number
    height: number
    color: string
  }
}

export class GraphLayoutManager {
  private dagreGraph: dagre.graphlib.Graph
  private readonly NODE_WIDTH = 350 // Increased width for better readability

  constructor() {
    // Setup the dagre graph
    this.dagreGraph = new dagre.graphlib.Graph()
    this.dagreGraph.setDefaultEdgeLabel(() => ({}))
  }

  // Analyze sender trees to determine complexity and optimal ordering
  private analyzeSenderTrees(
    transactions: any[],
    transactionsByLayer: { [key: number]: any[] }
  ): {
    senderAccounts: string[]
    senderComplexity: {
      [key: string]: { totalNodes: number; maxDepth: number; directChildren: number }
    }
    orderedSenders: string[]
  } {
    const senderComplexity: {
      [key: string]: { totalNodes: number; maxDepth: number; directChildren: number }
    } = {}
    const uniqueSenderAccounts = new Set<string>()

    // Extract unique sender accounts from Layer 1 transactions
    if (transactionsByLayer[1]) {
      transactionsByLayer[1].forEach((txn) => {
        const isMainTransactionType =
          txn.type === 'Money Transfer to' || txn.isMainTransaction === true
        if (isMainTransactionType && txn.sender_account) {
          uniqueSenderAccounts.add(txn.sender_account)
        }
      })
    }

    // Analyze each sender's tree complexity
    Array.from(uniqueSenderAccounts).forEach((senderAccount) => {
      const analysis = this.analyzeSenderTreeComplexity(
        senderAccount,
        transactions,
        transactionsByLayer
      )
      senderComplexity[senderAccount] = analysis
    })

    // Order senders by complexity (least complex first)
    const orderedSenders = Array.from(uniqueSenderAccounts).sort((a, b) => {
      const complexityA = senderComplexity[a]
      const complexityB = senderComplexity[b]

      // Primary sort: by total nodes (ascending - least complex first)
      if (complexityA.totalNodes !== complexityB.totalNodes) {
        return complexityA.totalNodes - complexityB.totalNodes
      }

      // Secondary sort: by max depth (ascending)
      if (complexityA.maxDepth !== complexityB.maxDepth) {
        return complexityA.maxDepth - complexityB.maxDepth
      }

      // Tertiary sort: by direct children (ascending)
      return complexityA.directChildren - complexityB.directChildren
    })

    return {
      senderAccounts: Array.from(uniqueSenderAccounts),
      senderComplexity,
      orderedSenders
    }
  }

  // Analyze the complexity of a single sender's tree
  private analyzeSenderTreeComplexity(
    senderAccount: string,
    _transactions: any[],
    transactionsByLayer: { [key: number]: any[] }
  ): { totalNodes: number; maxDepth: number; directChildren: number } {
    let totalNodes = 0
    let maxDepth = 0
    let directChildren = 0

    // Count direct children (layer 1)
    if (transactionsByLayer[1]) {
      directChildren = transactionsByLayer[1].filter(
        (txn) =>
          txn.sender_account === senderAccount &&
          (txn.isMainTransaction === true || txn.type === 'Money Transfer to')
      ).length
      totalNodes += directChildren
    }

    // Recursively count nodes in deeper layers
    const countNodesInSubtree = (accountsToCheck: string[], currentLayer: number): number => {
      if (currentLayer > 10 || !transactionsByLayer[currentLayer]) return 0 // Prevent infinite recursion

      let nodeCount = 0
      const nextLayerAccounts: string[] = []

      transactionsByLayer[currentLayer].forEach((txn) => {
        if (accountsToCheck.includes(txn.sender_account || '')) {
          nodeCount++
          const receiverAccount = txn.receiver_account || txn.account
          if (receiverAccount && !nextLayerAccounts.includes(receiverAccount)) {
            nextLayerAccounts.push(receiverAccount)
          }
        }
      })

      if (nodeCount > 0) {
        maxDepth = Math.max(maxDepth, currentLayer)
      }

      if (nextLayerAccounts.length > 0) {
        nodeCount += countNodesInSubtree(nextLayerAccounts, currentLayer + 1)
      }

      return nodeCount
    }

    // Get receiver accounts from layer 1 to start the recursive count
    const layer1ReceiverAccounts: string[] = []
    if (transactionsByLayer[1]) {
      transactionsByLayer[1].forEach((txn) => {
        if (txn.sender_account === senderAccount) {
          const receiverAccount = txn.receiver_account || txn.account
          if (receiverAccount && !layer1ReceiverAccounts.includes(receiverAccount)) {
            layer1ReceiverAccounts.push(receiverAccount)
          }
        }
      })
    }

    // Count nodes in layers 2 and beyond
    if (layer1ReceiverAccounts.length > 0) {
      totalNodes += countNodesInSubtree(layer1ReceiverAccounts, 2)
    }

    return { totalNodes, maxDepth: Math.max(maxDepth, 1), directChildren }
  }

  // Process transactions to create graph nodes and edges with TB layout only
  processTransactionsForGraph(
    transactions: any[],
    metadata: any,
    complaintData: any,
    complaintId: string,
    isDarkMode: boolean,
    _horizontalLayout: boolean = false // Only TB layout supported now
  ): { nodes: GraphNode[]; edges: GraphEdge[] } {
    // Performance optimization: limit transaction processing for large datasets
    let processedTransactions = transactions
    if (transactions.length > 500) {
      // For very large datasets, process only the first 500 transactions
      processedTransactions = transactions.slice(0, 500)
    }

    // First, consolidate matching money transfer transactions
    const consolidatedTransactions =
      this.consolidateMoneyTransferTransactions(processedTransactions)

    // Use TB layout method only
    return this.processTransactionsWithTBLayout(
      consolidatedTransactions,
      metadata,
      complaintData,
      complaintId,
      isDarkMode
    )
  }

  // Consolidate matching money transfer transactions and attach sub-transactions
  private consolidateMoneyTransferTransactions(transactions: any[]): any[] {
    const consolidatedTransactions: any[] = []
    const processedTransactions = new Set<string>()

    // Group transactions by layer first
    const transactionsByLayer: { [key: number]: any[] } = {}
    transactions.forEach((txn) => {
      const layer = txn.layer !== undefined ? Number(txn.layer) : 0
      if (!transactionsByLayer[layer]) {
        transactionsByLayer[layer] = []
      }
      transactionsByLayer[layer].push(txn)
    })

    // First, identify all main transactions and their potential sub-transactions
    const mainTransactionMap: { [key: string]: any } = {}
    const subTransactionMap: { [key: string]: any[] } = {}

    // Process each layer
    Object.keys(transactionsByLayer)
      .map(Number)
      .sort((a, b) => a - b)
      .forEach((layer) => {
        const layerTxns = transactionsByLayer[layer]

        // Separate main transactions and sub-transactions
        const mainTransactions: any[] = []
        const subTransactions: any[] = []

        layerTxns.forEach((txn) => {
          const txnId = `${txn.sender_account || ''}_${txn.txn_id || ''}_${txn.layer || 0}`

          // Skip if already processed
          if (processedTransactions.has(txnId)) {
            return
          }

          const isMoneyTransfer = txn.type === 'Money transfer' || txn.type === 'Money Transfer to'

          if (isMoneyTransfer) {
            mainTransactions.push(txn)
            // Create a key for this main transaction for sub-transaction matching
            const mainTxnKey = `${txn.receiver_account || txn.account || ''}_${txn.txn_id || ''}`
            mainTransactionMap[mainTxnKey] = txn
          } else {
            // This is a potential sub-transaction
            subTransactions.push(txn)
          }
        })

        // Process consolidation groups and attach sub-transactions
        Object.entries({}).forEach(([, groupTxns]) => {
          // Implementation continues...
        })
      })

    return consolidatedTransactions
  }

  // TB Layout method with proper hierarchy
  private processTransactionsWithTBLayout(
    transactions: any[],
    metadata: any,
    complaintData: any,
    _complaintId: string,
    isDarkMode: boolean
  ): { nodes: GraphNode[]; edges: GraphEdge[] } {
    const nodes: GraphNode[] = []
    const edges: GraphEdge[] = []

    // Configure Dagre graph for TB layout only
    this.dagreGraph.setGraph({
      rankdir: 'TB',
      // Horizontal spacing between nodes in the same rank/layer
      nodesep: 100,
      // Vertical spacing between ranks/layers
      ranksep: 200,
      // Margins around the entire graph
      marginx: 50,
      marginy: 50,
      // Alignment of nodes within ranks
      align: 'UL'
    })

    // Create metadata node (root)
    const metadataNodeId = 'metadata_node'
    this.dagreGraph.setNode(metadataNodeId, {
      width: this.NODE_WIDTH,
      height: 120
    })

    nodes.push({
      id: metadataNodeId,
      type: 'metadata',
      data: {
        label: `Complaint: ${metadata.complaint_number || 'Unknown'}`,
        account: metadata.complainant_name || 'Unknown',
        complaint_number: metadata.complaint_number,
        date: metadata.date_of_complaint,
        total_amount: metadata.total_amount,
        complainant_name: metadata.complainant_name
      },
      position: { x: 0, y: 0 } // Will be updated by Dagre
    })

    // Apply Dagre layout
    dagre.layout(this.dagreGraph)

    // Update node positions based on Dagre layout
    const layoutedNodes = nodes.map((node) => {
      const nodeWithPosition = this.dagreGraph.node(node.id)
      return {
        ...node,
        position: {
          x: nodeWithPosition.x - this.NODE_WIDTH / 2,
          y: nodeWithPosition.y - 60
        }
      }
    })

    return { nodes: layoutedNodes, edges }
  }
}

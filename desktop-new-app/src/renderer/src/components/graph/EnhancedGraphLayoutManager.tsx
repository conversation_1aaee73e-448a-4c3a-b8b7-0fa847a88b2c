import { Node, Edge, Position } from '@xyflow/react'
import { ComplaintGraphData } from '../../../../shared/api'

export interface LayoutConfig {
  nodeWidth: number
  nodeHeight: number
  horizontalSpacing: number
  verticalSpacing: number
  layerSpacing: number
  centerAlignment: boolean
  preventOverlap: boolean
  hierarchicalLayout: boolean
}

export const defaultLayoutConfig: LayoutConfig = {
  nodeWidth: 350,
  nodeHeight: 200,
  horizontalSpacing: 80,
  verticalSpacing: 120,
  layerSpacing: 200,
  centerAlignment: true,
  preventOverlap: true,
  hierarchicalLayout: true
}

export class EnhancedGraphLayoutManager {
  private config: LayoutConfig

  constructor(config: Partial<LayoutConfig> = {}) {
    this.config = { ...defaultLayoutConfig, ...config }
  }

  /**
   * Apply hierarchical layout to nodes based on layers
   */
  public applyHierarchicalLayout(nodes: Node[], edges: Edge[]): { nodes: Node[]; edges: Edge[] } {
    if (!this.config.hierarchicalLayout) {
      return { nodes, edges }
    }

    // Group nodes by layer
    const nodesByLayer = this.groupNodesByLayer(nodes)
    const layoutNodes: Node[] = []

    // Calculate positions for each layer
    Object.entries(nodesByLayer).forEach(([layerStr, layerNodes]) => {
      const layer = parseInt(layerStr)
      const positionedNodes = this.positionNodesInLayer(layerNodes, layer)
      layoutNodes.push(...positionedNodes)
    })

    return {
      nodes: layoutNodes,
      edges: this.updateEdgePositions(edges, layoutNodes)
    }
  }

  /**
   * Group nodes by their layer property
   */
  private groupNodesByLayer(nodes: Node[]): Record<number, Node[]> {
    const grouped: Record<number, Node[]> = {}

    nodes.forEach((node) => {
      const layer = this.getNodeLayer(node)
      if (!grouped[layer]) {
        grouped[layer] = []
      }
      grouped[layer].push(node)
    })

    return grouped
  }

  /**
   * Get layer number from node data
   */
  private getNodeLayer(node: Node): number {
    const layer = node.data?.layer
    if (typeof layer === 'number') return layer
    if (typeof layer === 'string') return parseInt(layer) || 0
    return 0
  }

  /**
   * Position nodes within a specific layer
   */
  private positionNodesInLayer(nodes: Node[], layer: number): Node[] {
    if (nodes.length === 0) return nodes

    const y = layer * this.config.layerSpacing
    const totalWidth = this.calculateLayerWidth(nodes.length)
    const startX = this.config.centerAlignment ? -totalWidth / 2 : 0

    return nodes.map((node, index) => {
      const x = startX + index * (this.config.nodeWidth + this.config.horizontalSpacing)

      return {
        ...node,
        position: {
          x: this.config.preventOverlap ? this.adjustForOverlap(x, y, nodes, index) : x,
          y
        }
      }
    })
  }

  /**
   * Calculate total width needed for a layer
   */
  private calculateLayerWidth(nodeCount: number): number {
    if (nodeCount <= 1) return this.config.nodeWidth
    return nodeCount * this.config.nodeWidth + (nodeCount - 1) * this.config.horizontalSpacing
  }

  /**
   * Adjust position to prevent overlap
   */
  private adjustForOverlap(x: number, y: number, layerNodes: Node[], currentIndex: number): number {
    if (!this.config.preventOverlap || currentIndex === 0) return x

    // Check for overlap with previous nodes
    for (let i = 0; i < currentIndex; i++) {
      const prevNode = layerNodes[i]
      if (prevNode.position) {
        const minDistance = this.config.nodeWidth + this.config.horizontalSpacing
        const distance = x - prevNode.position.x

        if (distance < minDistance) {
          x = prevNode.position.x + minDistance
        }
      }
    }

    return x
  }

  /**
   * Update edge positions based on new node positions
   */
  private updateEdgePositions(edges: Edge[], nodes: Node[]): Edge[] {
    const nodePositions = new Map(nodes.map((node) => [node.id, node.position]))

    return edges.map((edge) => ({
      ...edge,
      sourcePosition: Position.Bottom,
      targetPosition: Position.Top
    }))
  }

  /**
   * Auto-layout graph data from complaint
   */
  public autoLayoutGraphData(graphData: ComplaintGraphData): ComplaintGraphData {
    if (!graphData.nodes || graphData.nodes.length === 0) {
      return graphData
    }

    // Convert to ReactFlow format
    const reactFlowNodes: Node[] = graphData.nodes.map((node) => ({
      id: node.id,
      type: node.type || 'default',
      position: node.position || { x: 0, y: 0 },
      data: node.data
    }))

    const reactFlowEdges: Edge[] =
      graphData.edges?.map((edge) => ({
        id: edge.id,
        source: edge.source,
        target: edge.target,
        type: edge.type || 'default'
      })) || []

    // Apply layout
    const { nodes: layoutNodes } = this.applyHierarchicalLayout(reactFlowNodes, reactFlowEdges)

    // Convert back to graph data format
    const updatedNodes = layoutNodes.map((node) => ({
      id: node.id,
      type: node.type || 'default',
      position: node.position,
      data: node.data
    }))

    return {
      ...graphData,
      nodes: updatedNodes
    }
  }

  /**
   * Optimize spacing between nodes to prevent overlap
   */
  public optimizeNodeSpacing(nodes: Node[]): Node[] {
    const nodesByLayer = this.groupNodesByLayer(nodes)
    const optimizedNodes: Node[] = []

    Object.entries(nodesByLayer).forEach(([layerStr, layerNodes]) => {
      const layer = parseInt(layerStr)

      // Sort nodes by x position
      layerNodes.sort((a, b) => (a.position?.x || 0) - (b.position?.x || 0))

      // Adjust positions to prevent overlap
      let currentX = this.config.centerAlignment
        ? -this.calculateLayerWidth(layerNodes.length) / 2
        : 0

      layerNodes.forEach((node) => {
        optimizedNodes.push({
          ...node,
          position: {
            x: currentX,
            y: layer * this.config.layerSpacing
          }
        })
        currentX += this.config.nodeWidth + this.config.horizontalSpacing
      })
    })

    return optimizedNodes
  }

  /**
   * Calculate optimal position for a new node
   */
  public calculateNewNodePosition(
    existingNodes: Node[],
    layer: number,
    preferredX?: number
  ): { x: number; y: number } {
    const y = layer * this.config.layerSpacing

    // Get nodes in the same layer
    const sameLayerNodes = existingNodes.filter((node) => this.getNodeLayer(node) === layer)

    if (sameLayerNodes.length === 0) {
      return { x: preferredX || 0, y }
    }

    // Sort by x position
    sameLayerNodes.sort((a, b) => (a.position?.x || 0) - (b.position?.x || 0))

    // If preferred position is specified, try to use it
    if (preferredX !== undefined) {
      const hasOverlap = sameLayerNodes.some((node) => {
        const nodeX = node.position?.x || 0
        const distance = Math.abs(nodeX - preferredX)
        return distance < this.config.nodeWidth + this.config.horizontalSpacing
      })

      if (!hasOverlap) {
        return { x: preferredX, y }
      }
    }

    // Find the rightmost position
    const rightmostNode = sameLayerNodes[sameLayerNodes.length - 1]
    const x =
      (rightmostNode.position?.x || 0) + this.config.nodeWidth + this.config.horizontalSpacing

    return { x, y }
  }

  /**
   * Update layout configuration
   */
  public updateConfig(newConfig: Partial<LayoutConfig>): void {
    this.config = { ...this.config, ...newConfig }
  }

  /**
   * Get current configuration
   */
  public getConfig(): LayoutConfig {
    return { ...this.config }
  }

  /**
   * Center all nodes in the viewport
   */
  public centerNodes(nodes: Node[]): Node[] {
    if (nodes.length === 0) return nodes

    // Calculate bounds
    const bounds = this.calculateNodeBounds(nodes)
    const centerX = (bounds.minX + bounds.maxX) / 2
    const centerY = (bounds.minY + bounds.maxY) / 2

    // Offset to center
    return nodes.map((node) => ({
      ...node,
      position: {
        x: (node.position?.x || 0) - centerX,
        y: (node.position?.y || 0) - centerY
      }
    }))
  }

  /**
   * Calculate bounding box of all nodes
   */
  private calculateNodeBounds(nodes: Node[]): {
    minX: number
    maxX: number
    minY: number
    maxY: number
  } {
    let minX = Infinity
    let maxX = -Infinity
    let minY = Infinity
    let maxY = -Infinity

    nodes.forEach((node) => {
      const x = node.position?.x || 0
      const y = node.position?.y || 0

      minX = Math.min(minX, x)
      maxX = Math.max(maxX, x + this.config.nodeWidth)
      minY = Math.min(minY, y)
      maxY = Math.max(maxY, y + this.config.nodeHeight)
    })

    return { minX, maxX, minY, maxY }
  }
}

export default EnhancedGraphLayoutManager

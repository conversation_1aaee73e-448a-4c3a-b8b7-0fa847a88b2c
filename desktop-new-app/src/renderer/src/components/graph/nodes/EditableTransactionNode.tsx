import React, { useState, useCallback, useRef, useEffect } from 'react'
import { <PERSON><PERSON>, Position, NodeProps } from '@xyflow/react'
import { motion, AnimatePresence } from 'motion/react'
import {
  FiEdit2,
  <PERSON>Check,
  FiX,
  FiPlus,
  FiMinus,
  FiChevronDown,
  FiChevronRight,
  FiSave
} from 'react-icons/fi'
import { cn } from '../../../lib/aceternity-utils'
import { useThemeContext } from '../../../context/useThemeContext'

// Enhanced node data interface with editable fields
interface EditableNodeData {
  // Core transaction data
  id: string
  label?: string
  account?: string
  amount?: string | number
  date?: string
  txn_type?: string
  txn_id?: string
  bank?: string

  // Additional editable fields
  account_holder?: string
  mobile_number?: string
  email?: string
  address?: string
  ifsc_code?: string
  branch?: string
  remarks?: string

  // Node metadata
  layer?: string | number
  nodeType?: string
  isMainTransaction?: boolean
  isSubTransaction?: boolean
  hasSubTransactions?: boolean
  hasChildren?: boolean
  isCollapsed?: boolean

  // Custom fields added by user
  customFields?: Record<string, any>

  // Callbacks
  onUpdate?: (nodeId: string, data: Partial<EditableNodeData>) => void
  onToggleCollapse?: () => void
}

// Field configuration for editing
interface FieldConfig {
  key: string
  label: string
  type: 'text' | 'number' | 'date' | 'email' | 'tel' | 'textarea'
  required?: boolean
  placeholder?: string
  maxLength?: number
}

const defaultFields: FieldConfig[] = [
  { key: 'account', label: 'Account Number', type: 'text', required: true },
  { key: 'amount', label: 'Amount', type: 'number', required: true },
  { key: 'date', label: 'Date', type: 'date' },
  { key: 'txn_type', label: 'Transaction Type', type: 'text' },
  { key: 'txn_id', label: 'Transaction ID', type: 'text' },
  { key: 'bank', label: 'Bank Name', type: 'text' },
  { key: 'account_holder', label: 'Account Holder', type: 'text' },
  { key: 'mobile_number', label: 'Mobile Number', type: 'tel', maxLength: 10 },
  { key: 'email', label: 'Email', type: 'email' },
  { key: 'ifsc_code', label: 'IFSC Code', type: 'text', maxLength: 11 },
  { key: 'branch', label: 'Branch', type: 'text' },
  { key: 'address', label: 'Address', type: 'textarea' },
  { key: 'remarks', label: 'Remarks', type: 'textarea' }
]

export const EditableTransactionNode: React.FC<NodeProps> = ({ data: rawData, id }) => {
  const { isDark } = useThemeContext()
  const data = rawData as EditableNodeData

  // State management
  const [isEditing, setIsEditing] = useState(false)
  const [isExpanded, setIsExpanded] = useState(true)
  const [editedData, setEditedData] = useState<Partial<EditableNodeData>>(data)
  const [customFields, setCustomFields] = useState<FieldConfig[]>([])
  const [showAddField, setShowAddField] = useState(false)
  const [newFieldName, setNewFieldName] = useState('')
  const [newFieldType, setNewFieldType] = useState<FieldConfig['type']>('text')

  // Refs for auto-focus
  const firstInputRef = useRef<HTMLInputElement>(null)

  // Node styling based on type
  const getNodeStyle = () => {
    const isMainTransaction = data.isMainTransaction
    const isSubTransaction = data.isSubTransaction
    const layer = Number(data.layer) || 0

    if (layer === 0) {
      return {
        bg: isDark ? 'bg-red-900/20 border-red-500/50' : 'bg-red-50 border-red-300',
        text: isDark ? 'text-red-300' : 'text-red-700',
        accent: 'text-red-500'
      }
    } else if (isMainTransaction) {
      return {
        bg: isDark ? 'bg-blue-900/20 border-blue-500/50' : 'bg-blue-50 border-blue-300',
        text: isDark ? 'text-blue-300' : 'text-blue-700',
        accent: 'text-blue-500'
      }
    } else {
      return {
        bg: isDark ? 'bg-gray-800/50 border-gray-600/50' : 'bg-gray-50 border-gray-300',
        text: isDark ? 'text-gray-300' : 'text-gray-700',
        accent: 'text-gray-500'
      }
    }
  }

  const nodeStyle = getNodeStyle()

  // Handle edit mode toggle
  const handleEditToggle = useCallback(() => {
    if (isEditing) {
      // Save changes
      if (data.onUpdate) {
        data.onUpdate(id, editedData)
      }
      setIsEditing(false)
    } else {
      // Enter edit mode
      setEditedData(data)
      setIsEditing(true)
      // Focus first input after state update
      setTimeout(() => {
        firstInputRef.current?.focus()
      }, 100)
    }
  }, [isEditing, editedData, data, id])

  // Handle field value change
  const handleFieldChange = useCallback((key: string, value: any) => {
    setEditedData((prev) => ({ ...prev, [key]: value }))
  }, [])

  // Add custom field
  const handleAddCustomField = useCallback(() => {
    if (newFieldName.trim()) {
      const newField: FieldConfig = {
        key: `custom_${newFieldName.toLowerCase().replace(/\s+/g, '_')}`,
        label: newFieldName,
        type: newFieldType
      }
      setCustomFields((prev) => [...prev, newField])
      setNewFieldName('')
      setShowAddField(false)
    }
  }, [newFieldName, newFieldType])

  // Remove custom field
  const handleRemoveCustomField = useCallback((fieldKey: string) => {
    setCustomFields((prev) => prev.filter((f) => f.key !== fieldKey))
    setEditedData((prev) => {
      const newData = { ...prev }
      delete newData[fieldKey as keyof EditableNodeData]
      return newData
    })
  }, [])

  // Render field input based on type
  const renderFieldInput = (field: FieldConfig, value: any, isFirst = false) => {
    const inputClasses = cn(
      'w-full px-2 py-1 text-xs rounded border',
      'focus:outline-none focus:ring-1 focus:ring-blue-500',
      isDark
        ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400'
        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
    )

    const commonProps = {
      value: value || '',
      onChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) =>
        handleFieldChange(field.key, e.target.value),
      placeholder: field.placeholder || field.label,
      maxLength: field.maxLength,
      className: inputClasses,
      ref: isFirst ? firstInputRef : undefined
    }

    switch (field.type) {
      case 'textarea':
        return <textarea {...commonProps} rows={2} className={cn(inputClasses, 'resize-none')} />
      case 'number':
        return <input {...commonProps} type="number" step="0.01" />
      default:
        return <input {...commonProps} type={field.type} />
    }
  }

  // Render field display
  const renderFieldDisplay = (field: FieldConfig, value: any) => {
    if (!value) return null

    return (
      <div className="flex justify-between items-start gap-2 py-1">
        <span
          className={cn(
            'text-xs font-medium flex-shrink-0',
            isDark ? 'text-gray-400' : 'text-gray-500'
          )}
        >
          {field.label}:
        </span>
        <span
          className={cn(
            'text-xs text-right break-words max-w-[160px]',
            isDark ? 'text-white' : 'text-gray-800'
          )}
        >
          {field.type === 'number' && typeof value === 'number'
            ? value.toLocaleString()
            : String(value)}
        </span>
      </div>
    )
  }

  // Get all fields to display (default + custom)
  const allFields = [...defaultFields, ...customFields]
  const visibleFields = allFields.filter(
    (field) => isEditing || editedData[field.key as keyof EditableNodeData]
  )

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className={cn(
        'relative rounded-lg border shadow-lg backdrop-blur-sm',
        'transition-all duration-200 min-w-[300px] max-w-[350px]',
        nodeStyle.bg,
        isEditing && 'ring-2 ring-blue-500/50'
      )}
    >
      {/* ReactFlow Handles */}
      <Handle
        type="target"
        position={Position.Top}
        className={cn(
          'w-3 h-3 border-2',
          isDark ? 'bg-blue-500 border-blue-400' : 'bg-blue-600 border-blue-500'
        )}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        className={cn(
          'w-3 h-3 border-2',
          isDark ? 'bg-blue-500 border-blue-400' : 'bg-blue-600 border-blue-500'
        )}
      />

      {/* Header */}
      <div className="flex items-center justify-between p-3 border-b border-gray-200/50 dark:border-gray-700/50">
        <div className="flex items-center gap-2 flex-1">
          <button
            onClick={() => setIsExpanded(!isExpanded)}
            className={cn('p-1 rounded hover:bg-black/10 dark:hover:bg-white/10', nodeStyle.accent)}
          >
            {isExpanded ? <FiChevronDown size={14} /> : <FiChevronRight size={14} />}
          </button>
          <h3 className={cn('font-medium text-sm truncate', nodeStyle.text)}>
            {data.label || `Node ${id.slice(0, 8)}`}
          </h3>
        </div>

        <div className="flex items-center gap-1">
          {data.hasChildren && (
            <button
              onClick={data.onToggleCollapse}
              className={cn(
                'p-1 rounded hover:bg-black/10 dark:hover:bg-white/10',
                nodeStyle.accent
              )}
              title={data.isCollapsed ? 'Expand children' : 'Collapse children'}
            >
              {data.isCollapsed ? <FiPlus size={14} /> : <FiMinus size={14} />}
            </button>
          )}

          <button
            onClick={handleEditToggle}
            className={cn(
              'p-1 rounded hover:bg-black/10 dark:hover:bg-white/10',
              isEditing ? 'text-green-500' : nodeStyle.accent
            )}
            title={isEditing ? 'Save changes' : 'Edit node'}
          >
            {isEditing ? <FiCheck size={14} /> : <FiEdit2 size={14} />}
          </button>

          {isEditing && (
            <button
              onClick={() => {
                setIsEditing(false)
                setEditedData(data)
              }}
              className="p-1 rounded hover:bg-black/10 dark:hover:bg-white/10 text-red-500"
              title="Cancel editing"
            >
              <FiX size={14} />
            </button>
          )}
        </div>
      </div>

      {/* Content */}
      <AnimatePresence>
        {isExpanded && (
          <motion.div
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: 'auto', opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            className="overflow-hidden"
          >
            <div className="p-3 space-y-2">
              {isEditing ? (
                // Edit mode
                <div className="space-y-3">
                  {allFields.map((field, index) => (
                    <div key={field.key} className="space-y-1">
                      <div className="flex items-center justify-between">
                        <label
                          className={cn(
                            'text-xs font-medium',
                            isDark ? 'text-gray-300' : 'text-gray-700'
                          )}
                        >
                          {field.label}
                          {field.required && <span className="text-red-500 ml-1">*</span>}
                        </label>
                        {field.key.startsWith('custom_') && (
                          <button
                            onClick={() => handleRemoveCustomField(field.key)}
                            className="text-red-500 hover:text-red-600"
                            title="Remove field"
                          >
                            <FiX size={12} />
                          </button>
                        )}
                      </div>
                      {renderFieldInput(
                        field,
                        editedData[field.key as keyof EditableNodeData],
                        index === 0
                      )}
                    </div>
                  ))}

                  {/* Add custom field */}
                  {showAddField ? (
                    <div className="space-y-2 p-2 border rounded border-dashed border-gray-300 dark:border-gray-600">
                      <input
                        type="text"
                        placeholder="Field name"
                        value={newFieldName}
                        onChange={(e) => setNewFieldName(e.target.value)}
                        className={cn(
                          'w-full px-2 py-1 text-xs rounded border',
                          isDark
                            ? 'bg-gray-800 border-gray-600 text-white'
                            : 'bg-white border-gray-300 text-gray-900'
                        )}
                      />
                      <select
                        value={newFieldType}
                        onChange={(e) => setNewFieldType(e.target.value as FieldConfig['type'])}
                        className={cn(
                          'w-full px-2 py-1 text-xs rounded border',
                          isDark
                            ? 'bg-gray-800 border-gray-600 text-white'
                            : 'bg-white border-gray-300 text-gray-900'
                        )}
                      >
                        <option value="text">Text</option>
                        <option value="number">Number</option>
                        <option value="date">Date</option>
                        <option value="email">Email</option>
                        <option value="tel">Phone</option>
                        <option value="textarea">Textarea</option>
                      </select>
                      <div className="flex gap-1">
                        <button
                          onClick={handleAddCustomField}
                          className="flex-1 px-2 py-1 text-xs bg-blue-600 text-white rounded hover:bg-blue-700"
                        >
                          Add
                        </button>
                        <button
                          onClick={() => {
                            setShowAddField(false)
                            setNewFieldName('')
                          }}
                          className="flex-1 px-2 py-1 text-xs bg-gray-500 text-white rounded hover:bg-gray-600"
                        >
                          Cancel
                        </button>
                      </div>
                    </div>
                  ) : (
                    <button
                      onClick={() => setShowAddField(true)}
                      className={cn(
                        'w-full px-2 py-1 text-xs border border-dashed rounded',
                        'hover:bg-black/5 dark:hover:bg-white/5',
                        'border-gray-300 dark:border-gray-600',
                        'text-gray-500 dark:text-gray-400'
                      )}
                    >
                      + Add Custom Field
                    </button>
                  )}
                </div>
              ) : (
                // Display mode
                <div className="space-y-1">
                  {visibleFields.map((field) =>
                    renderFieldDisplay(field, editedData[field.key as keyof EditableNodeData])
                  )}

                  {visibleFields.length === 0 && (
                    <div
                      className={cn(
                        'text-xs text-center py-2',
                        isDark ? 'text-gray-500' : 'text-gray-400'
                      )}
                    >
                      No data available. Click edit to add information.
                    </div>
                  )}
                </div>
              )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  )
}

// Enhanced layout utilities for better node positioning
export const calculateOptimalNodePosition = (
  existingNodes: Array<{ position: { x: number; y: number } }>,
  layer: number,
  nodeWidth = 350,
  nodeHeight = 200,
  horizontalSpacing = 50,
  verticalSpacing = 100
) => {
  const layerY = layer * (nodeHeight + verticalSpacing)

  // Find existing nodes in the same layer
  const sameLayerNodes = existingNodes.filter(
    (node) => Math.abs(node.position.y - layerY) < nodeHeight / 2
  )

  if (sameLayerNodes.length === 0) {
    return { x: 0, y: layerY }
  }

  // Sort by x position
  sameLayerNodes.sort((a, b) => a.position.x - b.position.x)

  // Find the best position to avoid overlap
  let bestX = 0
  for (let i = 0; i < sameLayerNodes.length; i++) {
    const node = sameLayerNodes[i]
    const minX = node.position.x + nodeWidth + horizontalSpacing

    if (i === sameLayerNodes.length - 1) {
      // Last node, place after it
      bestX = minX
      break
    } else {
      // Check if there's space before the next node
      const nextNode = sameLayerNodes[i + 1]
      const availableSpace = nextNode.position.x - minX

      if (availableSpace >= nodeWidth) {
        bestX = minX
        break
      }
    }
  }

  return { x: bestX, y: layerY }
}

export default EditableTransactionNode

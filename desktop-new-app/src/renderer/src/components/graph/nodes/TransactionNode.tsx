import React, { useState } from 'react'
import { <PERSON>de<PERSON><PERSON>, <PERSON><PERSON>, Position } from '@xyflow/react'
import { useThemeContext } from '../../../context/useThemeContext'
import { formatDate } from '../../../utils/dateUtils'

// Define transaction types
interface ConsolidatedTransaction {
  txn_id?: string
  transaction_id?: string
  amount?: string
  reference?: string
  date?: string
  txn_type?: string
  receiver_info?: string
  receiver_account?: string
  receiver_bank?: string
  sender_account?: string
  sender_bank?: string
  layer?: number
}

interface SubTransaction {
  type?: string
  date?: string
  amount?: string
  receiver_info?: string
}

// Define the expected data structure for transaction nodes
interface TransactionNodeData {
  label?: string
  account?: string
  amount?: string | number
  date?: string
  type?: string
  nodeType?: string
  layer?: string | number
  txn_id?: string
  transaction_id?: string
  sender_account?: string
  receiver_account?: string
  receiver_info?: string
  sender_bank?: string
  receiver_bank?: string
  bank?: string
  fraud_type?: string
  reference?: string
  isSenderNode?: boolean
  isMainTransaction?: boolean
  isSubTransaction?: boolean
  isSpecial?: boolean
  isConsolidated?: boolean
  consolidatedTransactions?: ConsolidatedTransaction[]
  hasSubTransactions?: boolean
  subTransactions?: SubTransaction[]
  hasChildren?: boolean
  isCollapsed?: boolean
  onToggleCollapse?: () => void
  transaction_count?: number
  total_amount?: string | number
  isVictim?: boolean
  visibleFields?: Set<string> // Field visibility control
  enriched_bank_data?: {
    original_bank_name: string
    bank_name: string
    branch_name: string
    branch_address: string
    branch_state: string
    branch_city: string
    branch_district: string
    ifsc_code: string
    is_ifsc_valid: boolean
    enrichment_status: 'success' | 'invalid_ifsc' | 'api_error' | 'no_ifsc'
    enriched_at: string
  }
  [key: string]: unknown // Allow additional properties
}

export const TransactionNode: React.FC<NodeProps> = ({ data: rawData }) => {
  const { isDark } = useThemeContext()
  const [isExpanded, setIsExpanded] = useState(true)
  const [isEditing, setIsEditing] = useState(false)

  const [isSubTransactionsExpanded, setIsSubTransactionsExpanded] = useState(false)

  // Type assertion for the data
  const data = rawData as TransactionNodeData

  // Helper function to check if a field should be visible
  const isFieldVisible = (fieldKey: string): boolean => {
    if (!data.visibleFields) return true // Show all fields if no filter is set
    return data.visibleFields.has(fieldKey)
  }

  // Determine node type - sender, main, sub, or special
  const isSenderNode = data.isSenderNode === true
  const isMainTransaction = data.isMainTransaction === true
  const isSubTransaction = data.isSubTransaction === true
  const isSpecialTxn = data.isSpecial === true
  const hasSubTransactions = data.hasSubTransactions === true
  const consolidatedTransactions = data.consolidatedTransactions || []

  // Simple styling based on transaction type
  const getNodeStyle = (): { bg: string; border: string; text: string } => {
    // Sender node - blue
    if (isSenderNode) {
      return {
        bg: isDark ? 'bg-blue-900/30' : 'bg-blue-50',
        border: isDark ? 'border-blue-700' : 'border-blue-300',
        text: isDark ? 'text-blue-400' : 'text-blue-700'
      }
    }

    // Special transaction - red
    if (isSpecialTxn) {
      return {
        bg: isDark ? 'bg-red-900/30' : 'bg-red-50',
        border: isDark ? 'border-red-700' : 'border-red-300',
        text: isDark ? 'text-red-400' : 'text-red-700'
      }
    }

    // Sub-transaction - purple
    if (isSubTransaction) {
      return {
        bg: isDark ? 'bg-purple-900/30' : 'bg-purple-50',
        border: isDark ? 'border-purple-700' : 'border-purple-300',
        text: isDark ? 'text-purple-400' : 'text-purple-700'
      }
    }

    // Main transaction - green
    if (isMainTransaction || data.txn_type === 'Money Transfer to') {
      return {
        bg: isDark ? 'bg-green-900/30' : 'bg-green-50',
        border: isDark ? 'border-green-700' : 'border-green-300',
        text: isDark ? 'text-green-400' : 'text-green-700'
      }
    }

    // Default - amber
    return {
      bg: isDark ? 'bg-amber-900/30' : 'bg-amber-50',
      border: isDark ? 'border-amber-700' : 'border-amber-300',
      text: isDark ? 'text-amber-400' : 'text-amber-700'
    }
  }

  const nodeStyle = getNodeStyle()

  // Function to toggle edit mode
  const handleEditToggle = (): void => {
    setIsEditing(!isEditing)
  }

  // Function to toggle expanded/collapsed state
  const toggleExpanded = (e: React.MouseEvent): void => {
    e.stopPropagation()
    setIsExpanded(!isExpanded)
  }

  // Function to toggle individual node collapse (hide/show children)
  const toggleNodeCollapse = (e: React.MouseEvent): void => {
    e.stopPropagation()
    if (data.onToggleCollapse) {
      data.onToggleCollapse()
    }
  }

  // Check if this node has children - use the hasChildren property from data
  const hasChildren = data.hasChildren === true

  return (
    <div
      className={`p-3 rounded-lg border shadow-md w-[280px] ${nodeStyle.bg} ${nodeStyle.border} transition-all duration-200`}
    >
      {/* ReactFlow Handles */}
      <Handle
        type="target"
        position={Position.Top}
        style={{ background: isDark ? '#6366f1' : '#3b82f6', width: '8px', height: '8px' }}
      />
      <Handle
        type="source"
        position={Position.Bottom}
        style={{ background: isDark ? '#6366f1' : '#3b82f6', width: '8px', height: '8px' }}
      />

      {/* Header with controls */}
      <div className="flex justify-between items-center mb-1">
        <div
          className={`font-medium text-base pb-1 border-b border-gray-200 dark:border-gray-700 ${nodeStyle.text} flex-grow cursor-pointer`}
          onClick={toggleExpanded}
          title={isExpanded ? 'Click to collapse' : 'Click to expand'}
        >
          {String(data.label || 'Transaction')}
          {!isExpanded && data.hasSubTransactions && data.subTransactions && (
            <span className={`ml-2 text-xs ${isDark ? 'text-purple-400' : 'text-purple-700'}`}>
              ({data.subTransactions.length} sub-txns
              {data.subTransactions[0]?.type &&
                `: ${data.subTransactions[0].type.substring(0, 15)}${data.subTransactions[0].type.length > 15 ? '...' : ''}`}
              )
            </span>
          )}
          <span className="ml-2 text-xs">
            {isExpanded ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-3 w-3 inline"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-3 w-3 inline"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5l7 7-7 7"
                />
              </svg>
            )}
          </span>
        </div>
        <div className="flex gap-1 ml-2">
          {/* Individual Node Collapse/Expand Button - only show for non-leaf nodes */}
          {hasChildren && (
            <button
              onClick={toggleNodeCollapse}
              className={`p-0.5 rounded ${isDark ? 'hover:bg-blue-900/50 text-blue-400' : 'hover:bg-blue-100 text-blue-600'}`}
              title={data.isCollapsed ? 'Expand children' : 'Collapse children'}
            >
              {data.isCollapsed ? (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 6v6m0 0v6m0-6h6m-6 0H6"
                  />
                </svg>
              ) : (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 12H6" />
                </svg>
              )}
            </button>
          )}
          <button
            onClick={handleEditToggle}
            className={`p-0.5 rounded ${isDark ? 'hover:bg-blue-900/50 text-blue-400' : 'hover:bg-blue-100 text-blue-600'}`}
            title={isEditing ? 'Save changes' : 'Edit transaction'}
          >
            {isEditing ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-4 w-4"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"
                />
              </svg>
            )}
          </button>
        </div>
      </div>

      {/* Always show essential information */}
      <div className="flex flex-col gap-2 text-sm">
        {/* Account Details - Show if field is visible */}
        {isFieldVisible('account') && (
          <div className="flex justify-between items-start">
            <span
              className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}
            >
              Account:
            </span>
            <span
              className={`${isDark ? 'text-white' : 'text-gray-800'} text-right break-words text-xs leading-tight max-w-[200px]`}
            >
              {String(data.account || data.id)}
            </span>
          </div>
        )}

        {/* Bank Name */}
        {data.bank && isFieldVisible('bank') && (
          <div className="flex justify-between items-start">
            <span
              className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}
            >
              Bank:
            </span>
            <span
              className={`${isDark ? 'text-white' : 'text-gray-800'} text-right break-words text-xs leading-tight max-w-[200px]`}
            >
              {String(data.bank)}
            </span>
          </div>
        )}

        {/* Enriched Bank Data - Show IFSC enrichment information */}
        {data.enriched_bank_data && data.enriched_bank_data.enrichment_status === 'success' && (
          <>
            {/* Branch Name */}
            {data.enriched_bank_data.branch_name && (
              <div className="flex justify-between items-start">
                <span
                  className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}
                >
                  Branch:
                </span>
                <span
                  className={`${isDark ? 'text-white' : 'text-gray-800'} text-right break-words text-xs leading-tight max-w-[200px]`}
                >
                  {String(data.enriched_bank_data.branch_name)}
                </span>
              </div>
            )}

            {/* IFSC Code */}
            {data.enriched_bank_data.ifsc_code && (
              <div className="flex justify-between items-start">
                <span
                  className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}
                >
                  IFSC:
                </span>
                <span
                  className={`${isDark ? 'text-white' : 'text-gray-800'} font-mono text-xs text-right break-words leading-tight max-w-[200px]`}
                >
                  {String(data.enriched_bank_data.ifsc_code)}
                </span>
              </div>
            )}

            {/* Location (City, State) */}
            {(data.enriched_bank_data.branch_city || data.enriched_bank_data.branch_state) && (
              <div className="flex justify-between items-start">
                <span
                  className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}
                >
                  Location:
                </span>
                <span
                  className={`${isDark ? 'text-white' : 'text-gray-800'} text-right break-words text-xs leading-tight max-w-[200px]`}
                >
                  {[data.enriched_bank_data.branch_city, data.enriched_bank_data.branch_state]
                    .filter(Boolean)
                    .join(', ')}
                </span>
              </div>
            )}
          </>
        )}

        {/* Amount - Show if field is visible */}
        {(data.amount || data.total_amount) && isFieldVisible('amount') && (
          <div className="flex justify-between items-start">
            <span
              className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}
            >
              {hasSubTransactions ? 'Total Amount:' : 'Amount:'}
            </span>
            <span className={`font-medium ${isDark ? 'text-white' : 'text-gray-800'} text-right`}>
              ₹{String(data.total_amount || data.amount)}
            </span>
          </div>
        )}

        {/* Transaction Count - Show for consolidated nodes */}
        {hasSubTransactions && data.transaction_count && data.transaction_count > 1 && (
          <div className="flex justify-between items-start">
            <span
              className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}
            >
              Transactions:
            </span>
            <span className={`font-medium ${isDark ? 'text-white' : 'text-gray-800'} text-right`}>
              {data.transaction_count}
            </span>
          </div>
        )}

        {/* Transaction ID - prominently display for Money Transfer nodes (only if NOT consolidated) with word wrap */}
        {(data.txn_type === 'Money Transfer to' || data.txn_type === 'Money transfer') &&
          (data.txn_id || data.transaction_id) &&
          !hasSubTransactions &&
          isFieldVisible('txn_id') && (
            <div className="flex justify-between items-start">
              <span
                className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}
              >
                Txn ID:
              </span>
              <span
                className={`${isDark ? 'text-white' : 'text-gray-800'} font-mono text-xs text-right break-all leading-tight max-w-[200px]`}
              >
                {String(data.txn_id || data.transaction_id)}
              </span>
            </div>
          )}

        {/* Additional details - only show if expanded */}
        {isExpanded && (
          <>
            {data.date && isFieldVisible('date') && (
              <div className="flex justify-between items-start">
                <span
                  className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}
                >
                  Date:
                </span>
                <span className={`${isDark ? 'text-white' : 'text-gray-800'} text-right`}>
                  {formatDate(data.date)}
                </span>
              </div>
            )}

            {data.txn_type && isFieldVisible('txn_type') && (
              <div className="flex justify-between items-start">
                <span
                  className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}
                >
                  Type:
                </span>
                <span className={`${isDark ? 'text-white' : 'text-gray-800'} text-right`}>
                  {String(data.txn_type)}
                </span>
              </div>
            )}

            {data.txn_id && !hasSubTransactions && (
              <div className="flex justify-between items-start">
                <span
                  className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}
                >
                  Txn ID:
                </span>
                <span
                  className={`${isDark ? 'text-white' : 'text-gray-800'} font-mono text-xs text-right break-all leading-tight max-w-[200px]`}
                >
                  {String(data.txn_id)}
                </span>
              </div>
            )}

            {data.sender_account && !data.isSenderNode && (
              <div className="flex justify-between items-start">
                <span
                  className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}
                >
                  From:
                </span>
                <span
                  className={`${isDark ? 'text-white' : 'text-gray-800'} text-right break-words text-xs leading-tight max-w-[200px]`}
                >
                  {String(data.sender_account)}
                </span>
              </div>
            )}

            {data.receiver_info && isFieldVisible('receiver_info') && (
              <div className="flex justify-between items-start">
                <span
                  className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}
                >
                  Reference:
                </span>
                <span
                  className={`${isDark ? 'text-white' : 'text-gray-800'} text-xs text-right break-words leading-tight max-w-[200px]`}
                >
                  {String(data.receiver_info)}
                </span>
              </div>
            )}

            {data.layer !== undefined && isFieldVisible('layer') && (
              <div className="flex justify-between items-start">
                <span
                  className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}
                >
                  Layer:
                </span>
                <span className={`${isDark ? 'text-white' : 'text-gray-800'} text-right`}>
                  {String(data.layer)}
                </span>
              </div>
            )}

            {/* Consolidated transactions section */}
            {hasSubTransactions &&
              consolidatedTransactions &&
              consolidatedTransactions.length > 0 && (
                <div className="mt-2 border-t border-gray-200 dark:border-gray-700 pt-2">
                  <div
                    className={`font-medium text-sm mb-0.5 ${nodeStyle.text} cursor-pointer flex items-center`}
                    onClick={(e) => {
                      e.stopPropagation()
                      setIsSubTransactionsExpanded(!isSubTransactionsExpanded)
                    }}
                    title={isSubTransactionsExpanded ? 'Click to collapse' : 'Click to expand'}
                  >
                    Sub-Transactions ({consolidatedTransactions.length})
                    <span className="ml-1 text-xs">
                      {isSubTransactionsExpanded ? (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-3 w-3 inline"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M19 9l-7 7-7-7"
                          />
                        </svg>
                      ) : (
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-3 w-3 inline"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M9 5l7 7-7 7"
                          />
                        </svg>
                      )}
                    </span>
                  </div>

                  {isSubTransactionsExpanded &&
                    consolidatedTransactions.map(
                      (consolidatedTxn: ConsolidatedTransaction, index: number) => (
                        <div
                          key={`consolidated-txn-${index}`}
                          className={`p-0.5 mb-0.5 rounded ${isDark ? 'bg-blue-900/30 border border-blue-700/50' : 'bg-blue-50 border border-blue-300'}`}
                        >
                          <div
                            className={`font-medium ${isDark ? 'text-blue-400' : 'text-blue-700'} border-b border-blue-200 dark:border-blue-800 pb-0.5 mb-0.5 text-xs`}
                          >
                            Transaction {index + 1}
                          </div>
                          <div className="flex flex-col gap-1 text-xs">
                            {(consolidatedTxn.txn_id || consolidatedTxn.transaction_id) && (
                              <div className="flex justify-between items-start">
                                <span
                                  className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}
                                >
                                  Txn ID:
                                </span>
                                <span
                                  className={`${isDark ? 'text-white' : 'text-gray-800'} font-mono text-xs text-right break-all leading-tight max-w-[150px]`}
                                >
                                  {consolidatedTxn.txn_id || consolidatedTxn.transaction_id}
                                </span>
                              </div>
                            )}
                            {consolidatedTxn.amount && (
                              <div className="flex justify-between items-start">
                                <span
                                  className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}
                                >
                                  Amount:
                                </span>
                                <span
                                  className={`font-medium ${isDark ? 'text-white' : 'text-gray-800'} text-right`}
                                >
                                  ₹{consolidatedTxn.amount}
                                </span>
                              </div>
                            )}
                            {consolidatedTxn.reference && (
                              <div className="flex justify-between items-start">
                                <span
                                  className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}
                                >
                                  Reference:
                                </span>
                                <span
                                  className={`${isDark ? 'text-white' : 'text-gray-800'} text-xs text-right break-words leading-tight max-w-[150px]`}
                                >
                                  {consolidatedTxn.reference}
                                </span>
                              </div>
                            )}
                            {consolidatedTxn.date && (
                              <div className="flex justify-between items-start">
                                <span
                                  className={`${isDark ? 'text-gray-400' : 'text-gray-500'} font-medium flex-shrink-0 mr-2`}
                                >
                                  Date:
                                </span>
                                <span
                                  className={`${isDark ? 'text-white' : 'text-gray-800'} text-right`}
                                >
                                  {consolidatedTxn.date}
                                </span>
                              </div>
                            )}
                          </div>
                        </div>
                      )
                    )}
                </div>
              )}
          </>
        )}
      </div>
    </div>
  )
}

export default TransactionNode

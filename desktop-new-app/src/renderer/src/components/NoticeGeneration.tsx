import React, { useState, useEffect } from 'react'
import { ComplaintData } from '../../../shared/api'
import { Card, CardContent } from './ui/Card'
import { Button } from './ui/Button'
import { useThemeContext } from '../context/useThemeContext'
import { cn } from '../lib/aceternity-utils'
import { Download, Plus, X } from 'lucide-react'
import { TemplateService } from '../services/templateService'

interface NoticeGenerationProps {
  complaint: ComplaintData
  onClose: () => void
}

const DEFAULT_NOTICE_POINTS = [
  'Account Holder Information',
  'Linked Accounts and Cards',
  'IP and Device Records',
  'Authentication and Access Logs',
  'Beneficiary and Transaction History',
  'Wallet/Bank account status and Balance',
  'E-commerce or Delivery Information',
  'ATM CCTV footage',
  'BSA Certification'
]

const NoticeGeneration: React.FC<NoticeGenerationProps> = ({ complaint, onClose }) => {
  const { isDark } = useThemeContext()
  const [selectedBanks, setSelectedBanks] = useState<string[]>([])
  const [selectedNoticePoints, setSelectedNoticePoints] = useState<string[]>([
    ...DEFAULT_NOTICE_POINTS
  ])
  const [customNoticePoint, setCustomNoticePoint] = useState('')
  const [isGenerating, setIsGenerating] = useState(false)
  const [hasTemplate, setHasTemplate] = useState(false)

  // Extract banks from complaint data
  const banks = React.useMemo(() => {
    if (!complaint.bank_notice_data || typeof complaint.bank_notice_data !== 'object') {
      return []
    }
    return Object.keys(complaint.bank_notice_data.banks || {})
  }, [complaint])

  // Check for template on component mount
  useEffect(() => {
    const checkTemplate = async (): Promise<void> => {
      try {
        const result = await window.api.template.get()
        setHasTemplate(result.success && !!result.template)
      } catch (error) {
        console.error('Failed to check template:', error)
        setHasTemplate(false)
      }
    }
    checkTemplate()
  }, [])

  const handleBankToggle = (bank: string): void => {
    setSelectedBanks((prev) =>
      prev.includes(bank) ? prev.filter((b) => b !== bank) : [...prev, bank]
    )
  }

  const handleNoticePointToggle = (point: string): void => {
    setSelectedNoticePoints((prev) =>
      prev.includes(point) ? prev.filter((p) => p !== point) : [...prev, point]
    )
  }

  const handleAddCustomPoint = (): void => {
    if (customNoticePoint.trim() && !selectedNoticePoints.includes(customNoticePoint.trim())) {
      setSelectedNoticePoints((prev) => [...prev, customNoticePoint.trim()])
      setCustomNoticePoint('')
    }
  }

  const handleRemoveCustomPoint = (point: string): void => {
    if (!DEFAULT_NOTICE_POINTS.includes(point)) {
      setSelectedNoticePoints((prev) => prev.filter((p) => p !== point))
    }
  }

  const handleGenerateNotices = async (): Promise<void> => {
    if (!hasTemplate) {
      alert('Please upload a template in Settings before generating notices.')
      return
    }

    if (selectedBanks.length === 0) {
      alert('Please select at least one bank.')
      return
    }

    if (selectedNoticePoints.length === 0) {
      alert('Please select at least one notice point.')
      return
    }

    setIsGenerating(true)
    try {
      // Generate Word documents using the template
      for (const bank of selectedBanks) {
        try {
          const documentBlob = await TemplateService.generateNotice(
            complaint,
            bank,
            selectedNoticePoints
          )

          const filename = `Notice_${complaint.id}_${bank.replace(/\s+/g, '_')}.docx`
          TemplateService.downloadDocument(documentBlob, filename)
        } catch (error) {
          console.error(`Failed to generate notice for ${bank}:`, error)
          // Fallback to text file if template processing fails
          const content = `Notice for ${bank}

Complaint Number: ${complaint.metadata?.complaint_number || complaint.title}
Date: ${new Date().toLocaleDateString()}

Notice Points:
${selectedNoticePoints.map((point) => `• ${point}`).join('\n')}

Generated by NCRP Matrix Desktop Application`

          const blob = new Blob([content], { type: 'text/plain' })
          const filename = `Notice_${complaint.id}_${bank.replace(/\s+/g, '_')}.txt`
          TemplateService.downloadDocument(blob, filename)
        }
      }
    } catch (error) {
      console.error('Notice generation error:', error)
      alert('Failed to generate notices. Please try again.')
    } finally {
      setIsGenerating(false)
    }
  }

  if (!hasTemplate) {
    return (
      <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50">
        <div
          className={cn(
            'p-6 rounded-lg backdrop-blur-md border shadow-lg max-w-md w-full mx-4',
            isDark
              ? 'bg-white/10 border-white/10 text-white'
              : 'bg-white/90 border-gray-200/50 text-gray-900'
          )}
        >
          <div className="flex items-center justify-between mb-4">
            <h3 className={cn('text-lg font-semibold', isDark ? 'text-white' : 'text-gray-900')}>
              Template Required
            </h3>
            <button
              onClick={onClose}
              className={cn(
                'p-1 rounded-lg transition-colors',
                isDark ? 'hover:bg-white/10' : 'hover:bg-gray-100'
              )}
            >
              <X className="h-4 w-4" />
            </button>
          </div>
          <p className={cn('mb-4 text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
            Please upload a notice template in Settings before generating notices.
          </p>
          <div className="flex gap-3 justify-end">
            <Button onClick={onClose} variant="outline" size="sm">
              Close
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4">
      <div
        className={cn(
          'w-full max-w-6xl h-[90vh] rounded-lg backdrop-blur-md border shadow-lg overflow-hidden',
          isDark ? 'bg-white/10 border-white/10' : 'bg-white/90 border-gray-200/50'
        )}
      >
        {/* Header */}
        <div
          className={cn(
            'flex items-center justify-between p-6 border-b',
            isDark ? 'border-white/10' : 'border-gray-200/50'
          )}
        >
          <div>
            <h2 className={cn('text-xl font-semibold', isDark ? 'text-white' : 'text-gray-900')}>
              Generate Bank Notices
            </h2>
            <p className={cn('text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
              Complaint: {String(complaint.metadata?.complaint_number || complaint.title)}
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Button
              onClick={handleGenerateNotices}
              disabled={isGenerating || selectedBanks.length === 0}
              className="flex items-center gap-2"
            >
              <Download className="h-4 w-4" />
              {isGenerating ? 'Generating...' : 'Generate Notices'}
            </Button>
            <button
              onClick={onClose}
              className={cn(
                'p-2 rounded-lg transition-colors',
                isDark ? 'hover:bg-white/10' : 'hover:bg-gray-100'
              )}
            >
              <X className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="flex h-full">
          {/* Left Panel - Banks */}
          <div className="w-1/2 p-6 border-r border-white/10">
            <Card
              className={cn(
                'h-full backdrop-blur-md border shadow-lg',
                isDark ? 'bg-white/5 border-white/10' : 'bg-white/70 border-gray-200/50'
              )}
            >
              <CardContent className="p-6 h-full flex flex-col">
                <div className="flex items-center justify-between mb-4">
                  <h3
                    className={cn('text-lg font-semibold', isDark ? 'text-white' : 'text-gray-900')}
                  >
                    Select Banks
                  </h3>
                  <button
                    onClick={() =>
                      setSelectedBanks(selectedBanks.length === banks.length ? [] : [...banks])
                    }
                    className={cn(
                      'text-xs px-3 py-1 rounded border transition-colors',
                      isDark
                        ? 'border-white/20 hover:bg-white/10 text-white'
                        : 'border-gray-300 hover:bg-gray-100 text-gray-900'
                    )}
                  >
                    {selectedBanks.length === banks.length ? 'Deselect All' : 'Select All'}
                  </button>
                </div>
                <div className="flex-1 overflow-y-auto space-y-2">
                  {banks.length === 0 ? (
                    <p className={cn('text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
                      No bank data available
                    </p>
                  ) : (
                    banks.map((bank) => (
                      <label key={bank} className="flex items-center gap-3 cursor-pointer">
                        <input
                          type="checkbox"
                          checked={selectedBanks.includes(bank)}
                          onChange={() => handleBankToggle(bank)}
                          className="rounded"
                        />
                        <span className={cn('text-sm', isDark ? 'text-white' : 'text-gray-900')}>
                          {bank}
                        </span>
                      </label>
                    ))
                  )}
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Panel - Notice Points */}
          <div className="w-1/2 p-6">
            <Card
              className={cn(
                'h-full backdrop-blur-md border shadow-lg',
                isDark ? 'bg-white/5 border-white/10' : 'bg-white/70 border-gray-200/50'
              )}
            >
              <CardContent className="p-6 h-full flex flex-col">
                <h3
                  className={cn(
                    'text-lg font-semibold mb-4',
                    isDark ? 'text-white' : 'text-gray-900'
                  )}
                >
                  Notice Points
                </h3>

                {/* Add Custom Point */}
                <div className="mb-4">
                  <div className="flex gap-2">
                    <input
                      type="text"
                      value={customNoticePoint}
                      onChange={(e) => setCustomNoticePoint(e.target.value)}
                      placeholder="Add custom notice point..."
                      className={cn(
                        'flex-1 p-2 text-sm rounded border focus:outline-none focus:ring-2 focus:ring-blue-500',
                        isDark
                          ? 'bg-white/10 border-white/20 text-white placeholder-gray-400'
                          : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                      )}
                      onKeyDown={(e) => e.key === 'Enter' && handleAddCustomPoint()}
                    />
                    <button
                      onClick={handleAddCustomPoint}
                      disabled={!customNoticePoint.trim()}
                      className={cn(
                        'p-2 rounded transition-colors disabled:opacity-50',
                        isDark
                          ? 'bg-blue-600 hover:bg-blue-700 text-white'
                          : 'bg-blue-500 hover:bg-blue-600 text-white'
                      )}
                    >
                      <Plus className="h-4 w-4" />
                    </button>
                  </div>
                </div>

                {/* Notice Points List */}
                <div className="flex-1 overflow-y-auto space-y-2">
                  {selectedNoticePoints.map((point) => (
                    <div key={point} className="flex items-center gap-3">
                      <input
                        type="checkbox"
                        checked={true}
                        onChange={() => handleNoticePointToggle(point)}
                        className="rounded"
                      />
                      <span
                        className={cn('text-sm flex-1', isDark ? 'text-white' : 'text-gray-900')}
                      >
                        {point}
                      </span>
                      {!DEFAULT_NOTICE_POINTS.includes(point) && (
                        <button
                          onClick={() => handleRemoveCustomPoint(point)}
                          className={cn(
                            'p-1 rounded transition-colors',
                            isDark
                              ? 'hover:bg-red-600/20 text-red-400'
                              : 'hover:bg-red-100 text-red-600'
                          )}
                        >
                          <X className="h-3 w-3" />
                        </button>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}

export default NoticeGeneration

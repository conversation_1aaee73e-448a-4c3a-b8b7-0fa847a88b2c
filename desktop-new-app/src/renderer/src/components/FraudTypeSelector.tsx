import React from 'react'
import { fraudTypes } from '../lib/utils'
import { useThemeContext } from '../context/useThemeContext'
import { cn } from '../lib/aceternity-utils'

interface FraudTypeSelectorProps {
  selectedType: string | null
  onTypeSelect: (type: string, extractionType: string) => void
}

const FraudTypeSelector: React.FC<FraudTypeSelectorProps> = ({ selectedType, onTypeSelect }) => {
  const { isDark } = useThemeContext()

  return (
    <div
      className={cn(
        'p-4 rounded-lg border backdrop-blur-md shadow-lg',
        isDark ? 'bg-white/5 border-white/10' : 'bg-white/70 border-gray-200/50'
      )}
    >
      <h3 className={cn('text-sm font-medium mb-3', isDark ? 'text-white' : 'text-gray-900')}>
        Fraud Type
      </h3>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
        {fraudTypes.map((type) => (
          <button
            key={type.id}
            onClick={() => onTypeSelect(type.id, type.id)}
            className={cn(
              'p-4 rounded-lg text-left transition-all duration-200 border',
              selectedType === type.id
                ? isDark
                  ? 'bg-blue-600 text-white border-blue-500 shadow-lg shadow-blue-500/25'
                  : 'bg-blue-500 text-white border-blue-400 shadow-lg shadow-blue-500/25'
                : isDark
                  ? 'bg-white/5 hover:bg-white/10 text-white border-white/10 hover:border-white/20'
                  : 'bg-white hover:bg-gray-50 text-gray-900 border-gray-200 hover:border-gray-300'
            )}
          >
            <div className="flex items-center gap-3">
              <span className="text-lg">{type.icon}</span>
              <div>
                <div className="font-semibold text-sm">{type.name}</div>
                <div
                  className={cn(
                    'text-xs mt-1',
                    selectedType === type.id
                      ? 'text-white/80'
                      : isDark
                        ? 'text-gray-400'
                        : 'text-gray-600'
                  )}
                >
                  {type.description}
                </div>
              </div>
            </div>
          </button>
        ))}
      </div>
    </div>
  )
}

export default FraudTypeSelector

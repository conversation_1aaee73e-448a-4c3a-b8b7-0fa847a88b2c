import React, { ReactNode } from 'react'
import { motion } from 'motion/react'
import { cn } from '../../lib/aceternity-utils'
import { useThemeContext } from '../../context/useThemeContext'
import LampBackground from '../LampBackground'

interface FullWidthLayoutProps {
  children: ReactNode
  className?: string
  enableLampBackground?: boolean
  enableGlassmorphism?: boolean
  maxWidth?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '4xl' | '6xl' | '7xl'
  padding?: 'none' | 'sm' | 'md' | 'lg' | 'xl'
  enableAnimation?: boolean
}

const maxWidthClasses = {
  none: 'max-w-none',
  sm: 'max-w-sm',
  md: 'max-w-md',
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  '2xl': 'max-w-2xl',
  '4xl': 'max-w-4xl',
  '6xl': 'max-w-6xl',
  '7xl': 'max-w-7xl'
}

const paddingClasses = {
  none: 'p-0',
  sm: 'p-2',
  md: 'p-4',
  lg: 'p-6',
  xl: 'p-8'
}

export const FullWidthLayout: React.FC<FullWidthLayoutProps> = ({
  children,
  className,
  enableLampBackground = false,
  enableGlassmorphism = true,
  maxWidth = 'none',
  padding = 'lg',
  enableAnimation = true
}) => {
  const { isDark } = useThemeContext()

  const content = (
    <motion.div
      initial={enableAnimation ? { opacity: 0, y: 20 } : undefined}
      animate={enableAnimation ? { opacity: 1, y: 0 } : undefined}
      transition={enableAnimation ? { duration: 0.5 } : undefined}
      className={cn(
        'min-h-screen w-full',
        // Full width utilization
        'flex flex-col',
        // Glassmorphism background
        enableGlassmorphism && [
          'backdrop-blur-sm',
          isDark
            ? 'bg-gradient-to-br from-slate-900/50 via-slate-800/30 to-slate-900/50'
            : 'bg-gradient-to-br from-white/50 via-gray-50/30 to-white/50'
        ],
        className
      )}
    >
      <div
        className={cn('flex-1 w-full mx-auto', maxWidthClasses[maxWidth], paddingClasses[padding])}
      >
        {children}
      </div>
    </motion.div>
  )

  if (enableLampBackground) {
    return <LampBackground>{content}</LampBackground>
  }

  return content
}

// Responsive grid layout for full-width utilization
interface ResponsiveGridProps {
  children: ReactNode
  columns?: {
    sm?: number
    md?: number
    lg?: number
    xl?: number
    '2xl'?: number
  }
  gap?: 'sm' | 'md' | 'lg' | 'xl'
  className?: string
}

const gapClasses = {
  sm: 'gap-2',
  md: 'gap-4',
  lg: 'gap-6',
  xl: 'gap-8'
}

export const ResponsiveGrid: React.FC<ResponsiveGridProps> = ({
  children,
  columns = { sm: 1, md: 2, lg: 3, xl: 4, '2xl': 5 },
  gap = 'lg',
  className
}) => {
  const gridClasses = cn(
    'grid w-full',
    gapClasses[gap],
    // Responsive columns
    columns.sm && `grid-cols-${columns.sm}`,
    columns.md && `md:grid-cols-${columns.md}`,
    columns.lg && `lg:grid-cols-${columns.lg}`,
    columns.xl && `xl:grid-cols-${columns.xl}`,
    columns['2xl'] && `2xl:grid-cols-${columns['2xl']}`,
    className
  )

  return <div className={gridClasses}>{children}</div>
}

// Full-width container with proper spacing
interface FullWidthContainerProps {
  children: ReactNode
  className?: string
  enableGlassmorphism?: boolean
  enableBorder?: boolean
  enableShadow?: boolean
}

export const FullWidthContainer: React.FC<FullWidthContainerProps> = ({
  children,
  className,
  enableGlassmorphism = true,
  enableBorder = true,
  enableShadow = true
}) => {
  const { isDark } = useThemeContext()

  return (
    <div
      className={cn(
        'w-full rounded-xl overflow-hidden',
        // Glassmorphism styling
        enableGlassmorphism && ['backdrop-blur-md', isDark ? 'bg-white/5' : 'bg-white/70'],
        // Border
        enableBorder && ['border', isDark ? 'border-white/10' : 'border-gray-200/50'],
        // Shadow
        enableShadow && ['shadow-xl', isDark ? 'shadow-black/20' : 'shadow-gray-900/10'],
        className
      )}
    >
      {children}
    </div>
  )
}

// Sidebar layout for better space utilization
interface SidebarLayoutProps {
  sidebar: ReactNode
  main: ReactNode
  sidebarWidth?: 'sm' | 'md' | 'lg' | 'xl'
  sidebarPosition?: 'left' | 'right'
  collapsible?: boolean
  defaultCollapsed?: boolean
  className?: string
}

const sidebarWidthClasses = {
  sm: 'w-64',
  md: 'w-72',
  lg: 'w-80',
  xl: 'w-96'
}

export const SidebarLayout: React.FC<SidebarLayoutProps> = ({
  sidebar,
  main,
  sidebarWidth = 'md',
  sidebarPosition = 'left',
  collapsible = false,
  defaultCollapsed = false,
  className
}) => {
  const [isCollapsed, setIsCollapsed] = React.useState(defaultCollapsed)
  const { isDark } = useThemeContext()

  const sidebarContent = (
    <motion.aside
      initial={false}
      animate={{
        width: isCollapsed ? '4rem' : undefined
      }}
      className={cn(
        'flex-shrink-0 h-full',
        !isCollapsed && sidebarWidthClasses[sidebarWidth],
        isCollapsed && 'w-16',
        // Glassmorphism styling
        'backdrop-blur-md border-r',
        isDark ? 'bg-white/5 border-white/10' : 'bg-white/70 border-gray-200/50'
      )}
    >
      {collapsible && (
        <button
          onClick={() => setIsCollapsed(!isCollapsed)}
          className={cn(
            'absolute top-4 z-10 p-2 rounded-lg',
            'backdrop-blur-sm border',
            isDark
              ? 'bg-white/10 border-white/20 text-white hover:bg-white/20'
              : 'bg-white/80 border-gray-200 text-gray-900 hover:bg-white',
            sidebarPosition === 'left' ? 'right-4' : 'left-4'
          )}
        >
          <svg
            className={cn(
              'w-4 h-4 transition-transform',
              isCollapsed && sidebarPosition === 'left' && 'rotate-180',
              isCollapsed && sidebarPosition === 'right' && 'rotate-0'
            )}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d={sidebarPosition === 'left' ? 'M15 19l-7-7 7-7' : 'M9 5l7 7-7 7'}
            />
          </svg>
        </button>
      )}
      <div className={cn('h-full overflow-hidden', isCollapsed && 'px-2 py-4')}>{sidebar}</div>
    </motion.aside>
  )

  const mainContent = <main className="flex-1 h-full overflow-auto">{main}</main>

  return (
    <div className={cn('flex h-full w-full', className)}>
      {sidebarPosition === 'left' && sidebarContent}
      {mainContent}
      {sidebarPosition === 'right' && sidebarContent}
    </div>
  )
}

// Full-width section with proper spacing
interface FullWidthSectionProps {
  children: ReactNode
  title?: string
  subtitle?: string
  actions?: ReactNode
  className?: string
  contentClassName?: string
  enableGlassmorphism?: boolean
}

export const FullWidthSection: React.FC<FullWidthSectionProps> = ({
  children,
  title,
  subtitle,
  actions,
  className,
  contentClassName,
  enableGlassmorphism = true
}) => {
  const { isDark } = useThemeContext()

  return (
    <section className={cn('w-full', className)}>
      {(title || subtitle || actions) && (
        <div className="flex items-center justify-between mb-6">
          <div>
            {title && (
              <h2 className={cn('text-2xl font-bold', isDark ? 'text-white' : 'text-gray-900')}>
                {title}
              </h2>
            )}
            {subtitle && (
              <p className={cn('text-sm mt-1', isDark ? 'text-gray-400' : 'text-gray-600')}>
                {subtitle}
              </p>
            )}
          </div>
          {actions && <div className="flex items-center gap-2">{actions}</div>}
        </div>
      )}

      <FullWidthContainer enableGlassmorphism={enableGlassmorphism} className={contentClassName}>
        {children}
      </FullWidthContainer>
    </section>
  )
}

export default FullWidthLayout

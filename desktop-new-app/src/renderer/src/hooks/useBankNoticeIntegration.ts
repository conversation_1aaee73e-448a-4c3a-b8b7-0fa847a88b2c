/**
 * Bank Notice Integration Hook
 * 
 * This hook provides real-time bank notice integration functionality
 * for components that need to work with bank notice data.
 */

import { useState, useEffect, useCallback } from 'react'
import { ComplaintData } from '../../../shared/api'
import { BankNoticeSyncService } from '../services/bankNoticeSync'
import { realTimeSyncService, DataSyncEvent } from '../services/realTimeSyncService'

interface BankNoticeStats {
  totalBanks: number
  totalTransactions: number
  totalAmount: number
  bankBreakdown: Array<{
    bankName: string
    transactionCount: number
    totalAmount: number
    percentage: number
  }>
}

interface BankNoticeValidation {
  isConsistent: boolean
  issues: string[]
  suggestions: string[]
}

interface UseBankNoticeIntegrationReturn {
  // Bank notice data
  bankNoticeData: Record<string, unknown> | null
  
  // Statistics
  stats: BankNoticeStats | null
  
  // Validation
  validation: BankNoticeValidation | null
  
  // Loading states
  loading: boolean
  updating: boolean
  
  // Error state
  error: string | null
  
  // Actions
  refreshBankNoticeData: () => Promise<void>
  validateConsistency: () => Promise<BankNoticeValidation>
  autoFixInconsistencies: () => Promise<string[]>
  updateTransactionBank: (transactionId: string, oldBank: string, newBank: string) => Promise<void>
  bulkUpdateTransactions: (updates: Array<{ transactionId: string; updates: Record<string, unknown> }>) => Promise<void>
}

export const useBankNoticeIntegration = (
  complaintId: string,
  complaintData: ComplaintData | null
): UseBankNoticeIntegrationReturn => {
  const [bankNoticeData, setBankNoticeData] = useState<Record<string, unknown> | null>(null)
  const [stats, setStats] = useState<BankNoticeStats | null>(null)
  const [validation, setValidation] = useState<BankNoticeValidation | null>(null)
  const [loading, setLoading] = useState(false)
  const [updating, setUpdating] = useState(false)
  const [error, setError] = useState<string | null>(null)

  // Initialize bank notice data when complaint data changes
  useEffect(() => {
    if (complaintData) {
      setBankNoticeData(complaintData.bank_notice_data || null)
      
      // Calculate stats
      try {
        const calculatedStats = BankNoticeSyncService.getBankNoticeStats(complaintData)
        setStats(calculatedStats)
      } catch (err) {
        console.error('[Bank Notice Integration] Error calculating stats:', err)
        setStats(null)
      }
    } else {
      setBankNoticeData(null)
      setStats(null)
      setValidation(null)
    }
  }, [complaintData])

  // Subscribe to real-time updates
  useEffect(() => {
    if (!complaintId) return

    console.log(`[Bank Notice Integration] Setting up subscription for complaint ${complaintId}`)

    const unsubscribe = realTimeSyncService.subscribeToComplaint(
      complaintId,
      (event: DataSyncEvent) => {
        if (event.type === 'bank_notice_updated' || event.type === 'transaction_updated') {
          console.log('[Bank Notice Integration] Received sync event:', event.type)
          // Use a ref to avoid stale closure issues
          refreshBankNoticeData()
        }
      }
    )

    return () => {
      console.log(`[Bank Notice Integration] Cleaning up subscription for complaint ${complaintId}`)
      unsubscribe()
    }
  }, [complaintId, refreshBankNoticeData])

  // Refresh bank notice data from database
  const refreshBankNoticeData = useCallback(async (): Promise<void> => {
    if (!complaintId) return

    setLoading(true)
    setError(null)

    try {
      const updatedComplaint = await window.electronAPI.database.getComplaint(complaintId)
      if (updatedComplaint) {
        setBankNoticeData(updatedComplaint.bank_notice_data || null)
        
        // Recalculate stats
        const calculatedStats = BankNoticeSyncService.getBankNoticeStats(updatedComplaint)
        setStats(calculatedStats)
      }
    } catch (err) {
      console.error('[Bank Notice Integration] Error refreshing bank notice data:', err)
      setError(`Failed to refresh bank notice data: ${err}`)
    } finally {
      setLoading(false)
    }
  }, [complaintId])

  // Validate bank notice consistency
  const validateConsistency = useCallback(async (): Promise<BankNoticeValidation> => {
    if (!complaintData) {
      const emptyValidation = { isConsistent: false, issues: ['No complaint data available'], suggestions: [] }
      setValidation(emptyValidation)
      return emptyValidation
    }

    setLoading(true)
    setError(null)

    try {
      const validationResult = BankNoticeSyncService.validateBankNoticeConsistency(complaintData)
      setValidation(validationResult)
      return validationResult
    } catch (err) {
      console.error('[Bank Notice Integration] Error validating consistency:', err)
      const errorValidation = { 
        isConsistent: false, 
        issues: [`Validation error: ${err}`], 
        suggestions: ['Check bank notice data structure'] 
      }
      setValidation(errorValidation)
      setError(`Failed to validate consistency: ${err}`)
      return errorValidation
    } finally {
      setLoading(false)
    }
  }, [complaintData])

  // Auto-fix inconsistencies
  const autoFixInconsistencies = useCallback(async (): Promise<string[]> => {
    if (!complaintData || !complaintId) {
      return ['No complaint data available for auto-fix']
    }

    setUpdating(true)
    setError(null)

    try {
      const { fixedComplaintData, fixesApplied } = BankNoticeSyncService.autoFixBankNoticeData(complaintData)
      
      // Save the fixed data to database
      await window.electronAPI.database.updateComplaint(complaintId, {
        bank_notice_data: fixedComplaintData.bank_notice_data,
        updated_at: new Date().toISOString()
      })

      // Update local state
      setBankNoticeData(fixedComplaintData.bank_notice_data || null)
      
      // Recalculate stats
      const calculatedStats = BankNoticeSyncService.getBankNoticeStats(fixedComplaintData)
      setStats(calculatedStats)

      // Emit sync event
      realTimeSyncService.emit({
        type: 'bank_notice_updated',
        complaint_id: complaintId,
        timestamp: new Date().toISOString(),
        source: 'system'
      })

      return fixesApplied
    } catch (err) {
      console.error('[Bank Notice Integration] Error auto-fixing inconsistencies:', err)
      setError(`Failed to auto-fix inconsistencies: ${err}`)
      return [`Error during auto-fix: ${err}`]
    } finally {
      setUpdating(false)
    }
  }, [complaintData, complaintId])

  // Update transaction bank and sync
  const updateTransactionBank = useCallback(async (
    transactionId: string,
    oldBank: string,
    newBank: string
  ): Promise<void> => {
    if (!complaintData || !complaintId) return

    setUpdating(true)
    setError(null)

    try {
      const updatedComplaintData = BankNoticeSyncService.autoSyncOnBankChange(
        complaintData,
        transactionId,
        oldBank,
        newBank
      )

      // Save to database
      await window.electronAPI.database.updateComplaint(complaintId, {
        layer_transactions: updatedComplaintData.layer_transactions,
        bank_notice_data: updatedComplaintData.bank_notice_data,
        updated_at: new Date().toISOString()
      })

      // Update local state
      setBankNoticeData(updatedComplaintData.bank_notice_data || null)
      
      // Recalculate stats
      const calculatedStats = BankNoticeSyncService.getBankNoticeStats(updatedComplaintData)
      setStats(calculatedStats)

      // Emit sync event
      realTimeSyncService.emit({
        type: 'transaction_updated',
        complaint_id: complaintId,
        transaction_id: transactionId,
        data: { receiver_bank: newBank },
        timestamp: new Date().toISOString(),
        source: 'table'
      })
    } catch (err) {
      console.error('[Bank Notice Integration] Error updating transaction bank:', err)
      setError(`Failed to update transaction bank: ${err}`)
    } finally {
      setUpdating(false)
    }
  }, [complaintData, complaintId])

  // Bulk update transactions and sync
  const bulkUpdateTransactions = useCallback(async (
    updates: Array<{ transactionId: string; updates: Record<string, unknown> }>
  ): Promise<void> => {
    if (!complaintData || !complaintId) return

    setUpdating(true)
    setError(null)

    try {
      const updatedComplaintData = BankNoticeSyncService.bulkUpdateTransactionsAndSync(
        complaintData,
        updates as any
      )

      // Save to database
      await window.electronAPI.database.updateComplaint(complaintId, {
        layer_transactions: updatedComplaintData.layer_transactions,
        bank_notice_data: updatedComplaintData.bank_notice_data,
        updated_at: new Date().toISOString()
      })

      // Update local state
      setBankNoticeData(updatedComplaintData.bank_notice_data || null)
      
      // Recalculate stats
      const calculatedStats = BankNoticeSyncService.getBankNoticeStats(updatedComplaintData)
      setStats(calculatedStats)

      // Emit sync event
      realTimeSyncService.emit({
        type: 'bank_notice_updated',
        complaint_id: complaintId,
        timestamp: new Date().toISOString(),
        source: 'table'
      })
    } catch (err) {
      console.error('[Bank Notice Integration] Error bulk updating transactions:', err)
      setError(`Failed to bulk update transactions: ${err}`)
    } finally {
      setUpdating(false)
    }
  }, [complaintData, complaintId])

  return {
    bankNoticeData,
    stats,
    validation,
    loading,
    updating,
    error,
    refreshBankNoticeData,
    validateConsistency,
    autoFixInconsistencies,
    updateTransactionBank,
    bulkUpdateTransactions
  }
}

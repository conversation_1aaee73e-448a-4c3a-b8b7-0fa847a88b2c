/**
 * Device fingerprinting service for simplified authentication
 * Generates hardware-level device identifiers that remain constant across restarts
 * Works with simplified token-based authentication system
 */

export interface DeviceInfo {
  fingerprint: string
  deviceId: string
  hardwareSpecs: {
    platform: string
    arch: string
    cpuCores: number
    totalMemory: number
    userAgent: string
    screenResolution: string
    timezone: string
    language: string
  }
  osInfo: {
    platform: string
    version: string
    hostname: string
  }
  uniqueIdentifiers: {
    machineId?: string
    hardwareUuid?: string
    networkInterfaces: string[]
  }
}

class DeviceService {
  private static instance: DeviceService
  private deviceInfo: DeviceInfo | null = null

  private constructor() {}

  public static getInstance(): DeviceService {
    if (!DeviceService.instance) {
      DeviceService.instance = new DeviceService()
    }
    return DeviceService.instance
  }

  /**
   * Generate comprehensive device fingerprint
   */
  public async generateDeviceFingerprint(): Promise<DeviceInfo> {
    if (this.deviceInfo) {
      return this.deviceInfo
    }

    try {
      // Get system information from Electron main process
      const systemInfo = await this.getSystemInfo()
      
      // Get browser/renderer information
      const browserInfo = this.getBrowserInfo()
      
      // Combine all information - exclude timestamp for consistency
      const deviceData = {
        ...systemInfo,
        ...browserInfo
        // Note: Removed timestamp to ensure consistent fingerprints across app restarts
      }

      // Generate unique fingerprint hash
      const fingerprint = await this.generateFingerprint(deviceData)
      
      this.deviceInfo = {
        fingerprint,
        deviceId: fingerprint.substring(0, 16), // Short device ID
        hardwareSpecs: {
          platform: systemInfo.platform || 'unknown',
          arch: systemInfo.arch || 'unknown',
          cpuCores: systemInfo.cpuCores || 0,
          totalMemory: systemInfo.totalMemory || 0,
          userAgent: browserInfo.userAgent,
          screenResolution: browserInfo.screenResolution,
          timezone: browserInfo.timezone,
          language: browserInfo.language
        },
        osInfo: {
          platform: systemInfo.platform || 'unknown',
          version: systemInfo.osVersion || 'unknown',
          hostname: systemInfo.hostname || 'unknown'
        },
        uniqueIdentifiers: {
          machineId: systemInfo.machineId,
          hardwareUuid: systemInfo.hardwareUuid,
          networkInterfaces: systemInfo.networkInterfaces || []
        }
      }

      return this.deviceInfo
    } catch (error) {
      console.error('Failed to generate device fingerprint:', error)
      
      // Fallback fingerprint using only browser data - exclude timestamp for consistency
      const browserInfo = this.getBrowserInfo()
      const fallbackData = {
        ...browserInfo,
        fallback: true,
        // Note: Removed timestamp to ensure consistent fingerprints across app restarts
        hardwareConcurrency: navigator.hardwareConcurrency || 0
      }

      const fingerprint = await this.generateFingerprint(fallbackData)
      
      this.deviceInfo = {
        fingerprint,
        deviceId: fingerprint.substring(0, 16),
        hardwareSpecs: {
          platform: 'unknown',
          arch: 'unknown',
          cpuCores: navigator.hardwareConcurrency || 0,
          totalMemory: 0,
          userAgent: browserInfo.userAgent,
          screenResolution: browserInfo.screenResolution,
          timezone: browserInfo.timezone,
          language: browserInfo.language
        },
        osInfo: {
          platform: 'unknown',
          version: 'unknown',
          hostname: 'unknown'
        },
        uniqueIdentifiers: {
          networkInterfaces: []
        }
      }

      return this.deviceInfo
    }
  }

  /**
   * Get system information from Electron main process
   */
  private async getSystemInfo(): Promise<any> {
    try {
      // Check if we're in Electron environment
      if (window.api?.system?.getSystemInfo) {
        return await window.api.system.getSystemInfo()
      }
      
      // Fallback for non-Electron environments
      return {
        platform: navigator.platform,
        arch: 'unknown',
        cpuCores: navigator.hardwareConcurrency || 0,
        totalMemory: 0,
        osVersion: 'unknown',
        hostname: 'unknown',
        machineId: null,
        hardwareUuid: null,
        networkInterfaces: []
      }
    } catch (error) {
      console.warn('Failed to get system info from main process:', error)
      return {}
    }
  }

  /**
   * Get browser/renderer information
   */
  private getBrowserInfo(): any {
    return {
      userAgent: navigator.userAgent,
      screenResolution: `${screen.width}x${screen.height}`,
      timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
      language: navigator.language,
      cookieEnabled: navigator.cookieEnabled,
      doNotTrack: navigator.doNotTrack,
      hardwareConcurrency: navigator.hardwareConcurrency || 0,
      maxTouchPoints: navigator.maxTouchPoints || 0,
      vendor: navigator.vendor,
      webdriver: (navigator as any).webdriver || false
    }
  }

  /**
   * Generate SHA-256 hash from device data using Web Crypto API
   */
  private async generateFingerprint(data: any): Promise<string> {
    const dataString = JSON.stringify(data, Object.keys(data).sort())
    const encoder = new TextEncoder()
    const dataBuffer = encoder.encode(dataString)

    // Use Web Crypto API for hashing in renderer process
    const hashBuffer = await crypto.subtle.digest('SHA-256', dataBuffer)
    const hashArray = Array.from(new Uint8Array(hashBuffer))
    return hashArray.map(b => b.toString(16).padStart(2, '0')).join('')
  }

  /**
   * Get cached device info
   */
  public getDeviceInfo(): DeviceInfo | null {
    return this.deviceInfo
  }

  /**
   * Clear cached device info (for testing)
   */
  public clearCache(): void {
    this.deviceInfo = null
  }

  /**
   * Validate device fingerprint format
   */
  public static isValidFingerprint(fingerprint: string): boolean {
    return /^[a-f0-9]{64}$/i.test(fingerprint)
  }
}

export const deviceService = DeviceService.getInstance()

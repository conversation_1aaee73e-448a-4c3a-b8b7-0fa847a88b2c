/**
 * Bank Notice Synchronization Service
 * 
 * This service automatically updates bank notice data when transactions are modified
 * in either the table view or graph view, ensuring real-time synchronization.
 */

import { ComplaintData } from '../../../shared/api'
import { DataTransformationService } from './dataTransformationService'

// Bank notice data structure
interface BankNoticeData {
  banks: Record<string, BankNoticeEntry>
  total_banks: number
  total_amount: number
  last_updated: string
}

interface BankNoticeEntry {
  bank_name: string
  enhanced_bank_name?: string
  transactions: CoreTransactionData[]
  total_amount: number
  transaction_count: number
  enhanced_bank_data?: EnhancedBankData
}

interface CoreTransactionData {
  id: string
  txn_id?: string
  transaction_id?: string
  fraud_type: string
  txn_type: string
  amount: string
  date: string
  reference: string
  layer: number
  sender_account: string
  sender_transaction_id: string
  sender_bank: string
  receiver_account: string
  receiver_transaction_id: string
  receiver_bank: string
  receiver_ifsc: string
  receiver_info: string
  created_at: string
  updated_at: string
}

interface EnhancedBankData {
  original_bank_name?: string
  enhanced_bank_name: string
  bank: string
  branch: string
  address: string
  city: string
  state: string
  district: string
  ifsc: string
  enhancement_status: 'success' | 'no_ifsc' | 'invalid_ifsc' | 'api_error'
  enhanced_at: string
}

export class BankNoticeSyncService {
  /**
   * Update bank notice data based on current transaction data
   */
  static updateBankNoticeData(complaintData: ComplaintData): ComplaintData {
    try {
      // Get all transactions from the complaint
      const transactions = this.extractAllTransactions(complaintData)
      
      // Generate new bank notice data
      const bankNoticeData = this.generateBankNoticeData(transactions)
      
      // Return updated complaint data
      return {
        ...complaintData,
        bank_notice_data: bankNoticeData
      }
    } catch (error) {
      console.error('[Bank Notice Sync] Error updating bank notice data:', error)
      return complaintData
    }
  }

  /**
   * Extract all transactions from complaint data (handles both unified and legacy formats)
   */
  private static extractAllTransactions(complaintData: ComplaintData): CoreTransactionData[] {
    const transactions: CoreTransactionData[] = []

    // Check if data is in unified format
    if (complaintData.layer_transactions && DataTransformationService.isUnifiedFormat(complaintData.layer_transactions)) {
      // Unified format
      const unifiedData = complaintData.layer_transactions as any
      Object.values(unifiedData.layers || {}).forEach((layer: any) => {
        if (layer.transactions && Array.isArray(layer.transactions)) {
          layer.transactions.forEach((transaction: any) => {
            if (transaction.core && transaction.include_in_bank_notice) {
              transactions.push(transaction.core)
            }
          })
        }
      })
    } else if (complaintData.layer_transactions) {
      // Legacy format
      Object.values(complaintData.layer_transactions).forEach((layerTransactions: any) => {
        if (Array.isArray(layerTransactions)) {
          layerTransactions.forEach((txn: any) => {
            // Convert legacy transaction to core format
            const coreTransaction: CoreTransactionData = {
              id: txn.id || `txn_${Date.now()}_${Math.random()}`,
              txn_id: txn.txn_id || txn.transaction_id,
              transaction_id: txn.transaction_id || txn.txn_id,
              fraud_type: txn.fraud_type || '',
              txn_type: txn.txn_type || txn.type || '',
              amount: txn.amount || '',
              date: txn.date || '',
              reference: txn.reference || '',
              layer: parseInt(txn.layer) || 0,
              sender_account: txn.sender_account || '',
              sender_transaction_id: txn.sender_transaction_id || '',
              sender_bank: txn.sender_bank || '',
              receiver_account: txn.receiver_account || '',
              receiver_transaction_id: txn.receiver_transaction_id || '',
              receiver_bank: txn.receiver_bank || '',
              receiver_ifsc: txn.receiver_ifsc || '',
              receiver_info: txn.receiver_info || '',
              created_at: txn.extracted_at || new Date().toISOString(),
              updated_at: new Date().toISOString()
            }
            transactions.push(coreTransaction)
          })
        }
      })
    }

    return transactions
  }

  /**
   * Generate bank notice data from transactions
   */
  private static generateBankNoticeData(transactions: CoreTransactionData[]): BankNoticeData {
    const banks: Record<string, BankNoticeEntry> = {}
    let totalAmount = 0

    transactions.forEach(transaction => {
      // Use receiver_bank as primary bank for notice generation
      const bankName = transaction.receiver_bank || 'Unknown Bank'
      
      if (!banks[bankName]) {
        banks[bankName] = {
          bank_name: bankName,
          enhanced_bank_name: bankName,
          transactions: [],
          total_amount: 0,
          transaction_count: 0,
          enhanced_bank_data: undefined
        }
      }

      // Add transaction to bank entry
      banks[bankName].transactions.push(transaction)
      banks[bankName].transaction_count++

      // Update amounts
      try {
        const amount = parseFloat(transaction.amount || '0')
        banks[bankName].total_amount += amount
        totalAmount += amount
      } catch {
        // Ignore invalid amounts
      }
    })

    return {
      banks,
      total_banks: Object.keys(banks).length,
      total_amount: totalAmount,
      last_updated: new Date().toISOString()
    }
  }

  /**
   * Update a specific transaction and sync bank notice data
   */
  static updateTransactionAndSync(
    complaintData: ComplaintData,
    transactionId: string,
    updates: Partial<CoreTransactionData>
  ): ComplaintData {
    try {
      // Update the transaction in the complaint data
      const updatedComplaintData = this.updateTransactionInComplaint(complaintData, transactionId, updates)
      
      // Regenerate bank notice data
      return this.updateBankNoticeData(updatedComplaintData)
    } catch (error) {
      console.error('[Bank Notice Sync] Error updating transaction and syncing:', error)
      return complaintData
    }
  }

  /**
   * Update a specific transaction in complaint data
   */
  private static updateTransactionInComplaint(
    complaintData: ComplaintData,
    transactionId: string,
    updates: Partial<CoreTransactionData>
  ): ComplaintData {
    const updatedComplaintData = { ...complaintData }

    // Handle unified format
    if (updatedComplaintData.layer_transactions && DataTransformationService.isUnifiedFormat(updatedComplaintData.layer_transactions)) {
      const unifiedData = updatedComplaintData.layer_transactions as any
      Object.keys(unifiedData.layers || {}).forEach(layerKey => {
        const layer = unifiedData.layers[layerKey]
        if (layer.transactions && Array.isArray(layer.transactions)) {
          layer.transactions.forEach((transaction: any, index: number) => {
            if (transaction.core && transaction.core.id === transactionId) {
              layer.transactions[index] = {
                ...transaction,
                core: {
                  ...transaction.core,
                  ...updates,
                  updated_at: new Date().toISOString()
                }
              }
            }
          })
        }
      })
    } else if (updatedComplaintData.layer_transactions) {
      // Handle legacy format
      Object.keys(updatedComplaintData.layer_transactions).forEach(layerKey => {
        const layerTransactions = updatedComplaintData.layer_transactions![layerKey] as any[]
        if (Array.isArray(layerTransactions)) {
          layerTransactions.forEach((txn, index) => {
            if (txn.id === transactionId || txn.transaction_id === transactionId || txn.txn_id === transactionId) {
              layerTransactions[index] = {
                ...txn,
                ...updates,
                updated_at: new Date().toISOString()
              }
            }
          })
        }
      })
    }

    return updatedComplaintData
  }

  /**
   * Add a new transaction and sync bank notice data
   */
  static addTransactionAndSync(
    complaintData: ComplaintData,
    newTransaction: CoreTransactionData
  ): ComplaintData {
    try {
      // Add the transaction to the complaint data
      const updatedComplaintData = this.addTransactionToComplaint(complaintData, newTransaction)
      
      // Regenerate bank notice data
      return this.updateBankNoticeData(updatedComplaintData)
    } catch (error) {
      console.error('[Bank Notice Sync] Error adding transaction and syncing:', error)
      return complaintData
    }
  }

  /**
   * Add a new transaction to complaint data
   */
  private static addTransactionToComplaint(
    complaintData: ComplaintData,
    newTransaction: CoreTransactionData
  ): ComplaintData {
    const updatedComplaintData = { ...complaintData }
    const layerKey = `Layer ${newTransaction.layer}`

    // Handle unified format
    if (updatedComplaintData.layer_transactions && DataTransformationService.isUnifiedFormat(updatedComplaintData.layer_transactions)) {
      const unifiedData = updatedComplaintData.layer_transactions as any
      
      if (!unifiedData.layers[layerKey]) {
        unifiedData.layers[layerKey] = {
          layer_number: newTransaction.layer,
          transactions: [],
          total_amount: 0,
          transaction_count: 0
        }
      }

      const unifiedTransaction = {
        core: newTransaction,
        include_in_bank_notice: true,
        graph: null
      }

      unifiedData.layers[layerKey].transactions.push(unifiedTransaction)
      unifiedData.layers[layerKey].transaction_count++
      
      try {
        const amount = parseFloat(newTransaction.amount || '0')
        unifiedData.layers[layerKey].total_amount += amount
        unifiedData.total_amount += amount
      } catch {
        // Ignore invalid amounts
      }
      
      unifiedData.total_transactions++
    } else {
      // Handle legacy format
      if (!updatedComplaintData.layer_transactions) {
        updatedComplaintData.layer_transactions = {}
      }
      
      if (!updatedComplaintData.layer_transactions[layerKey]) {
        updatedComplaintData.layer_transactions[layerKey] = []
      }

      (updatedComplaintData.layer_transactions[layerKey] as any[]).push(newTransaction)
    }

    return updatedComplaintData
  }

  /**
   * Remove a transaction and sync bank notice data
   */
  static removeTransactionAndSync(
    complaintData: ComplaintData,
    transactionId: string
  ): ComplaintData {
    try {
      // Remove the transaction from the complaint data
      const updatedComplaintData = this.removeTransactionFromComplaint(complaintData, transactionId)
      
      // Regenerate bank notice data
      return this.updateBankNoticeData(updatedComplaintData)
    } catch (error) {
      console.error('[Bank Notice Sync] Error removing transaction and syncing:', error)
      return complaintData
    }
  }

  /**
   * Remove a transaction from complaint data
   */
  private static removeTransactionFromComplaint(
    complaintData: ComplaintData,
    transactionId: string
  ): ComplaintData {
    const updatedComplaintData = { ...complaintData }

    // Handle unified format
    if (updatedComplaintData.layer_transactions && DataTransformationService.isUnifiedFormat(updatedComplaintData.layer_transactions)) {
      const unifiedData = updatedComplaintData.layer_transactions as any
      Object.keys(unifiedData.layers || {}).forEach(layerKey => {
        const layer = unifiedData.layers[layerKey]
        if (layer.transactions && Array.isArray(layer.transactions)) {
          layer.transactions = layer.transactions.filter((transaction: any) => 
            transaction.core && transaction.core.id !== transactionId
          )
          layer.transaction_count = layer.transactions.length
          
          // Recalculate layer total
          layer.total_amount = layer.transactions.reduce((sum: number, txn: any) => {
            try {
              return sum + parseFloat(txn.core.amount || '0')
            } catch {
              return sum
            }
          }, 0)
        }
      })
      
      // Recalculate unified data totals
      unifiedData.total_transactions = Object.values(unifiedData.layers).reduce(
        (sum: number, layer: any) => sum + (layer.transaction_count || 0), 0
      )
      unifiedData.total_amount = Object.values(unifiedData.layers).reduce(
        (sum: number, layer: any) => sum + (layer.total_amount || 0), 0
      )
    } else if (updatedComplaintData.layer_transactions) {
      // Handle legacy format
      Object.keys(updatedComplaintData.layer_transactions).forEach(layerKey => {
        const layerTransactions = updatedComplaintData.layer_transactions![layerKey] as any[]
        if (Array.isArray(layerTransactions)) {
          updatedComplaintData.layer_transactions![layerKey] = layerTransactions.filter(
            txn => txn.id !== transactionId && txn.transaction_id !== transactionId && txn.txn_id !== transactionId
          )
        }
      })
    }

    return updatedComplaintData
  }

  /**
   * Auto-sync bank notice data when receiver_bank field changes
   */
  static autoSyncOnBankChange(
    complaintData: ComplaintData,
    transactionId: string,
    oldReceiverBank: string,
    newReceiverBank: string
  ): ComplaintData {
    try {
      console.log(`[Bank Notice Sync] Auto-syncing bank change: ${oldReceiverBank} -> ${newReceiverBank}`)

      // Update the transaction with new receiver bank
      const updatedComplaintData = this.updateTransactionInComplaint(complaintData, transactionId, {
        receiver_bank: newReceiverBank,
        updated_at: new Date().toISOString()
      })

      // Regenerate bank notice data to reflect the bank change
      const syncedComplaintData = this.updateBankNoticeData(updatedComplaintData)

      console.log(`[Bank Notice Sync] Auto-sync completed for bank change`)
      return syncedComplaintData
    } catch (error) {
      console.error('[Bank Notice Sync] Error in auto-sync on bank change:', error)
      return complaintData
    }
  }

  /**
   * Bulk update multiple transactions and sync bank notice data
   */
  static bulkUpdateTransactionsAndSync(
    complaintData: ComplaintData,
    updates: Array<{ transactionId: string; updates: Partial<CoreTransactionData> }>
  ): ComplaintData {
    try {
      console.log(`[Bank Notice Sync] Bulk updating ${updates.length} transactions`)

      let updatedComplaintData = { ...complaintData }

      // Apply all updates
      updates.forEach(({ transactionId, updates: txnUpdates }) => {
        updatedComplaintData = this.updateTransactionInComplaint(
          updatedComplaintData,
          transactionId,
          txnUpdates
        )
      })

      // Regenerate bank notice data once after all updates
      const syncedComplaintData = this.updateBankNoticeData(updatedComplaintData)

      console.log(`[Bank Notice Sync] Bulk update completed for ${updates.length} transactions`)
      return syncedComplaintData
    } catch (error) {
      console.error('[Bank Notice Sync] Error in bulk update and sync:', error)
      return complaintData
    }
  }

  /**
   * Get bank notice statistics
   */
  static getBankNoticeStats(complaintData: ComplaintData): {
    totalBanks: number
    totalTransactions: number
    totalAmount: number
    bankBreakdown: Array<{
      bankName: string
      transactionCount: number
      totalAmount: number
      percentage: number
    }>
  } {
    const transactions = this.extractAllTransactions(complaintData)
    const bankStats = new Map<string, { count: number; amount: number }>()
    let totalAmount = 0

    transactions.forEach(transaction => {
      const bankName = transaction.receiver_bank || 'Unknown Bank'

      if (!bankStats.has(bankName)) {
        bankStats.set(bankName, { count: 0, amount: 0 })
      }

      const stats = bankStats.get(bankName)!
      stats.count++

      try {
        const amount = parseFloat(transaction.amount || '0')
        stats.amount += amount
        totalAmount += amount
      } catch {
        // Ignore invalid amounts
      }
    })

    const bankBreakdown = Array.from(bankStats.entries()).map(([bankName, stats]) => ({
      bankName,
      transactionCount: stats.count,
      totalAmount: stats.amount,
      percentage: totalAmount > 0 ? (stats.amount / totalAmount) * 100 : 0
    })).sort((a, b) => b.totalAmount - a.totalAmount)

    return {
      totalBanks: bankStats.size,
      totalTransactions: transactions.length,
      totalAmount,
      bankBreakdown
    }
  }

  /**
   * Validate bank notice data consistency
   */
  static validateBankNoticeConsistency(complaintData: ComplaintData): {
    isConsistent: boolean
    issues: string[]
    suggestions: string[]
  } {
    const issues: string[] = []
    const suggestions: string[] = []

    try {
      const transactions = this.extractAllTransactions(complaintData)
      const bankNoticeData = complaintData.bank_notice_data as any

      if (!bankNoticeData || !bankNoticeData.banks) {
        issues.push('Bank notice data is missing or invalid')
        suggestions.push('Regenerate bank notice data using updateBankNoticeData method')
        return { isConsistent: false, issues, suggestions }
      }

      // Check if all receiver banks are represented in bank notice data
      const transactionBanks = new Set(transactions.map(t => t.receiver_bank || 'Unknown Bank'))
      const noticeBanks = new Set(Object.keys(bankNoticeData.banks))

      transactionBanks.forEach(bank => {
        if (!noticeBanks.has(bank)) {
          issues.push(`Bank "${bank}" found in transactions but missing from bank notice data`)
          suggestions.push(`Add missing bank "${bank}" to bank notice data`)
        }
      })

      noticeBanks.forEach(bank => {
        if (!transactionBanks.has(bank)) {
          issues.push(`Bank "${bank}" found in bank notice data but no transactions reference it`)
          suggestions.push(`Remove orphaned bank "${bank}" from bank notice data`)
        }
      })

      // Check transaction counts and amounts
      Object.entries(bankNoticeData.banks).forEach(([bankName, bankEntry]: [string, any]) => {
        const bankTransactions = transactions.filter(t => (t.receiver_bank || 'Unknown Bank') === bankName)

        if (bankEntry.transaction_count !== bankTransactions.length) {
          issues.push(`Bank "${bankName}" transaction count mismatch: expected ${bankTransactions.length}, got ${bankEntry.transaction_count}`)
          suggestions.push(`Update transaction count for bank "${bankName}"`)
        }

        const expectedAmount = bankTransactions.reduce((sum, t) => {
          try {
            return sum + parseFloat(t.amount || '0')
          } catch {
            return sum
          }
        }, 0)

        if (Math.abs(bankEntry.total_amount - expectedAmount) > 0.01) {
          issues.push(`Bank "${bankName}" total amount mismatch: expected ${expectedAmount}, got ${bankEntry.total_amount}`)
          suggestions.push(`Recalculate total amount for bank "${bankName}"`)
        }
      })

      // Check overall totals
      if (bankNoticeData.total_banks !== Object.keys(bankNoticeData.banks).length) {
        issues.push(`Total banks count mismatch: expected ${Object.keys(bankNoticeData.banks).length}, got ${bankNoticeData.total_banks}`)
        suggestions.push('Update total_banks count in bank notice data')
      }

      const expectedTotalAmount = transactions.reduce((sum, t) => {
        try {
          return sum + parseFloat(t.amount || '0')
        } catch {
          return sum
        }
      }, 0)

      if (Math.abs(bankNoticeData.total_amount - expectedTotalAmount) > 0.01) {
        issues.push(`Total amount mismatch: expected ${expectedTotalAmount}, got ${bankNoticeData.total_amount}`)
        suggestions.push('Recalculate total amount in bank notice data')
      }

    } catch (error) {
      issues.push(`Error validating bank notice consistency: ${error}`)
      suggestions.push('Check bank notice data structure and regenerate if necessary')
    }

    return {
      isConsistent: issues.length === 0,
      issues,
      suggestions
    }
  }

  /**
   * Auto-fix bank notice data inconsistencies
   */
  static autoFixBankNoticeData(complaintData: ComplaintData): {
    fixedComplaintData: ComplaintData
    fixesApplied: string[]
  } {
    const fixesApplied: string[] = []

    try {
      console.log('[Bank Notice Sync] Auto-fixing bank notice data inconsistencies')

      // Regenerate bank notice data from scratch to fix all inconsistencies
      const fixedComplaintData = this.updateBankNoticeData(complaintData)
      fixesApplied.push('Regenerated bank notice data from transaction data')

      console.log(`[Bank Notice Sync] Auto-fix completed with ${fixesApplied.length} fixes applied`)

      return {
        fixedComplaintData,
        fixesApplied
      }
    } catch (error) {
      console.error('[Bank Notice Sync] Error in auto-fix:', error)
      return {
        fixedComplaintData: complaintData,
        fixesApplied: [`Error during auto-fix: ${error}`]
      }
    }
  }
}

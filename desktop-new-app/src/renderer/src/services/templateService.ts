import Docxtemplater from 'docxtemplater'
import <PERSON><PERSON><PERSON><PERSON> from 'pizzip'
import { ComplaintData } from '../../../shared/api'

export interface TemplateData {
  bank_name: string
  complaint_number: string
  complaint_date: string
  complainant_name: string
  total_fraud_amount: string
  main_transactions: Array<{
    date: string
    receiver_account: string
    amount: string
    txn_id: string
    reference: string
  }>
  sub_transactions: Array<{
    date: string
    txn_type: string
    amount: string
    sender_txn_id: string
    reference: string
  }>
  selected_bank_points: string[]
}

interface TransactionLike {
  layer?: number | string
  amount?: string
  date?: string
  receiver_account?: string
  txn_id?: string
  receiver_transaction_id?: string
  reference?: string
  receiver_info?: string
  txn_type?: string
  type?: string
  sender_transaction_id?: string
}

export class TemplateService {
  /**
   * Generate notice document using template and complaint data
   */
  static async generateNotice(
    complaint: ComplaintData,
    bankName: string,
    selectedPoints: string[]
  ): Promise<Blob> {
    try {
      // Get the template from storage
      const templateResult = await window.api.template.get()
      if (!templateResult.success || !templateResult.template) {
        throw new Error('No template found')
      }

      // Convert template content from base64 or text to ArrayBuffer
      const templateContent = templateResult.template.content
      let templateBuffer: ArrayBuffer

      try {
        // Try to decode as base64 first
        const binaryString = atob(templateContent)
        templateBuffer = new ArrayBuffer(binaryString.length)
        const uint8Array = new Uint8Array(templateBuffer)
        for (let i = 0; i < binaryString.length; i++) {
          uint8Array[i] = binaryString.charCodeAt(i)
        }
      } catch {
        // If base64 fails, treat as binary string
        const encoder = new TextEncoder()
        templateBuffer = encoder.encode(templateContent).buffer
      }

      // Prepare template data
      const templateData = this.prepareTemplateData(complaint, bankName, selectedPoints)

      // Create PizZip instance and load template
      const zip = new PizZip(templateBuffer)

      // Create docxtemplater instance
      const doc = new Docxtemplater(zip, {
        paragraphLoop: true,
        linebreaks: true
      })

      doc.setData(templateData)

      try {
        doc.render()
      } catch (error) {
        console.error('Template rendering error:', error)
        throw new Error('Failed to render template')
      }

      // Generate the document
      const buffer = doc.getZip().generate({ type: 'arraybuffer' })
      return new Blob([buffer], {
        type: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
      })
    } catch (error) {
      console.error('Notice generation error:', error)
      throw error
    }
  }

  /**
   * Prepare template data from complaint information
   */
  private static prepareTemplateData(
    complaint: ComplaintData,
    bankName: string,
    selectedPoints: string[]
  ): TemplateData {
    // Extract basic complaint information
    const complaintNumber = String(complaint.metadata?.complaint_number || complaint.title || 'N/A')
    const complaintDate = complaint.created_at
      ? new Date(complaint.created_at).toLocaleDateString()
      : new Date().toLocaleDateString()
    const complainantName = String(complaint.metadata?.complainant_name || 'N/A')

    // Calculate total fraud amount
    let totalAmount = 0
    const allTransactions = Object.values(
      complaint.layer_transactions || {}
    ).flat() as TransactionLike[]
    allTransactions.forEach((txn) => {
      if (txn.amount) {
        const amount = parseFloat(txn.amount.toString().replace(/[^\d.-]/g, ''))
        if (!isNaN(amount)) {
          totalAmount += amount
        }
      }
    })

    // Prepare main transactions (Layer 0 - sender transactions)
    const mainTransactions = allTransactions
      .filter((txn) => txn.layer === 0 || txn.layer === '0')
      .map((txn) => ({
        date: txn.date || 'N/A',
        receiver_account: txn.receiver_account || 'N/A',
        amount: txn.amount || 'N/A',
        txn_id: txn.txn_id || txn.receiver_transaction_id || 'N/A',
        reference: txn.reference || txn.receiver_info || 'N/A'
      }))

    // Prepare sub transactions (Layer 1+ - receiver transactions)
    const subTransactions = allTransactions
      .filter((txn) => txn.layer && Number(txn.layer) > 0 && txn.layer !== '0')
      .map((txn) => ({
        date: txn.date || 'N/A',
        txn_type: txn.txn_type || txn.type || 'N/A',
        amount: txn.amount || 'N/A',
        sender_txn_id: txn.sender_transaction_id || 'N/A',
        reference: txn.reference || txn.receiver_info || 'N/A'
      }))

    return {
      bank_name: bankName,
      complaint_number: complaintNumber,
      complaint_date: complaintDate,
      complainant_name: complainantName,
      total_fraud_amount: totalAmount.toLocaleString('en-IN', {
        style: 'currency',
        currency: 'INR'
      }),
      main_transactions: mainTransactions,
      sub_transactions: subTransactions,
      selected_bank_points: selectedPoints
    }
  }

  /**
   * Download generated document
   */
  static downloadDocument(blob: Blob, filename: string): void {
    const url = URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.download = filename
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    URL.revokeObjectURL(url)
  }
}

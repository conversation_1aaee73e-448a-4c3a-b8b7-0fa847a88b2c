/**
 * Real-Time Synchronization Service
 * 
 * This service provides real-time synchronization between different views
 * (table, graph, bank notice) when transaction data is modified.
 */

import { ComplaintData } from '../../../shared/api'
import { DataTransformationService } from './dataTransformationService'
import { BankNoticeSyncService } from './bankNoticeSync'

// Event types for real-time synchronization
export interface DataSyncEvent {
  type: 'transaction_updated' | 'transaction_added' | 'transaction_deleted' | 'bank_notice_updated' | 'complaint_updated'
  complaint_id: string
  transaction_id?: string
  data?: unknown
  timestamp: string
  source: 'table' | 'graph' | 'bank_notice' | 'system'
}

// Callback type for event subscribers
export type DataSyncCallback = (event: DataSyncEvent) => void

// Subscription interface
interface Subscription {
  id: string
  callback: DataSyncCallback
  filter?: (event: DataSyncEvent) => boolean
}

export class RealTimeSyncService {
  private static instance: RealTimeSyncService | null = null
  private subscriptions: Map<string, Subscription> = new Map()
  private complaintCache: Map<string, ComplaintData> = new Map()
  private subscriptionDebounce: Map<string, NodeJS.Timeout> = new Map()

  private constructor() {
    // Private constructor for singleton pattern
  }

  /**
   * Get singleton instance
   */
  static getInstance(): RealTimeSyncService {
    if (!RealTimeSyncService.instance) {
      RealTimeSyncService.instance = new RealTimeSyncService()
    }
    return RealTimeSyncService.instance
  }

  /**
   * Subscribe to data change events
   */
  subscribe(
    callback: DataSyncCallback,
    filter?: (event: DataSyncEvent) => boolean
  ): () => void {
    const subscriptionId = `sub_${Date.now()}_${Math.random()}`

    this.subscriptions.set(subscriptionId, {
      id: subscriptionId,
      callback,
      filter
    })

    console.log(`[RealTimeSync] New subscription created: ${subscriptionId} (Total: ${this.subscriptions.size})`)

    // Return unsubscribe function
    return () => {
      // Clear any pending debounce for this subscription
      const debounceKey = `unsub_${subscriptionId}`
      if (this.subscriptionDebounce.has(debounceKey)) {
        clearTimeout(this.subscriptionDebounce.get(debounceKey)!)
        this.subscriptionDebounce.delete(debounceKey)
      }

      this.subscriptions.delete(subscriptionId)
      console.log(`[RealTimeSync] Subscription removed: ${subscriptionId} (Total: ${this.subscriptions.size})`)
    }
  }

  /**
   * Emit data change event to all subscribers
   */
  emit(event: DataSyncEvent): void {
    console.log(`[RealTimeSync] Emitting event:`, event)

    this.subscriptions.forEach((subscription) => {
      try {
        // Apply filter if provided
        if (subscription.filter && !subscription.filter(event)) {
          return
        }

        // Call the callback
        subscription.callback(event)
      } catch (error) {
        console.error(`[RealTimeSync] Error in subscription ${subscription.id}:`, error)
      }
    })
  }

  /**
   * Update transaction and sync all views
   */
  async updateTransaction(
    complaintId: string,
    transactionId: string,
    updates: Record<string, unknown>,
    source: 'table' | 'graph' = 'table'
  ): Promise<void> {
    try {
      console.log(`[RealTimeSync] Updating transaction ${transactionId} in complaint ${complaintId}`)

      // Get current complaint data
      const complaintData = await this.getComplaintData(complaintId)
      if (!complaintData) {
        throw new Error(`Complaint ${complaintId} not found`)
      }

      // Update the transaction using BankNoticeSyncService
      const updatedComplaintData = BankNoticeSyncService.updateTransactionAndSync(
        complaintData,
        transactionId,
        updates as any
      )

      // Update cache
      this.complaintCache.set(complaintId, updatedComplaintData)

      // Save to database
      await this.saveComplaintData(complaintId, updatedComplaintData)

      // Emit sync event
      this.emit({
        type: 'transaction_updated',
        complaint_id: complaintId,
        transaction_id: transactionId,
        data: updates,
        timestamp: new Date().toISOString(),
        source
      })

      console.log(`[RealTimeSync] Transaction ${transactionId} updated successfully`)
    } catch (error) {
      console.error(`[RealTimeSync] Error updating transaction:`, error)
      throw error
    }
  }

  /**
   * Add new transaction and sync all views
   */
  async addTransaction(
    complaintId: string,
    newTransaction: Record<string, unknown>,
    source: 'table' | 'graph' = 'table'
  ): Promise<string> {
    try {
      console.log(`[RealTimeSync] Adding new transaction to complaint ${complaintId}`)

      // Get current complaint data
      const complaintData = await this.getComplaintData(complaintId)
      if (!complaintData) {
        throw new Error(`Complaint ${complaintId} not found`)
      }

      // Generate transaction ID
      const transactionId = `txn_${Date.now()}_${Math.random()}`
      const transactionWithId = { ...newTransaction, id: transactionId }

      // Add the transaction using BankNoticeSyncService
      const updatedComplaintData = BankNoticeSyncService.addTransactionAndSync(
        complaintData,
        transactionWithId as any
      )

      // Update cache
      this.complaintCache.set(complaintId, updatedComplaintData)

      // Save to database
      await this.saveComplaintData(complaintId, updatedComplaintData)

      // Emit sync event
      this.emit({
        type: 'transaction_added',
        complaint_id: complaintId,
        transaction_id: transactionId,
        data: transactionWithId,
        timestamp: new Date().toISOString(),
        source
      })

      console.log(`[RealTimeSync] Transaction ${transactionId} added successfully`)
      return transactionId
    } catch (error) {
      console.error(`[RealTimeSync] Error adding transaction:`, error)
      throw error
    }
  }

  /**
   * Remove transaction and sync all views
   */
  async removeTransaction(
    complaintId: string,
    transactionId: string,
    source: 'table' | 'graph' = 'table'
  ): Promise<void> {
    try {
      console.log(`[RealTimeSync] Removing transaction ${transactionId} from complaint ${complaintId}`)

      // Get current complaint data
      const complaintData = await this.getComplaintData(complaintId)
      if (!complaintData) {
        throw new Error(`Complaint ${complaintId} not found`)
      }

      // Remove the transaction using BankNoticeSyncService
      const updatedComplaintData = BankNoticeSyncService.removeTransactionAndSync(
        complaintData,
        transactionId
      )

      // Update cache
      this.complaintCache.set(complaintId, updatedComplaintData)

      // Save to database
      await this.saveComplaintData(complaintId, updatedComplaintData)

      // Emit sync event
      this.emit({
        type: 'transaction_deleted',
        complaint_id: complaintId,
        transaction_id: transactionId,
        data: null,
        timestamp: new Date().toISOString(),
        source
      })

      console.log(`[RealTimeSync] Transaction ${transactionId} removed successfully`)
    } catch (error) {
      console.error(`[RealTimeSync] Error removing transaction:`, error)
      throw error
    }
  }

  /**
   * Get complaint data (from cache or database)
   */
  private async getComplaintData(complaintId: string): Promise<ComplaintData | null> {
    // Check cache first
    if (this.complaintCache.has(complaintId)) {
      return this.complaintCache.get(complaintId)!
    }

    // Load from database
    try {
      const complaintData = await window.electronAPI.database.getComplaint(complaintId)
      if (complaintData) {
        this.complaintCache.set(complaintId, complaintData)
      }
      return complaintData
    } catch (error) {
      console.error(`[RealTimeSync] Error loading complaint ${complaintId}:`, error)
      return null
    }
  }

  /**
   * Save complaint data to database
   */
  private async saveComplaintData(complaintId: string, complaintData: ComplaintData): Promise<void> {
    try {
      await window.electronAPI.database.updateComplaint(complaintId, {
        layer_transactions: complaintData.layer_transactions,
        graph_data: complaintData.graph_data,
        bank_notice_data: complaintData.bank_notice_data,
        updated_at: new Date().toISOString()
      })
    } catch (error) {
      console.error(`[RealTimeSync] Error saving complaint ${complaintId}:`, error)
      throw error
    }
  }

  /**
   * Clear cache for a specific complaint
   */
  clearCache(complaintId?: string): void {
    if (complaintId) {
      this.complaintCache.delete(complaintId)
      console.log(`[RealTimeSync] Cache cleared for complaint ${complaintId}`)
    } else {
      this.complaintCache.clear()
      console.log(`[RealTimeSync] All cache cleared`)
    }
  }

  /**
   * Get current cache size
   */
  getCacheSize(): number {
    return this.complaintCache.size
  }

  /**
   * Subscribe to specific complaint events
   */
  subscribeToComplaint(
    complaintId: string,
    callback: DataSyncCallback
  ): () => void {
    return this.subscribe(callback, (event) => event.complaint_id === complaintId)
  }

  /**
   * Subscribe to specific transaction events
   */
  subscribeToTransaction(
    complaintId: string,
    transactionId: string,
    callback: DataSyncCallback
  ): () => void {
    return this.subscribe(
      callback,
      (event) => event.complaint_id === complaintId && event.transaction_id === transactionId
    )
  }

  /**
   * Force sync all views for a complaint
   */
  async forceSyncComplaint(complaintId: string): Promise<void> {
    try {
      console.log(`[RealTimeSync] Force syncing complaint ${complaintId}`)

      // Clear cache to force reload
      this.clearCache(complaintId)

      // Emit sync event
      this.emit({
        type: 'complaint_updated',
        complaint_id: complaintId,
        timestamp: new Date().toISOString(),
        source: 'system'
      })

      console.log(`[RealTimeSync] Force sync completed for complaint ${complaintId}`)
    } catch (error) {
      console.error(`[RealTimeSync] Error force syncing complaint:`, error)
      throw error
    }
  }

  /**
   * Get subscription count for debugging
   */
  getSubscriptionCount(): number {
    return this.subscriptions.size
  }

  /**
   * Clear all subscriptions (for cleanup)
   */
  clearAllSubscriptions(): void {
    console.log(`[RealTimeSync] Clearing all ${this.subscriptions.size} subscriptions`)
    this.subscriptions.clear()
    this.subscriptionDebounce.forEach(timeout => clearTimeout(timeout))
    this.subscriptionDebounce.clear()
  }

  /**
   * Debug method to log current subscriptions
   */
  debugSubscriptions(): void {
    console.log(`[RealTimeSync] Current subscriptions (${this.subscriptions.size}):`)
    this.subscriptions.forEach((sub, id) => {
      console.log(`  - ${id}: ${sub.filter ? 'filtered' : 'unfiltered'}`)
    })
  }
}

// Export singleton instance
export const realTimeSyncService = RealTimeSyncService.getInstance()

// Export convenience functions
export const subscribeToDataChanges = (callback: DataSyncCallback) => 
  realTimeSyncService.subscribe(callback)

export const subscribeToComplaint = (complaintId: string, callback: DataSyncCallback) =>
  realTimeSyncService.subscribeToComplaint(complaintId, callback)

export const updateTransaction = (
  complaintId: string,
  transactionId: string,
  updates: Record<string, unknown>,
  source: 'table' | 'graph' = 'table'
) => realTimeSyncService.updateTransaction(complaintId, transactionId, updates, source)

export const addTransaction = (
  complaintId: string,
  newTransaction: Record<string, unknown>,
  source: 'table' | 'graph' = 'table'
) => realTimeSyncService.addTransaction(complaintId, newTransaction, source)

export const removeTransaction = (
  complaintId: string,
  transactionId: string,
  source: 'table' | 'graph' = 'table'
) => realTimeSyncService.removeTransaction(complaintId, transactionId, source)

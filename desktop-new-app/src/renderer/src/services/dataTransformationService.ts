/**
 * Data Transformation Service
 *
 * This service transforms the unified data structure from the backend
 * into the formats needed by different frontend components:
 * - ReactFlow graph visualization format
 * - Table format for complaint details
 * - Handles real-time synchronization between views
 */

import { TransactionData } from '../../../shared/api'

// Unified data structure from backend
interface UnifiedData {
  layers: Record<string, LayerData>
  graph_edges: GraphEdgeData[]
  metadata: Record<string, unknown>
  max_layer: number
  total_transactions: number
  total_amount: number
}

interface LayerData {
  layer_number: number
  transactions: UnifiedTransaction[]
  total_amount: number
  transaction_count: number
}

interface UnifiedTransaction {
  core: CoreTransactionData
  include_in_bank_notice: boolean
  graph?: GraphNodeData
}

interface CoreTransactionData {
  id: string
  txn_id?: string
  transaction_id?: string
  fraud_type: string
  txn_type: string
  amount: string
  date: string
  reference: string
  layer: number
  sender_account: string
  sender_transaction_id: string
  sender_bank: string
  receiver_account: string
  receiver_transaction_id: string
  receiver_bank: string
  receiver_ifsc: string
  receiver_info: string
  created_at: string
  updated_at: string
}

interface GraphNodeData {
  position: { x: number; y: number }
  node_type: 'sender_account' | 'receiver_account' | 'metadata'
  label: string
  is_sender_node: boolean
  is_main_transaction: boolean
  is_victim: boolean
  is_consolidated: boolean
  consolidated_transactions?: CoreTransactionData[]
  transaction_count: number
  total_amount: string
  is_collapsed: boolean
  has_children: boolean
  visible_fields: string[]
}

interface GraphEdgeData {
  id: string
  source_transaction_id: string
  target_transaction_id: string
  edge_type: 'transaction_flow' | 'account_link'
  label?: string
  animated?: boolean
  style?: Record<string, unknown>
}

// ReactFlow format
interface ReactFlowGraphData {
  nodes: ReactFlowNode[]
  edges: ReactFlowEdge[]
  metadata: Record<string, unknown>
  max_layer: number
}

interface ReactFlowNode {
  id: string
  type: string
  position: { x: number; y: number }
  data: Record<string, unknown>
}

interface ReactFlowEdge {
  id: string
  source: string
  target: string
  type?: string
  data?: Record<string, unknown>
}

// Table format
interface TransactionTableData {
  transactions: TransactionData[]
  total_count: number
  layers: string[]
}

export class DataTransformationService {
  /**
   * Transform unified data to ReactFlow format for graph visualization
   */
  static toReactFlowData(unifiedData: UnifiedData): ReactFlowGraphData {
    const nodes: ReactFlowNode[] = []
    const edges: ReactFlowEdge[] = []

    // Extract nodes from transactions that have graph data
    Object.values(unifiedData.layers).forEach((layer) => {
      layer.transactions.forEach((transaction) => {
        if (transaction.graph) {
          const node: ReactFlowNode = {
            id: transaction.core.id,
            type: 'transaction',
            position: transaction.graph.position,
            data: {
              ...transaction.core,
              ...transaction.graph,
              // Map fields for compatibility with existing components
              account:
                transaction.graph.node_type === 'sender_account'
                  ? transaction.core.sender_account
                  : transaction.core.receiver_account,
              bank:
                transaction.graph.node_type === 'sender_account'
                  ? transaction.core.sender_bank
                  : transaction.core.receiver_bank,
              isSenderNode: transaction.graph.is_sender_node,
              isMainTransaction: transaction.graph.is_main_transaction,
              isVictim: transaction.graph.is_victim,
              hasSubTransactions: transaction.graph.has_children,
              consolidatedTransactions: transaction.graph.consolidated_transactions,
              transaction_count: transaction.graph.transaction_count,
              total_amount: transaction.graph.total_amount,
              visibleFields: new Set(transaction.graph.visible_fields)
            }
          }
          nodes.push(node)
        }
      })
    })

    // Transform edges
    unifiedData.graph_edges.forEach((edge) => {
      const reactFlowEdge: ReactFlowEdge = {
        id: edge.id,
        source: edge.source_transaction_id,
        target: edge.target_transaction_id,
        type: 'default',
        data: {
          label: edge.label,
          animated: edge.animated,
          style: edge.style
        }
      }
      edges.push(reactFlowEdge)
    })

    return {
      nodes,
      edges,
      metadata: unifiedData.metadata,
      max_layer: unifiedData.max_layer
    }
  }

  /**
   * Transform unified data to table format for complaint details
   */
  static toTableData(unifiedData: UnifiedData): TransactionTableData {
    const transactions: TransactionData[] = []
    const layers: string[] = []

    // Extract all transactions and flatten them
    Object.entries(unifiedData.layers).forEach(([layerKey, layerData]) => {
      layers.push(layerKey)

      layerData.transactions.forEach((transaction) => {
        const tableTransaction: TransactionData = {
          fraud_type: transaction.core.fraud_type,
          sender_account: transaction.core.sender_account,
          sender_transaction_id: transaction.core.sender_transaction_id,
          sender_bank: transaction.core.sender_bank,
          layer: transaction.core.layer,
          txn_type: transaction.core.txn_type,
          type: transaction.core.txn_type, // Map txn_type to type for compatibility
          date: transaction.core.date,
          receiver_bank: transaction.core.receiver_bank,
          receiver_account: transaction.core.receiver_account,
          receiver_transaction_id: transaction.core.receiver_transaction_id,
          amount: transaction.core.amount,
          receiver_ifsc: transaction.core.receiver_ifsc,
          receiver_info: transaction.core.receiver_info,
          reference: transaction.core.reference,
          sr_no: '', // Not used in unified structure
          extracted_at: transaction.core.created_at,
          source: 'unified_extraction',
          is_valid: 'true', // All transactions in unified structure are valid
          validation_errors: '',
          txn_id: transaction.core.txn_id,
          transaction_id: transaction.core.transaction_id
        }
        transactions.push(tableTransaction)
      })
    })

    return {
      transactions,
      total_count: transactions.length,
      layers: layers.sort()
    }
  }

  /**
   * Transform legacy layer_transactions format to unified format (for backward compatibility)
   */
  static fromLegacyLayerTransactions(layerTransactions: Record<string, unknown>): UnifiedData {
    const unifiedData: UnifiedData = {
      layers: {},
      graph_edges: [],
      metadata: {},
      max_layer: 0,
      total_transactions: 0,
      total_amount: 0
    }

    Object.entries(layerTransactions).forEach(([layerKey, transactions]) => {
      if (Array.isArray(transactions)) {
        const layerNumber = parseInt(layerKey.replace('Layer ', ''))
        unifiedData.max_layer = Math.max(unifiedData.max_layer, layerNumber)

        const layerData: LayerData = {
          layer_number: layerNumber,
          transactions: [],
          total_amount: 0,
          transaction_count: 0
        }

        transactions.forEach((txn: TransactionData & { id?: string }) => {
          const unifiedTransaction: UnifiedTransaction = {
            core: {
              id: txn.id || `txn_${Date.now()}_${Math.random()}`,
              txn_id: txn.txn_id || txn.transaction_id,
              transaction_id: txn.transaction_id || txn.txn_id,
              fraud_type: txn.fraud_type || '',
              txn_type: txn.txn_type || txn.type || '',
              amount: txn.amount || '',
              date: txn.date || '',
              reference: txn.reference || '',
              layer: layerNumber,
              sender_account: txn.sender_account || '',
              sender_transaction_id: txn.sender_transaction_id || '',
              sender_bank: txn.sender_bank || '',
              receiver_account: txn.receiver_account || '',
              receiver_transaction_id: txn.receiver_transaction_id || '',
              receiver_bank: txn.receiver_bank || '',
              receiver_ifsc: txn.receiver_ifsc || '',
              receiver_info: txn.receiver_info || '',
              created_at: txn.extracted_at || new Date().toISOString(),
              updated_at: new Date().toISOString()
            },
            include_in_bank_notice: true,
            graph: undefined // Will be populated if needed
          }

          layerData.transactions.push(unifiedTransaction)
          layerData.transaction_count++

          try {
            const amount = parseFloat(txn.amount || '0')
            layerData.total_amount += amount
            unifiedData.total_amount += amount
          } catch {
            // Ignore invalid amounts
          }
        })

        unifiedData.layers[layerKey] = layerData
        unifiedData.total_transactions += layerData.transaction_count
      }
    })

    return unifiedData
  }

  /**
   * Check if data is in unified format
   */
  static isUnifiedFormat(data: unknown): boolean {
    return (
      typeof data === 'object' &&
      data !== null &&
      'layers' in data &&
      'graph_edges' in data &&
      'metadata' in data
    )
  }

  /**
   * Get all transactions as a flat array from unified data
   */
  static getAllTransactions(unifiedData: UnifiedData): CoreTransactionData[] {
    const allTransactions: CoreTransactionData[] = []

    Object.values(unifiedData.layers).forEach((layer) => {
      layer.transactions.forEach((transaction) => {
        allTransactions.push(transaction.core)
      })
    })

    return allTransactions
  }

  /**
   * Update a specific transaction in unified data
   */
  static updateTransactionInUnifiedData(
    unifiedData: UnifiedData,
    transactionId: string,
    updates: Partial<CoreTransactionData>
  ): UnifiedData {
    const updatedData = { ...unifiedData }

    Object.keys(updatedData.layers).forEach((layerKey) => {
      const layer = updatedData.layers[layerKey]
      layer.transactions.forEach((transaction, index) => {
        if (transaction.core.id === transactionId) {
          layer.transactions[index] = {
            ...transaction,
            core: {
              ...transaction.core,
              ...updates,
              updated_at: new Date().toISOString()
            }
          }
        }
      })
    })

    return updatedData
  }

  /**
   * Add a new transaction to unified data
   */
  static addTransactionToUnifiedData(
    unifiedData: UnifiedData,
    newTransaction: CoreTransactionData
  ): UnifiedData {
    const updatedData = { ...unifiedData }
    const layerKey = `Layer ${newTransaction.layer}`

    if (!updatedData.layers[layerKey]) {
      updatedData.layers[layerKey] = {
        layer_number: newTransaction.layer,
        transactions: [],
        total_amount: 0,
        transaction_count: 0
      }
    }

    const unifiedTransaction: UnifiedTransaction = {
      core: newTransaction,
      include_in_bank_notice: true,
      graph: undefined
    }

    updatedData.layers[layerKey].transactions.push(unifiedTransaction)
    updatedData.layers[layerKey].transaction_count++

    try {
      const amount = parseFloat(newTransaction.amount || '0')
      updatedData.layers[layerKey].total_amount += amount
      updatedData.total_amount += amount
    } catch {
      // Ignore invalid amounts
    }

    updatedData.total_transactions++
    updatedData.max_layer = Math.max(updatedData.max_layer, newTransaction.layer)

    return updatedData
  }

  /**
   * Remove a transaction from unified data
   */
  static removeTransactionFromUnifiedData(
    unifiedData: UnifiedData,
    transactionId: string
  ): UnifiedData {
    const updatedData = { ...unifiedData }

    Object.keys(updatedData.layers).forEach((layerKey) => {
      const layer = updatedData.layers[layerKey]
      const originalLength = layer.transactions.length

      layer.transactions = layer.transactions.filter(
        (transaction) => transaction.core.id !== transactionId
      )

      if (layer.transactions.length !== originalLength) {
        layer.transaction_count = layer.transactions.length

        // Recalculate layer total
        layer.total_amount = layer.transactions.reduce((sum, txn) => {
          try {
            return sum + parseFloat(txn.core.amount || '0')
          } catch {
            return sum
          }
        }, 0)
      }
    })

    // Recalculate unified data totals
    updatedData.total_transactions = Object.values(updatedData.layers).reduce(
      (sum, layer) => sum + layer.transaction_count,
      0
    )
    updatedData.total_amount = Object.values(updatedData.layers).reduce(
      (sum, layer) => sum + layer.total_amount,
      0
    )

    return updatedData
  }

  /**
   * Validate unified data structure
   */
  static validateUnifiedData(data: unknown): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    if (!data || typeof data !== 'object') {
      errors.push('Data must be an object')
      return { isValid: false, errors }
    }

    const unifiedData = data as Record<string, unknown>

    // Check required top-level properties
    if (!unifiedData.layers || typeof unifiedData.layers !== 'object') {
      errors.push('Missing or invalid layers property')
    }

    if (!Array.isArray(unifiedData.graph_edges)) {
      errors.push('Missing or invalid graph_edges property')
    }

    if (!unifiedData.metadata || typeof unifiedData.metadata !== 'object') {
      errors.push('Missing or invalid metadata property')
    }

    if (typeof unifiedData.max_layer !== 'number') {
      errors.push('Missing or invalid max_layer property')
    }

    if (typeof unifiedData.total_transactions !== 'number') {
      errors.push('Missing or invalid total_transactions property')
    }

    if (typeof unifiedData.total_amount !== 'number') {
      errors.push('Missing or invalid total_amount property')
    }

    // Validate layers structure
    if (unifiedData.layers && typeof unifiedData.layers === 'object') {
      Object.entries(unifiedData.layers as Record<string, unknown>).forEach(([layerKey, layer]) => {
        if (!layer || typeof layer !== 'object') {
          errors.push(`Invalid layer structure for ${layerKey}`)
          return
        }

        const layerData = layer as Record<string, unknown>

        if (typeof layerData.layer_number !== 'number') {
          errors.push(`Missing or invalid layer_number for ${layerKey}`)
        }

        if (!Array.isArray(layerData.transactions)) {
          errors.push(`Missing or invalid transactions array for ${layerKey}`)
        }

        if (typeof layerData.total_amount !== 'number') {
          errors.push(`Missing or invalid total_amount for ${layerKey}`)
        }

        if (typeof layerData.transaction_count !== 'number') {
          errors.push(`Missing or invalid transaction_count for ${layerKey}`)
        }
      })
    }

    return { isValid: errors.length === 0, errors }
  }
}

import React, { useState, useEffect } from 'react'
import { Navigate, Link, useNavigate, useLocation } from 'react-router-dom'
import { useAuth } from '../hooks/useAuth'
import { CardContainer, CardBody, CardItem } from '../components/ui/3d-card'
import { BackgroundBeams } from '../components/ui/background-beams'
import { FullWidthLayout } from '../components/layout/FullWidthLayout'
import { Loader2, Mail, CheckCircle } from 'lucide-react'

const VerifyOTP: React.FC = () => {
  const { isAuthenticated, isLoading, verifyOTP, resendOTP } = useAuth()
  const navigate = useNavigate()
  const location = useLocation()
  const emailFromState = (location.state as { email?: string })?.email || ''

  const [formData, setFormData] = useState({
    email: emailFromState,
    otp: ''
  })
  const [error, setError] = useState('')
  const [success, setSuccess] = useState('')
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isResending, setIsResending] = useState(false)
  const [resendCooldown, setResendCooldown] = useState(0)
  const [timer, setTimer] = useState(300) // 5 minutes in seconds

  // Track resend attempts
  const [resendAttempts, setResendAttempts] = useState(0)

  // Cooldown timer for resend button
  useEffect(() => {
    const timerId = setTimeout(() => {
      if (resendCooldown > 0) {
        setResendCooldown(resendCooldown - 1)
      }
    }, 1000)
    return () => clearTimeout(timerId)
  }, [resendCooldown])

  // Countdown timer for OTP entry (5 minutes)
  useEffect(() => {
    const intervalId = setInterval(() => {
      if (timer > 0) {
        setTimer((t) => t - 1)
      }
    }, 1000)
    return () => clearInterval(intervalId)
  }, [timer])

  // Redirect if already authenticated
  if (isAuthenticated) {
    return <Navigate to="/" replace />
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const { name, value } = e.target
    setFormData((prev) => ({
      ...prev,
      [name]: value
    }))
    // Clear error when user starts typing
    if (error) {
      setError('')
    }
  }

  const handleSubmit = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault()

    if (!formData.email || !formData.otp) {
      setError('Please fill in all fields')
      console.error('[VerifyOTP] Error: Please fill in all fields')
      return
    }

    // Accept only 6-character alphanumeric OTPs (secure, matches backend)
    if (!/^[A-Za-z0-9]{6}$/.test(formData.otp)) {
      setError('OTP must be 6 characters (letters or numbers)')
      console.error('[VerifyOTP] Error: OTP must be 6 characters (letters or numbers)')
      return
    }

    setIsSubmitting(true)
    setError('')
    setSuccess('')

    try {
      const formattedEmail = formData.email.trim().toLowerCase()
      const formattedOtp = formData.otp.trim().toUpperCase()
      console.log('[VerifyOTP] window.api:', window.api)
      console.log('[VerifyOTP] window.api.auth:', window.api?.auth)
      console.log('[VerifyOTP] window.api.auth.verifyEmail:', window.api?.auth?.verifyEmail)
      console.log('[VerifyOTP] Sending OTP verification request with payload:', {
        email: formattedEmail,
        otp: formattedOtp
      })
      const response = await verifyOTP({
        email: formattedEmail,
        otp: formattedOtp
      })
      console.log('[VerifyOTP] OTP verification response:', response)

      if (response.success) {
        setSuccess('Email verified successfully! You can now login with your credentials.')
        console.log('[VerifyOTP] OTP verified successfully, redirecting to login in 2s')
        // Redirect to login after 2 seconds
        setTimeout(() => {
          navigate('/login')
        }, 2000)
      } else {
        console.error(
          '[VerifyOTP] OTP verification failed:',
          response.message || 'OTP verification failed'
        )
        setError(response.message || 'OTP verification failed')
      }
    } catch (err) {
      console.error('[VerifyOTP] Exception during OTP verification:', err)
      if (err instanceof Error && err.message.includes('Backend service is not available')) {
        setError('Backend service is not available. Please ensure the backend server is running.')
        console.error('[VerifyOTP] Backend service is not available.')
      } else {
        setError(err instanceof Error ? err.message : 'OTP verification failed. Please try again.')
        console.error(
          '[VerifyOTP] OTP verification failed:',
          err instanceof Error ? err.message : 'OTP verification failed. Please try again.'
        )
      }
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleResendOTP = async (): Promise<void> => {
    if (resendAttempts >= 5) {
      setError(
        'You have reached the maximum number of resend attempts. Please try again later or contact support.'
      )
      return
    }
    if (!formData.email) {
      setError('Please enter your email address')
      return
    }

    setIsResending(true)
    setError('')

    try {
      const response = await resendOTP(formData.email)
      setSuccess(response.message || 'OTP sent successfully! Please check your email.')
      setResendCooldown(60) // 60 second cooldown
      setTimer(300) // Reset OTP timer to 5 minutes
      setResendAttempts((attempts) => attempts + 1)
    } catch (err) {
      console.error('Resend OTP error:', err)
      setError(err instanceof Error ? err.message : 'Failed to resend OTP. Please try again.')
    } finally {
      setIsResending(false)
    }
  }

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span>Loading...</span>
        </div>
      </div>
    )
  }

  return (
    <FullWidthLayout
      enableLampBackground={false}
      enableGlassmorphism={false}
      maxWidth="none"
      padding="none"
      className="min-h-screen relative"
    >
      {/* Full-width background */}
      <div className="absolute inset-0">
        <BackgroundBeams />
      </div>

      {/* Centered content container */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-4">
        <div className="w-full max-w-lg">
          <CardContainer>
            <CardBody enableGlassmorphism={true} enableGlow={true}>
              <div className="p-8 w-full flex flex-col gap-6">
                <CardItem as="div" translateZ="60" className="text-center">
                  <div className="flex justify-center mb-4">
                    <div className="p-3 bg-primary-100 dark:bg-primary-900/20 rounded-full">
                      <Mail className="h-8 w-8 text-primary-600 dark:text-primary-400" />
                    </div>
                  </div>
                  <h2 className="text-3xl font-bold text-neutral-800 dark:text-neutral-200">
                    Verify Your Email
                  </h2>
                  <p className="text-neutral-600 dark:text-neutral-400">
                    Enter the 6-digit code sent to your email address
                    <br />
                    <span style={{ fontWeight: 'bold', color: timer <= 30 ? 'red' : undefined }}>
                      Time remaining: {Math.floor(timer / 60)}:
                      {(timer % 60).toString().padStart(2, '0')}
                    </span>
                  </p>
                </CardItem>
                <CardItem as="div" translateZ="50">
                  {error && <div className="text-red-500 text-center font-medium">{error}</div>}
                  {success && (
                    <div className="text-green-500 text-center font-medium">
                      <CheckCircle className="h-4 w-4 mr-2 inline" />
                      {success}
                    </div>
                  )}
                </CardItem>
                <form onSubmit={handleSubmit} className="flex flex-col gap-6 mt-2">
                  <CardItem translateZ="50">
                    <div className="flex items-center gap-2">
                      <label htmlFor="otp" className="mb-0 text-neutral-800 dark:text-neutral-200">
                        Email Address:
                      </label>
                      <span
                        className="font-semibold text-blue-700 bg-white/60 px-2 py-1 rounded shadow-sm select-text"
                        style={{ wordBreak: 'break-all' }}
                      >
                        {formData.email}
                      </span>
                    </div>
                  </CardItem>
                  <CardItem translateZ="50">
                    <label htmlFor="otp" className="text-neutral-800 dark:text-neutral-200">
                      Verification Code
                    </label>
                    <input
                      id="otp"
                      name="otp"
                      type="text"
                      placeholder="Enter 6-character code"
                      value={formData.otp}
                      onChange={handleInputChange}
                      disabled={isSubmitting}
                      maxLength={6}
                      autoComplete="one-time-code"
                      required
                      className="w-full p-3 bg-transparent border-b-2 border-neutral-400 dark:border-neutral-600 focus:outline-none focus:border-primary-500 dark:focus:border-primary-400 text-neutral-800 dark:text-neutral-200 text-center text-lg tracking-widest"
                    />
                    <p className="text-xs text-gray-500 text-center mt-1">
                      Check your email for the verification code
                    </p>
                  </CardItem>
                  <CardItem translateZ="60">
                    <button
                      type="submit"
                      className="w-full py-3 text-lg font-semibold bg-primary-500 text-white rounded-lg hover:bg-primary-600 transition-colors"
                      disabled={isSubmitting}
                    >
                      {isSubmitting ? 'Verifying...' : 'Verify Email'}
                    </button>
                  </CardItem>
                  <CardItem translateZ="60">
                    <button
                      type="button"
                      className="w-full py-3 text-lg font-semibold bg-neutral-500 text-white rounded-lg hover:bg-neutral-600 transition-colors"
                      onClick={handleResendOTP}
                      disabled={isResending || resendCooldown > 0}
                    >
                      {isResending
                        ? 'Sending...'
                        : resendCooldown > 0
                          ? `Resend in ${resendCooldown}s`
                          : 'Resend OTP'}
                    </button>
                  </CardItem>
                </form>
                <CardItem
                  translateZ="40"
                  className="text-center text-neutral-600 dark:text-neutral-400 mt-6"
                >
                  Didn&apos;t receive the code? Check your spam folder or{' '}
                  <button
                    type="button"
                    onClick={handleResendOTP}
                    disabled={isResending || resendCooldown > 0}
                    className="text-primary-500 hover:underline font-medium disabled:opacity-50"
                  >
                    resend it
                  </button>
                </CardItem>
                <CardItem
                  translateZ="40"
                  className="text-center text-neutral-600 dark:text-neutral-400 mt-4"
                >
                  <Link to="/login" className="text-primary-500 hover:underline">
                    Back to Login
                  </Link>
                </CardItem>
              </div>
            </CardBody>
          </CardContainer>
        </div>
      </div>
    </FullWidthLayout>
  )
}

export default VerifyOTP

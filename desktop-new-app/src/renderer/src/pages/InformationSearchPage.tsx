import '../global.d.ts'
import React, { useState, useEffect, useCallback } from 'react'
import { Search, ArrowLeft } from 'lucide-react'
import {
  EnhancedModernTable,
  EnhancedColumnDef,
  TableRow,
  CRUDOperations
} from '../components/ui/enhanced-modern-table'
import { FullWidthLayout } from '../components/layout/FullWidthLayout'
import { useThemeContext } from '../context/useThemeContext'
import { cn } from '../lib/aceternity-utils'
import { useNavigate } from 'react-router-dom'
import {
  InformationRecord,
  InformationSearchFilters,
  InformationPaginationOptions
} from '../../../shared/api'

// Create a custom interface that extends TableRow to resolve id type mismatch
interface InformationTableRow extends Omit<InformationRecord, 'id'>, TableRow {
  id: string // Override id to be string as required by TableRow
  originalId: number // Keep original numeric id for API calls
}

const InformationSearchPage: React.FC = () => {
  const { isDark } = useThemeContext()
  const navigate = useNavigate()

  // State for search and filters
  const [searchTerm, setSearchTerm] = useState('')
  const [selectedBank, setSelectedBank] = useState('')
  const [selectedDesignation, setSelectedDesignation] = useState('')

  // State for data
  const [records, setRecords] = useState<InformationRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // State for pagination
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalRecords, setTotalRecords] = useState(0)
  const [pageSize] = useState(20)

  // State for filter options
  const [banks, setBanks] = useState<string[]>([])
  const [designations, setDesignations] = useState<string[]>([])

  const loadFilterOptions = useCallback(async (): Promise<void> => {
    try {
      const [banksData, designationsData] = await Promise.all([
        window.api.informationSearch.getBanks(),
        window.api.informationSearch.getDesignations()
      ])
      setBanks(banksData)
      setDesignations(designationsData)
    } catch (err) {
      console.error('Failed to load filter options:', err)
    }
  }, [])

  const loadData = useCallback(async (): Promise<void> => {
    setLoading(true)
    setError(null)

    try {
      const filters: InformationSearchFilters = {}
      if (searchTerm.trim()) filters.searchTerm = searchTerm.trim()
      if (selectedBank) filters.bank = selectedBank
      if (selectedDesignation) filters.designation = selectedDesignation

      const pagination: InformationPaginationOptions = {
        page: currentPage,
        limit: pageSize
      }

      const result = await window.api.informationSearch.search(filters, pagination)

      setRecords(result.data)
      setTotalPages(result.totalPages)
      setTotalRecords(result.total)
    } catch (err) {
      console.error('Failed to load data:', err)
      setError('Failed to load information records')
    } finally {
      setLoading(false)
    }
  }, [searchTerm, selectedBank, selectedDesignation, currentPage, pageSize])

  // Load filter options on component mount
  useEffect(() => {
    loadFilterOptions()
  }, [loadFilterOptions])

  // Load data when filters or pagination change
  useEffect(() => {
    loadData()
  }, [loadData])

  const handleClearFilters = (): void => {
    setSearchTerm('')
    setSelectedBank('')
    setSelectedDesignation('')
    setCurrentPage(1)
  }

  // Helper function to convert InformationRecord to InformationTableRow
  const convertToTableRow = (record: InformationRecord): InformationTableRow => ({
    ...record,
    id: record.id.toString(),
    originalId: record.id
  })

  // Convert records for table display
  const tableData: InformationTableRow[] = records.map(convertToTableRow)

  // CRUD operations for the table
  const operations: CRUDOperations<InformationTableRow> = {
    onCreate: async (data: Omit<InformationTableRow, 'id'>): Promise<InformationTableRow> => {
      try {
        const recordData = {
          s_no: data.s_no || '',
          bank: data.bank || '',
          name_of_officer: data.name_of_officer || '',
          designation: data.designation || '',
          mobile_no: data.mobile_no || '',
          email: data.email || '',
          last_login: '' // Keep for API compatibility but don't show in table
        }

        const newId = await window.api.informationSearch.create(recordData)
        await loadData()
        await loadFilterOptions()

        // Return the created record with proper ID
        return {
          ...data,
          id: newId.toString(),
          originalId: newId,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        } as InformationTableRow
      } catch (error) {
        console.error('Failed to create record:', error)
        throw new Error('Failed to create record')
      }
    },

    onUpdate: async (
      id: string,
      data: Partial<Omit<InformationTableRow, 'id' | 'originalId'>>
    ): Promise<InformationTableRow> => {
      try {
        const tableRow = tableData.find((row) => row.id === id)
        if (!tableRow) throw new Error('Record not found')

        const updateData: Partial<Omit<InformationRecord, 'id' | 'created_at' | 'updated_at'>> = {}
        if (data.s_no !== undefined) updateData.s_no = data.s_no
        if (data.bank !== undefined) updateData.bank = data.bank
        if (data.name_of_officer !== undefined) updateData.name_of_officer = data.name_of_officer
        if (data.designation !== undefined) updateData.designation = data.designation
        if (data.mobile_no !== undefined) updateData.mobile_no = data.mobile_no
        if (data.email !== undefined) updateData.email = data.email
        if (data.last_login !== undefined) updateData.last_login = data.last_login

        const success = await window.api.informationSearch.update(tableRow.originalId, updateData)
        if (!success) throw new Error('Update failed')

        await loadData()
        await loadFilterOptions()

        // Return updated record
        return {
          ...tableRow,
          ...data,
          updated_at: new Date().toISOString()
        } as InformationTableRow
      } catch (error) {
        console.error('Failed to update record:', error)
        throw new Error('Failed to update record')
      }
    },

    onDelete: async (id: string): Promise<void> => {
      try {
        const tableRow = tableData.find((row) => row.id === id)
        if (!tableRow) throw new Error('Record not found')

        const success = await window.api.informationSearch.delete(tableRow.originalId)
        if (!success) throw new Error('Delete failed')

        await loadData()
        await loadFilterOptions()
      } catch (error) {
        console.error('Failed to delete record:', error)
        throw new Error('Failed to delete record')
      }
    }
  }

  // Define table columns with editable fields
  const columns: EnhancedColumnDef<InformationTableRow>[] = [
    {
      key: 's_no',
      title: 'S.No',
      type: 'text',
      editable: true,
      width: 80,
      render: (value) => <span className="font-mono">{value}</span>
    },
    {
      key: 'bank',
      title: 'Bank',
      type: 'text',
      editable: true,
      width: 150,
      render: (value) => <span className="font-medium">{value}</span>
    },
    {
      key: 'name_of_officer',
      title: 'Officer Name',
      type: 'text',
      editable: true,
      width: 180,
      render: (value) => <span className="font-medium">{value}</span>
    },
    {
      key: 'designation',
      title: 'Designation',
      type: 'text',
      editable: true,
      width: 120,
      render: (value) => <span className="text-sm">{value}</span>
    },
    {
      key: 'mobile_no',
      title: 'Mobile',
      type: 'text',
      editable: true,
      width: 120,
      render: (value) => <span className="font-mono">{value}</span>
    },
    {
      key: 'email',
      title: 'Email',
      type: 'text',
      editable: true,
      width: 200,
      render: (value) => <span className="text-sm">{value}</span>
    }
  ]

  if (loading && records.length === 0) {
    return (
      <FullWidthLayout
        enableLampBackground={true}
        enableGlassmorphism={true}
        className="min-h-screen"
      >
        <div className="flex items-center justify-center min-h-[400px]">
          <div className="text-center">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
            <p className={cn('text-lg', isDark ? 'text-white' : 'text-gray-900')}>
              Loading information records...
            </p>
          </div>
        </div>
      </FullWidthLayout>
    )
  }

  return (
    <FullWidthLayout
      enableLampBackground={true}
      enableGlassmorphism={true}
      className="min-h-screen"
    >
      {/* Header */}
      <div
        className={`sticky top-0 z-10 backdrop-blur-md border-b ${
          isDark
            ? 'bg-gray-900/80 border-gray-700'
            : 'bg-white/80 border-gray-200'
        }`}
      >
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate(-1)}
              className={`p-2 rounded-lg transition-colors ${
                isDark
                  ? 'hover:bg-gray-700 text-gray-300'
                  : 'hover:bg-gray-100 text-gray-600'
              }`}
            >
              <ArrowLeft size={20} />
            </button>
            <h1
              className={`text-xl font-semibold ${
                isDark ? 'text-white' : 'text-gray-900'
              }`}
            >
              Information Search
            </h1>
          </div>

          {/* Search Bar */}
          <div className="flex items-center gap-4 max-w-md">
            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                placeholder="Search by name, bank, email..."
                className={`w-full pl-10 pr-4 py-2 border rounded-lg ${
                  isDark
                    ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400'
                    : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                }`}
              />
            </div>

            {/* Filter dropdowns */}
            <select
              value={selectedBank}
              onChange={(e) => setSelectedBank(e.target.value)}
              className={`px-3 py-2 border rounded-lg ${
                isDark
                  ? 'bg-gray-800 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              <option value="">All Banks</option>
              {banks.map((bank) => (
                <option key={bank} value={bank}>
                  {bank}
                </option>
              ))}
            </select>

            <select
              value={selectedDesignation}
              onChange={(e) => setSelectedDesignation(e.target.value)}
              className={`px-3 py-2 border rounded-lg ${
                isDark
                  ? 'bg-gray-800 border-gray-600 text-white'
                  : 'bg-white border-gray-300 text-gray-900'
              }`}
            >
              <option value="">All Designations</option>
              {designations.map((designation) => (
                <option key={designation} value={designation}>
                  {designation}
                </option>
              ))}
            </select>

            {(searchTerm || selectedBank || selectedDesignation) && (
              <button
                onClick={handleClearFilters}
                className={`px-3 py-2 rounded-lg transition-colors ${
                  isDark
                    ? 'bg-gray-700 hover:bg-gray-600 text-white'
                    : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                }`}
              >
                Clear
              </button>
            )}
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        {/* Error Display */}
        {error && (
          <div
            className={`mb-4 p-4 rounded-lg border ${
              isDark
                ? 'bg-red-900/20 border-red-800/50 text-red-300'
                : 'bg-red-50 border-red-200 text-red-800'
            }`}
          >
            {error}
          </div>
        )}

        {/* Results Summary */}
        <div className="flex justify-between items-center mb-4">
          <p
            className={`text-sm ${
              isDark ? 'text-gray-400' : 'text-gray-600'
            }`}
          >
            Showing {records.length} of {totalRecords} records
          </p>
          <div className="flex gap-2">
            <button
              disabled={currentPage === 1}
              onClick={() => setCurrentPage(currentPage - 1)}
              className={`px-3 py-1 text-sm rounded border transition-colors ${
                currentPage === 1
                  ? 'opacity-50 cursor-not-allowed'
                  : isDark
                    ? 'bg-gray-700 hover:bg-gray-600 text-white border-gray-600'
                    : 'bg-white hover:bg-gray-50 text-gray-700 border-gray-300'
              }`}
            >
              Previous
            </button>
            <span
              className={`px-3 py-1 text-sm ${
                isDark ? 'text-gray-300' : 'text-gray-700'
              }`}
            >
              Page {currentPage} of {totalPages}
            </span>
            <button
              disabled={currentPage === totalPages}
              onClick={() => setCurrentPage(currentPage + 1)}
              className={`px-3 py-1 text-sm rounded border transition-colors ${
                currentPage === totalPages
                  ? 'opacity-50 cursor-not-allowed'
                  : isDark
                    ? 'bg-gray-700 hover:bg-gray-600 text-white border-gray-600'
                    : 'bg-white hover:bg-gray-50 text-gray-700 border-gray-300'
              }`}
            >
              Next
            </button>
          </div>
        </div>

        {/* Data Table */}
        <div
          className={`backdrop-blur-md border rounded-lg shadow-lg ${
            isDark ? 'bg-white/10 border-gray-700' : 'bg-white/80 border-gray-200'
          }`}
        >
          <EnhancedModernTable
            data={tableData}
            columns={columns}
            operations={operations}
            loading={loading}
            config={{
              enableSearch: false, // Search is in header
              enablePagination: false, // Pagination is custom
              enableSorting: true,
              enableSelection: false,
              enableGlassmorphism: true,
              enableRowActions: true,
              pageSize: 20,
              searchPlaceholder: 'Search information records...'
            }}
            emptyMessage="No information records found"
            className="w-full"
          />
        </div>
      </div>
    </FullWidthLayout>
  )
}

export default InformationSearchPage

import React, { useState, useEffect, useCallback } from 'react'
import { ComplaintData, TransactionData } from '../../../shared/api'
import UnifiedNavbar from '../components/UnifiedNavbar'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import { Button } from '../components/ui/Button'
import { useThemeContext } from '../context/useThemeContext'
import { cn } from '../lib/aceternity-utils'
// Motion imports removed - no longer needed without IFSC scanning animation
import {
  FullWidthLayout,
  ResponsiveGrid,
  FullWidthSection
} from '../components/layout/FullWidthLayout'

// IFSC-related interfaces removed - bank data is now pre-enhanced during upload

interface CommonAccount {
  account: string
  count: number
  complaints: string[]
  ifsc?: string
  bank?: string
  location?: {
    city?: string
    state?: string
    district?: string
    address?: string
  }
}

interface AccountHolder {
  account: string
  name?: string
  address?: string
  city?: string
  state?: string
  district?: string
  pincode?: string
}

const AllStatsPage: React.FC = () => {
  const { isDark } = useThemeContext()
  const [complaints, setComplaints] = useState<ComplaintData[]>([])
  const [loading, setLoading] = useState(true)

  // Common account analysis state - only keep common account analysis
  const [selectedComplaints, setSelectedComplaints] = useState<string[]>([])
  const [commonAccounts, setCommonAccounts] = useState<CommonAccount[]>([])
  const [, setAccountHolders] = useState<Map<string, AccountHolder>>(new Map())
  const [analysisLoading, setAnalysisLoading] = useState(false)
  const [complaintSearchQuery, setComplaintSearchQuery] = useState<string>('')

  const fetchComplaints = useCallback(async (): Promise<void> => {
    try {
      setLoading(true)
      const dbComplaints: ComplaintData[] = await window.api.database.getComplaints()
      setComplaints(dbComplaints)
    } catch (error) {
      console.error('Error fetching complaints:', error)
    } finally {
      setLoading(false)
    }
  }, [])

  // IFSC extraction removed - bank data is now pre-enhanced during upload

  // IFSC scanning removed - bank data is now pre-enhanced during upload

  // IFSC scanning removed - bank data is now pre-enhanced during upload

  useEffect(() => {
    fetchComplaints()
  }, [fetchComplaints])

  // Common account analysis functions
  const handleAnalyzeCommonAccounts = useCallback(async (): Promise<void> => {
    setAnalysisLoading(true)
    try {
      const selectedComplaintData = complaints.filter((c) => selectedComplaints.includes(c.id))
      const accountMap: Record<
        string,
        { count: number; complaints: string[]; ifsc?: string; transactions: TransactionData[] }
      > = {}

      selectedComplaintData.forEach((complaint) => {
        if (complaint.layer_transactions) {
          Object.values(complaint.layer_transactions)
            .flat()
            .forEach((txn: TransactionData | unknown) => {
              if (txn && typeof txn === 'object' && 'receiver_account' in txn) {
                const transaction = txn as TransactionData
                const account = transaction.receiver_account // Only search receiver accounts
                const ifsc = transaction.receiver_ifsc

                if (account) {
                  if (!accountMap[account]) {
                    accountMap[account] = { count: 0, complaints: [], ifsc, transactions: [] }
                  }
                  accountMap[account].count++
                  accountMap[account].transactions.push(transaction)
                  if (!accountMap[account].complaints.includes(complaint.title)) {
                    accountMap[account].complaints.push(complaint.title)
                  }
                  if (ifsc && !accountMap[account].ifsc) {
                    accountMap[account].ifsc = ifsc
                  }
                }
              }
            })
        }
      })

      // Filter accounts that appear in multiple complaints (not just multiple times in same complaint)
      const commons = Object.entries(accountMap)
        .filter(([, v]) => v.complaints.length > 1)
        .map(([account, v]) => ({
          account,
          count: v.count,
          complaints: v.complaints,
          ifsc: v.ifsc
        }))

      // Bank data is now pre-enhanced during upload, no need for IFSC lookup
      setCommonAccounts(commons)
    } catch (error) {
      console.error('Error analyzing common accounts:', error)
    } finally {
      setAnalysisLoading(false)
    }
  }, [complaints, selectedComplaints])

  // Update account holder information (currently unused but kept for future use)
  const updateAccountHolder = useCallback(
    (_account: string, _holderInfo: Partial<AccountHolder>): void => {
      setAccountHolders((prev) => {
        const updated = new Map(prev)
        const existing = updated.get(_account) || { account: _account }
        updated.set(_account, { ...existing, ..._holderInfo })
        return updated
      })
    },
    []
  )

  // Suppress unused variable warning by referencing it
  void updateAccountHolder

  // IFSC-related calculations removed - bank data is now pre-enhanced during upload

  // Calculate comprehensive analytics
  const analytics = React.useMemo(() => {
    const totalComplaints = complaints.length
    const currentMonth = new Date().getMonth()
    const currentYear = new Date().getFullYear()

    // Monthly statistics
    const monthlyStats = complaints.reduce((acc, complaint) => {
      const date = new Date(complaint.created_at)
      const monthKey = `${date.getFullYear()}-${date.getMonth()}`
      if (!acc[monthKey]) {
        acc[monthKey] = { count: 0, amount: 0 }
      }
      acc[monthKey].count++

      // Calculate total amount from layer_transactions
      if (complaint.layer_transactions) {
        Object.values(complaint.layer_transactions).forEach((layer) => {
          if (Array.isArray(layer)) {
            layer.forEach((transaction: any) => {
              const amount = parseFloat(transaction.amount || '0')
              if (!isNaN(amount)) {
                acc[monthKey].amount += amount
              }
            })
          }
        })
      }
      return acc
    }, {} as Record<string, { count: number; amount: number }>)

    // Fraud type analysis
    const fraudTypeStats = complaints.reduce((acc, complaint) => {
      const fraudType = complaint.fraud_type || 'Unknown'
      if (!acc[fraudType]) {
        acc[fraudType] = { count: 0, amount: 0 }
      }
      acc[fraudType].count++

      // Calculate amount for this fraud type
      if (complaint.layer_transactions) {
        Object.values(complaint.layer_transactions).forEach((layer) => {
          if (Array.isArray(layer)) {
            layer.forEach((transaction: any) => {
              const amount = parseFloat(transaction.amount || '0')
              if (!isNaN(amount)) {
                acc[fraudType].amount += amount
              }
            })
          }
        })
      }
      return acc
    }, {} as Record<string, { count: number; amount: number }>)

    // Calculate totals
    const totalFraudAmount = Object.values(fraudTypeStats).reduce((sum, stat) => sum + stat.amount, 0)

    // Monthly trend (compare current month with previous)
    const currentMonthKey = `${currentYear}-${currentMonth}`
    const previousMonthKey = `${currentMonth === 0 ? currentYear - 1 : currentYear}-${currentMonth === 0 ? 11 : currentMonth - 1}`
    const currentMonthCount = monthlyStats[currentMonthKey]?.count || 0
    const previousMonthCount = monthlyStats[previousMonthKey]?.count || 0
    const monthlyTrend = previousMonthCount > 0 ? ((currentMonthCount - previousMonthCount) / previousMonthCount) * 100 : 0

    return {
      totalComplaints,
      totalFraudAmount,
      monthlyStats,
      fraudTypeStats,
      monthlyTrend,
      currentMonthCount,
      previousMonthCount
    }
  }, [complaints])

  if (loading) {
    return (
      <FullWidthLayout>
        <UnifiedNavbar title="All Statistics" showBackButton />
        <FullWidthSection>
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-500 mx-auto mb-4"></div>
              <p className={cn('text-lg', isDark ? 'text-white' : 'text-gray-900')}>
                Loading complaints...
              </p>
            </div>
          </div>
        </FullWidthSection>
      </FullWidthLayout>
    )
  }

  return (
    <FullWidthLayout>
      <UnifiedNavbar title="All Statistics & Analytics" showBackButton />

      {/* Comprehensive Analytics Overview */}
      <FullWidthSection className="space-y-6">
        {/* Overview Cards */}
        <ResponsiveGrid columns={{ sm: 1, md: 2, lg: 4 }} className="gap-6">
          <Card
            className={cn(
              'backdrop-blur-md border-0 shadow-lg',
              isDark ? 'bg-white/10' : 'bg-white/80'
            )}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Complaints</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.totalComplaints}</div>
            </CardContent>
          </Card>

          <Card
            className={cn(
              'backdrop-blur-md border-0 shadow-lg',
              isDark ? 'bg-white/10' : 'bg-white/80'
            )}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Total Fraud Amount</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">₹{analytics.totalFraudAmount.toLocaleString()}</div>
            </CardContent>
          </Card>

          <Card
            className={cn(
              'backdrop-blur-md border-0 shadow-lg',
              isDark ? 'bg-white/10' : 'bg-white/80'
            )}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">This Month</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{analytics.currentMonthCount}</div>
              <div className={cn(
                'text-sm',
                analytics.monthlyTrend > 0 ? 'text-red-500' : analytics.monthlyTrend < 0 ? 'text-green-500' : 'text-gray-500'
              )}>
                {analytics.monthlyTrend > 0 ? '↑' : analytics.monthlyTrend < 0 ? '↓' : '→'} {Math.abs(analytics.monthlyTrend).toFixed(1)}%
              </div>
            </CardContent>
          </Card>

          <Card
            className={cn(
              'backdrop-blur-md border-0 shadow-lg',
              isDark ? 'bg-white/10' : 'bg-white/80'
            )}
          >
            <CardHeader className="pb-2">
              <CardTitle className="text-sm font-medium">Fraud Types</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{Object.keys(analytics.fraudTypeStats).length}</div>
            </CardContent>
          </Card>
        </ResponsiveGrid>

        {/* Charts and Analytics */}
        <ResponsiveGrid columns={{ sm: 1, lg: 2 }} className="gap-6">
          {/* Fraud Type Distribution */}
          <Card
            className={cn(
              'backdrop-blur-md border-0 shadow-lg',
              isDark ? 'bg-white/10' : 'bg-white/80'
            )}
          >
            <CardHeader>
              <CardTitle>Fraud Amount by Type</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(analytics.fraudTypeStats)
                  .sort(([,a], [,b]) => b.amount - a.amount)
                  .slice(0, 5)
                  .map(([type, stats]) => {
                    const percentage = analytics.totalFraudAmount > 0 ? (stats.amount / analytics.totalFraudAmount) * 100 : 0
                    return (
                      <div key={type} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">{type}</span>
                          <span className="text-sm text-muted-foreground">
                            ₹{stats.amount.toLocaleString()} ({percentage.toFixed(1)}%)
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${percentage}%` }}
                          />
                        </div>
                      </div>
                    )
                  })}
              </div>
            </CardContent>
          </Card>

          {/* Monthly Trend */}
          <Card
            className={cn(
              'backdrop-blur-md border-0 shadow-lg',
              isDark ? 'bg-white/10' : 'bg-white/80'
            )}
          >
            <CardHeader>
              <CardTitle>Monthly Complaint Trend</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(analytics.monthlyStats)
                  .sort(([a], [b]) => b.localeCompare(a))
                  .slice(0, 6)
                  .map(([monthKey, stats]) => {
                    const [year, month] = monthKey.split('-')
                    const monthName = new Date(parseInt(year), parseInt(month)).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })
                    const maxCount = Math.max(...Object.values(analytics.monthlyStats).map(s => s.count))
                    const percentage = maxCount > 0 ? (stats.count / maxCount) * 100 : 0

                    return (
                      <div key={monthKey} className="space-y-2">
                        <div className="flex justify-between items-center">
                          <span className="text-sm font-medium">{monthName}</span>
                          <span className="text-sm text-muted-foreground">
                            {stats.count} complaints
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-green-500 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${percentage}%` }}
                          />
                        </div>
                      </div>
                    )
                  })}
              </div>
            </CardContent>
          </Card>
        </ResponsiveGrid>
      </FullWidthSection>

      {/* Common Account Analysis - always show */}
      <FullWidthSection className="space-y-6">
        <div className="space-y-6">
          {/* Complaint Selection */}
          <Card
            className={cn(
              'backdrop-blur-md border shadow-lg',
              isDark ? 'bg-white/10 border-white/10' : 'bg-white/80 border-gray-200/50'
            )}
          >
            <CardHeader>
              <CardTitle>Search Complaints by Number</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 mb-4">
                <div className="flex gap-4">
                  <input
                    type="text"
                    placeholder="Enter complaint numbers (comma-separated)"
                    value={complaintSearchQuery}
                    onChange={(e) => setComplaintSearchQuery(e.target.value)}
                    className={cn(
                      'flex-1 px-3 py-2 border rounded-lg',
                      isDark
                        ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400'
                        : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                    )}
                  />
                  <Button
                    onClick={() => {
                      const searchNumbers = complaintSearchQuery
                        .split(',')
                        .map((num) => num.trim())
                        .filter((num) => num.length > 0)

                      const matchingComplaints = complaints.filter((complaint) =>
                        searchNumbers.some(
                          (searchNum) =>
                            complaint.title.includes(searchNum) ||
                            (
                              complaint.metadata as { complaint_number?: string }
                            )?.complaint_number?.includes(searchNum)
                        )
                      )

                      setSelectedComplaints(matchingComplaints.map((c) => c.id))
                    }}
                    variant="outline"
                  >
                    Search
                  </Button>
                </div>

                {/* Show selected complaints */}
                {selectedComplaints.length > 0 && (
                  <div className="space-y-2">
                    <h4 className="font-medium">Selected Complaints:</h4>
                    <div className="flex flex-wrap gap-2">
                      {selectedComplaints.map((id) => {
                        const complaint = complaints.find((c) => c.id === id)
                        return complaint ? (
                          <span
                            key={id}
                            className={cn(
                              'px-2 py-1 rounded text-xs',
                              isDark ? 'bg-blue-600 text-white' : 'bg-blue-100 text-blue-800'
                            )}
                          >
                            {complaint.title}
                            <button
                              onClick={() =>
                                setSelectedComplaints((prev) => prev.filter((cId) => cId !== id))
                              }
                              className="ml-1 text-xs hover:text-red-500"
                            >
                              ×
                            </button>
                          </span>
                        ) : null
                      })}
                    </div>
                  </div>
                )}
              </div>
              <div className="flex gap-4">
                <Button
                  onClick={handleAnalyzeCommonAccounts}
                  disabled={selectedComplaints.length < 2 || analysisLoading}
                  className="min-w-[120px]"
                >
                  {analysisLoading ? 'Analyzing...' : 'Analyze Common Accounts'}
                </Button>
                <Button
                  onClick={() => setSelectedComplaints(complaints.map((c) => c.id))}
                  variant="outline"
                >
                  Select All
                </Button>
                <Button onClick={() => setSelectedComplaints([])} variant="outline">
                  Clear All
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Common Accounts Results */}
          {commonAccounts.length > 0 && (
            <Card
              className={cn(
                'backdrop-blur-md border shadow-lg',
                isDark ? 'bg-white/10 border-white/10' : 'bg-white/80 border-gray-200/50'
              )}
            >
              <CardHeader>
                <CardTitle>Common Accounts Found ({commonAccounts.length})</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="overflow-x-auto">
                  <table
                    className={cn(
                      'w-full border-collapse',
                      isDark ? 'text-white' : 'text-gray-900'
                    )}
                  >
                    <thead>
                      <tr
                        className={cn('border-b', isDark ? 'border-gray-600' : 'border-gray-300')}
                      >
                        <th className="text-left p-3 font-medium">Account Number</th>
                        <th className="text-left p-3 font-medium">Bank</th>
                        <th className="text-left p-3 font-medium">IFSC</th>
                        <th className="text-left p-3 font-medium">Location</th>
                        <th className="text-left p-3 font-medium">Transactions</th>
                        <th className="text-left p-3 font-medium">Complaints</th>
                      </tr>
                    </thead>
                    <tbody>
                      {commonAccounts.map((account) => (
                        <tr
                          key={account.account}
                          className={cn(
                            'border-b hover:bg-black/5 dark:hover:bg-white/5',
                            isDark ? 'border-gray-700' : 'border-gray-200'
                          )}
                        >
                          <td className="p-3">
                            <div className="font-mono text-sm">{account.account}</div>
                          </td>
                          <td className="p-3">
                            <div className="text-sm">{account.bank || 'N/A'}</div>
                          </td>
                          <td className="p-3">
                            <div className="font-mono text-sm">{account.ifsc || 'N/A'}</div>
                          </td>
                          <td className="p-3">
                            <div className="text-sm">
                              {account.location
                                ? `${account.location.city}, ${account.location.state}`
                                : 'N/A'}
                            </div>
                          </td>
                          <td className="p-3">
                            <span className="bg-blue-500 text-white px-2 py-1 rounded text-xs">
                              {account.count}
                            </span>
                          </td>
                          <td className="p-3">
                            <div className="flex flex-wrap gap-1">
                              {account.complaints.map((complaintTitle, i) => (
                                <span
                                  key={i}
                                  className={cn(
                                    'px-2 py-1 rounded text-xs',
                                    isDark ? 'bg-white/10 text-white' : 'bg-gray-200 text-gray-800'
                                  )}
                                >
                                  {complaintTitle}
                                </span>
                              ))}
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </FullWidthSection>
    </FullWidthLayout>
  )
}

export default AllStatsPage

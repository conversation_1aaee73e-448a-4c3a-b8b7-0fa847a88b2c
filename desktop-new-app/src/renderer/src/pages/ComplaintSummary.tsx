import React, { useEffect, useState, useCallback } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { CardContainer, CardBody, CardItem } from '../components/ui/3d-card'
import { ArrowLeft, Printer, Edit3, Save } from 'lucide-react'
import { backendService } from '../services/backendService'
import { ComplaintData } from '../../../shared/api'
import { FullWidthLayout } from '../components/layout/FullWidthLayout'
import { useThemeContext } from '../context/useThemeContext'
import { formatDate, formatDateTime } from '../utils/dateUtils'

const ComplaintSummary: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { isDark } = useThemeContext()
  const [complaint, setComplaint] = useState<ComplaintData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [notes, setNotes] = useState<string>('')
  const [isEditingNotes, setIsEditingNotes] = useState(false)
  const [tempNotes, setTempNotes] = useState<string>('')

  useEffect(() => {
    const loadComplaint = async (): Promise<void> => {
      if (!id) return
      setLoading(true)
      try {
        const data = await backendService.getLocalComplaint(id)
        setComplaint(data)
        // Load notes from metadata or initialize empty
        const existingNotes = ((data?.metadata as Record<string, unknown>)?.notes as string) || ''
        setNotes(existingNotes)
        setTempNotes(existingNotes)
      } catch {
        setError('Failed to load complaint data')
      } finally {
        setLoading(false)
      }
    }
    loadComplaint()
  }, [id])

  // Save notes to complaint metadata
  const saveNotes = useCallback(async () => {
    if (!id || !complaint) return

    try {
      const updatedMetadata = {
        ...complaint.metadata,
        notes: tempNotes,
        notes_updated_at: new Date().toISOString()
      }

      await backendService.updateComplaint(id, {
        metadata: updatedMetadata
      })

      setNotes(tempNotes)
      setIsEditingNotes(false)

      // Update local state
      setComplaint((prev) =>
        prev
          ? {
              ...prev,
              metadata: updatedMetadata
            }
          : null
      )
    } catch (error) {
      console.error('Failed to save notes:', error)
      alert('Failed to save notes. Please try again.')
    }
  }, [id, complaint, tempNotes])

  // Cancel notes editing
  const cancelNotesEdit = useCallback(() => {
    setTempNotes(notes)
    setIsEditingNotes(false)
  }, [notes])

  // Calculate transaction statistics from unified data
  const getTransactionStats = useCallback(() => {
    if (!complaint?.layer_transactions)
      return { totalTransactions: 0, totalAmount: 0, layerCount: 0 }

    let totalTransactions = 0
    let totalAmount = 0
    const layers = Object.keys(complaint.layer_transactions)

    Object.values(complaint.layer_transactions).forEach((layerData) => {
      const layer = layerData as Record<string, unknown>
      if (layer?.transactions && Array.isArray(layer.transactions)) {
        totalTransactions += layer.transactions.length
        layer.transactions.forEach((txn) => {
          const transaction = txn as Record<string, unknown>
          const core = transaction.core as Record<string, unknown>
          const amount = parseFloat((core?.amount as string) || '0')
          if (!isNaN(amount)) {
            totalAmount += amount
          }
        })
      }
    })

    return {
      totalTransactions,
      totalAmount,
      layerCount: layers.length
    }
  }, [complaint])

  if (loading) {
    return (
      <FullWidthLayout
        enableLampBackground={true}
        enableGlassmorphism={true}
        className="min-h-screen flex items-center justify-center"
      >
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </FullWidthLayout>
    )
  }

  if (error || !complaint) {
    return (
      <FullWidthLayout
        enableLampBackground={true}
        enableGlassmorphism={true}
        className="min-h-screen"
      >
        {/* Header */}
        <div
          className={`sticky top-0 z-10 backdrop-blur-md border-b ${
            isDark ? 'bg-gray-900/80 border-gray-700' : 'bg-white/80 border-gray-200'
          }`}
        >
          <div className="flex items-center justify-between p-4">
            <div className="flex items-center gap-4">
              <button
                onClick={() => navigate(-1)}
                className={`p-2 rounded-lg transition-colors ${
                  isDark ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-600'
                }`}
              >
                <ArrowLeft size={20} />
              </button>
              <h1 className={`text-xl font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>
                Complaint Summary
              </h1>
            </div>
          </div>
        </div>

        <div className="p-6 w-full">
          <div
            className={`border rounded-lg p-4 ${
              isDark
                ? 'bg-red-900/20 border-red-800/50 text-red-300'
                : 'bg-red-50 border-red-200 text-red-800'
            }`}
          >
            {error || 'No complaint data available'}
          </div>
        </div>
      </FullWidthLayout>
    )
  }

  const stats = getTransactionStats()

  return (
    <FullWidthLayout
      enableLampBackground={true}
      enableGlassmorphism={true}
      className="min-h-screen"
    >
      {/* Header */}
      <div
        className={`sticky top-0 z-10 backdrop-blur-md border-b ${
          isDark ? 'bg-gray-900/80 border-gray-700' : 'bg-white/80 border-gray-200'
        }`}
      >
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-4">
            <button
              onClick={() => navigate(-1)}
              className={`p-2 rounded-lg transition-colors ${
                isDark ? 'hover:bg-gray-700 text-gray-300' : 'hover:bg-gray-100 text-gray-600'
              }`}
            >
              <ArrowLeft size={20} />
            </button>
            <h1 className={`text-xl font-semibold ${isDark ? 'text-white' : 'text-gray-900'}`}>
              Complaint Summary
            </h1>
          </div>
          <button
            onClick={() => window.print()}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
          >
            <Printer size={20} />
            Print
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6 max-w-6xl mx-auto">
        {/* Complaint Overview Card */}
        <CardContainer className="inter-var mb-6">
          <CardBody
            className={`relative group/card w-full h-auto rounded-xl p-6 border backdrop-blur-sm ${
              isDark
                ? 'bg-gray-900/50 border-gray-700 hover:shadow-2xl hover:shadow-blue-500/[0.1]'
                : 'bg-white/50 border-gray-200 hover:shadow-xl'
            }`}
          >
            <CardItem
              translateZ="50"
              className={`text-2xl font-bold ${isDark ? 'text-white' : 'text-gray-900'}`}
            >
              Complaint Overview
            </CardItem>
            <CardItem
              as="p"
              translateZ="60"
              className={`text-sm mt-2 ${isDark ? 'text-gray-300' : 'text-gray-600'}`}
            >
              Complaint #
              {((complaint.metadata as Record<string, unknown>)?.complaint_number as string) ||
                complaint.id.slice(0, 8)}
            </CardItem>

            {/* Basic Information Grid */}
            <div className="mt-6 grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <div>
                <h3 className={`font-semibold mb-2 ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                  Complainant Name
                </h3>
                <p className={`${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                  {((complaint.metadata as Record<string, unknown>)?.complainant_name as string) ||
                    'Not specified'}
                </p>
              </div>
              <div>
                <h3 className={`font-semibold mb-2 ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                  Fraud Category
                </h3>
                <p className={`${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                  {((complaint.metadata as Record<string, unknown>)?.subcategory as string) ||
                    complaint.fraud_type ||
                    'Not specified'}
                </p>
              </div>
              <div>
                <h3 className={`font-semibold mb-2 ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                  Date of Complaint
                </h3>
                <p className={`${isDark ? 'text-gray-400' : 'text-gray-600'}`}>
                  {formatDate(
                    ((complaint.metadata as Record<string, unknown>)?.date as string) ||
                      complaint.created_at
                  )}
                </p>
              </div>
              <div>
                <h3 className={`font-semibold mb-2 ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                  Total Amount
                </h3>
                <p className={`text-lg font-semibold ${isDark ? 'text-red-400' : 'text-red-600'}`}>
                  ₹{stats.totalAmount.toLocaleString('en-IN')}
                </p>
              </div>
              <div>
                <h3 className={`font-semibold mb-2 ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                  Total Transactions
                </h3>
                <p
                  className={`text-lg font-semibold ${isDark ? 'text-blue-400' : 'text-blue-600'}`}
                >
                  {stats.totalTransactions}
                </p>
              </div>
              <div>
                <h3 className={`font-semibold mb-2 ${isDark ? 'text-gray-300' : 'text-gray-700'}`}>
                  Transaction Layers
                </h3>
                <p
                  className={`text-lg font-semibold ${
                    isDark ? 'text-green-400' : 'text-green-600'
                  }`}
                >
                  {stats.layerCount}
                </p>
              </div>
            </div>
          </CardBody>
        </CardContainer>

        {/* Notes Section */}
        <CardContainer className="inter-var">
          <CardBody
            className={`relative group/card w-full h-auto rounded-xl p-6 border backdrop-blur-sm ${
              isDark ? 'bg-gray-900/50 border-gray-700' : 'bg-white/50 border-gray-200'
            }`}
          >
            <div className="flex items-center justify-between mb-4">
              <CardItem
                translateZ="50"
                className={`text-xl font-bold ${isDark ? 'text-white' : 'text-gray-900'}`}
              >
                Investigation Notes
              </CardItem>
              <button
                onClick={() => setIsEditingNotes(!isEditingNotes)}
                className="flex items-center gap-2 px-3 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
              >
                {isEditingNotes ? <Save size={16} /> : <Edit3 size={16} />}
                {isEditingNotes ? 'Save' : 'Edit'}
              </button>
            </div>

            {isEditingNotes ? (
              <div className="space-y-4">
                <textarea
                  value={tempNotes}
                  onChange={(e) => setTempNotes(e.target.value)}
                  placeholder="Add investigation notes, observations, or important details about this complaint..."
                  className={`w-full h-32 p-3 rounded-lg border resize-none ${
                    isDark
                      ? 'bg-gray-800 border-gray-600 text-white placeholder-gray-400'
                      : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                  }`}
                />
                <div className="flex gap-2">
                  <button
                    onClick={saveNotes}
                    className="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-lg transition-colors"
                  >
                    Save Notes
                  </button>
                  <button
                    onClick={cancelNotesEdit}
                    className={`px-4 py-2 rounded-lg transition-colors ${
                      isDark
                        ? 'bg-gray-700 hover:bg-gray-600 text-white'
                        : 'bg-gray-200 hover:bg-gray-300 text-gray-700'
                    }`}
                  >
                    Cancel
                  </button>
                </div>
              </div>
            ) : (
              <div
                className={`min-h-[100px] p-3 rounded-lg border ${
                  isDark
                    ? 'bg-gray-800/50 border-gray-600 text-gray-300'
                    : 'bg-gray-50 border-gray-200 text-gray-700'
                }`}
              >
                {notes || (
                  <span className={`italic ${isDark ? 'text-gray-500' : 'text-gray-400'}`}>
                    No notes added yet. Click Edit to add investigation notes.
                  </span>
                )}
              </div>
            )}

            {notes && (
              <div className="mt-4 text-xs text-gray-500">
                Last updated:{' '}
                {formatDateTime(
                  ((complaint.metadata as Record<string, unknown>)?.notes_updated_at as string) ||
                    complaint.updated_at
                )}
              </div>
            )}
          </CardBody>
        </CardContainer>
      </div>
    </FullWidthLayout>
  )
}

export default ComplaintSummary

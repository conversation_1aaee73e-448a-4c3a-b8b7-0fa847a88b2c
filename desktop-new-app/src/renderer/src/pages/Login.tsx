import React, { useState, useContext } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { AuthContext } from '../context/AuthContext'
import { CardContainer, CardBody, CardItem } from '../components/ui/3d-card'
import { BackgroundBeams } from '../components/ui/background-beams'
import { FullWidthLayout } from '../components/layout/FullWidthLayout'

interface LoginProps {
  onLoginSuccess?: () => void
}

const Login: React.FC<LoginProps> = ({ onLoginSuccess }) => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const navigate = useNavigate()
  const authContext = useContext(AuthContext)

  const handleLogin = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault()
    setLoading(true)
    setError('')
    try {
      if (!authContext) throw new Error('AuthContext not available')
      const result = await authContext.login({ email, password })
      setLoading(false)
      if (result.success) {
        if (onLoginSuccess) onLoginSuccess()
        navigate('/dashboard')
      } else {
        setError(result.error || 'Login failed')
      }
    } catch (err: unknown) {
      if (err instanceof Error) {
        setError(err.message || 'Login failed')
      } else {
        setError('Login failed')
      }
      setLoading(false)
    }
  }

  return (
    <FullWidthLayout
      enableLampBackground={false}
      enableGlassmorphism={false}
      maxWidth="none"
      padding="none"
      className="min-h-screen relative"
    >
      {/* Full-width background */}
      <div className="absolute inset-0">
        <BackgroundBeams />
      </div>

      {/* Centered content container */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-4">
        <div className="w-full max-w-md">
          <CardContainer>
            <CardBody enableGlassmorphism={true} enableGlow={true}>
              <form onSubmit={handleLogin} className="p-8 w-full flex flex-col gap-6">
                <CardItem
                  as="h2"
                  translateZ="60"
                  className="text-3xl font-bold text-center text-black dark:text-white"
                >
                  Login
                </CardItem>
                <CardItem translateZ="50">
                  <input
                    type="email"
                    placeholder="Email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full p-3 bg-transparent border-b-2 border-neutral-400 dark:border-neutral-600 focus:outline-none focus:border-primary-500 dark:focus:border-primary-400 text-black dark:text-white"
                    required
                  />
                </CardItem>
                <CardItem translateZ="50">
                  <input
                    type="password"
                    placeholder="Password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full p-3 bg-transparent border-b-2 border-neutral-400 dark:border-neutral-600 focus:outline-none focus:border-primary-500 dark:focus:border-primary-400 text-black dark:text-white"
                    required
                  />
                </CardItem>
                {error && (
                  <CardItem translateZ="40" className="text-red-500 text-center font-medium">
                    {error}
                  </CardItem>
                )}
                <CardItem translateZ="60">
                  <button
                    type="submit"
                    className="w-full py-3 text-lg font-semibold bg-primary-500 text-black dark:text-white rounded-lg hover:bg-primary-600 transition-colors"
                    disabled={loading}
                  >
                    {loading ? 'Logging in...' : 'Login'}
                  </button>
                </CardItem>
                <CardItem
                  translateZ="40"
                  className="text-center text-neutral-600 dark:text-neutral-400"
                >
                  New user?{' '}
                  <Link to="/signup" className="text-primary-500 hover:underline">
                    Sign up
                  </Link>
                </CardItem>
              </form>
            </CardBody>
          </CardContainer>
        </div>
      </div>
    </FullWidthLayout>
  )
}

export default Login

import React, { useRef, useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Upload, Info } from 'lucide-react'
import FraudTypeSelector from '../components/FraudTypeSelector'
import UnifiedNavbar from '../components/UnifiedNavbar'
import { FullWidthLayout, FullWidthSection } from '../components/layout/FullWidthLayout'
import { useThemeContext } from '../context/useThemeContext'
import { cn } from '../lib/aceternity-utils'

const MAX_FILE_SIZE = 10 * 1024 * 1024 // 10MB

const ComplaintUpload: React.FC = () => {
  const navigate = useNavigate()
  const fileInputRef = useRef<HTMLInputElement>(null)
  const [file, setFile] = useState<File | null>(null)
  const [fraudType, setFraudType] = useState<string>('')
  const [layerDepth, setLayerDepth] = useState<number>(7)
  const [skipThresholdAmount, setSkipThresholdAmount] = useState<number>(0) // New state for threshold
  const [error, setError] = useState('')
  const [isUploading, setIsUploading] = useState(false)
  const [uploadProgress, setUploadProgress] = useState(0)

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>): void => {
    setError('')
    if (!event.target.files || event.target.files.length === 0) return
    const selectedFile = event.target.files[0]
    if (selectedFile.size > MAX_FILE_SIZE) {
      setError('File size exceeds 10MB limit')
      return
    }
    if (!selectedFile.name.endsWith('.html') && !selectedFile.name.endsWith('.htm')) {
      setError('Only HTML files are allowed')
      return
    }
    setFile(selectedFile)
  }

  const handleBrowseClick = (): void => {
    if (fileInputRef.current) fileInputRef.current.click()
  }

  const handleRemoveFile = (): void => {
    setFile(null)
    if (fileInputRef.current) fileInputRef.current.value = ''
  }

  const handleUpload = async (): Promise<void> => {
    if (!file) {
      setError('Please select a file')
      return
    }
    if (!fraudType) {
      setError('Please select a fraud type')
      return
    }
    setIsUploading(true)
    setUploadProgress(0)
    setError('')

    try {
      const fileContent = await file.text()
      const result = await window.api.uploadHtml({
        fileContent,
        fileName: file.name,
        fraudType,
        layerDepth,
        skipThresholdAmount
      })

      if (result.success && result.complaintId) {
        setUploadProgress(100)
        navigate(`/complaint/${result.complaintId}`)
      } else {
        setError(result.error || 'An unknown error occurred')
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An unexpected error occurred.')
    } finally {
      setIsUploading(false)
    }
  }

  const { isDark } = useThemeContext()

  return (
    <FullWidthLayout
      enableLampBackground={false}
      enableGlassmorphism={true}
      maxWidth="none"
      padding="none"
      className="min-h-screen"
    >
      <UnifiedNavbar title="Upload Complaint" showBackButton />

      <main className="flex-1 p-6 w-full">
        <FullWidthSection
          title="Upload Complaint"
          subtitle="Upload your bank statement HTML file for automatic transaction extraction"
          className="mb-8"
        >
          <div className="p-6 max-w-7xl mx-auto">
            <div className="grid grid-cols-1 lg:grid-cols-12 gap-6 mb-8">
              <div className="lg:col-span-8 space-y-8">
                {/* Row 1: Fraud Type and Layer Depth */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div
                    className={cn(
                      'p-6 rounded-lg backdrop-blur-md border shadow-lg',
                      isDark ? 'bg-white/10 border-white/10' : 'bg-white/80 border-gray-200/50'
                    )}
                  >
                    <h3
                      className={cn(
                        'text-sm font-medium mb-4',
                        isDark ? 'text-white' : 'text-gray-900'
                      )}
                    >
                      Fraud Type
                    </h3>
                    <FraudTypeSelector
                      selectedType={fraudType}
                      onTypeSelect={(type) => {
                        setFraudType(type)
                      }}
                    />
                  </div>

                  <div
                    className={cn(
                      'p-6 rounded-lg backdrop-blur-md border shadow-lg',
                      isDark ? 'bg-white/10 border-white/10' : 'bg-white/80 border-gray-200/50'
                    )}
                  >
                    <h3
                      className={cn(
                        'text-sm font-medium mb-4',
                        isDark ? 'text-white' : 'text-gray-900'
                      )}
                    >
                      Money Flow Depth
                    </h3>
                    <div className="flex items-center mb-4">
                      <input
                        type="range"
                        min={2}
                        max={7}
                        step={1}
                        value={layerDepth}
                        onChange={(e) => setLayerDepth(parseInt(e.target.value))}
                        className="w-full h-2 rounded-lg appearance-none cursor-pointer bg-gray-600 accent-blue-500"
                      />
                    </div>
                    <div className="flex justify-between mb-4">
                      {[2, 3, 4, 5, 6, 7].map((value) => (
                        <button
                          key={value}
                          onClick={() => setLayerDepth(value)}
                          className={cn(
                            'flex flex-col items-center justify-center w-10 h-10 rounded-full text-sm border transition-all duration-200',
                            layerDepth === value
                              ? 'bg-blue-500 text-white border-blue-500 shadow-md'
                              : isDark
                                ? 'bg-white/10 text-gray-300 border-white/20 hover:bg-white/20'
                                : 'bg-gray-100 text-gray-700 border-gray-300 hover:bg-gray-200'
                          )}
                        >
                          {value}
                        </button>
                      ))}
                    </div>
                    <div
                      className={cn(
                        'flex justify-between items-center p-3 rounded-lg text-sm border',
                        isDark ? 'bg-white/5 border-white/10' : 'bg-gray-50 border-gray-200'
                      )}
                    >
                      <div className="flex items-center">
                        <div className="w-6 h-6 rounded-full flex items-center justify-center mr-2 text-xs bg-blue-500 text-white">
                          {layerDepth}
                        </div>
                        <p className={cn('text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
                          {layerDepth <= 3
                            ? 'Faster processing'
                            : layerDepth >= 6
                              ? 'Deeper tracing'
                              : 'Balanced depth'}
                        </p>
                      </div>
                      <div className="px-3 py-1 rounded-md bg-blue-500 text-white">
                        <p className="text-xs font-medium">
                          {layerDepth <= 3 ? 'Fast ⚡' : layerDepth <= 5 ? 'Medium' : 'Deep'}
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Row 2: Upload and Amount Threshold */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div
                    className={cn(
                      'p-6 rounded-lg backdrop-blur-md border shadow-lg',
                      isDark ? 'bg-white/10 border-white/10' : 'bg-white/80 border-gray-200/50'
                    )}
                  >
                    <h3
                      className={cn(
                        'text-sm font-medium mb-4',
                        isDark ? 'text-white' : 'text-gray-900'
                      )}
                    >
                      Skip Threshold Amount
                    </h3>
                    <div className="flex items-center mb-4">
                      <input
                        type="number"
                        min={0}
                        value={skipThresholdAmount}
                        onChange={(e) => setSkipThresholdAmount(parseInt(e.target.value))}
                        className={cn(
                          'w-full p-3 rounded-lg border focus:outline-none focus:ring-2 focus:ring-blue-500',
                          isDark
                            ? 'bg-white/10 border-white/20 text-white placeholder-gray-400'
                            : 'bg-white border-gray-300 text-gray-900 placeholder-gray-500'
                        )}
                        placeholder="Enter threshold amount"
                      />
                    </div>
                    <p className={cn('text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
                      Transactions below this amount will be skipped during analysis.
                    </p>
                  </div>

                  <div
                    className={cn(
                      'p-6 rounded-lg backdrop-blur-md border shadow-lg',
                      isDark ? 'bg-white/10 border-white/10' : 'bg-white/80 border-gray-200/50'
                    )}
                  >
                    <h3
                      className={cn(
                        'text-sm font-medium mb-4',
                        isDark ? 'text-white' : 'text-gray-900'
                      )}
                    >
                      HTML File Upload
                    </h3>
                    <div
                      className={cn(
                        'mt-2 flex flex-col items-center justify-center w-full h-64 border-2 border-dashed rounded-lg cursor-pointer transition-colors p-6',
                        isDark
                          ? 'border-white/20 hover:border-blue-400 bg-white/5 hover:bg-white/10'
                          : 'border-gray-300 hover:border-blue-400 bg-gray-50 hover:bg-gray-100'
                      )}
                      onClick={handleBrowseClick}
                    >
                      {!file ? (
                        <div className="text-center">
                          <Upload
                            className={cn(
                              'mx-auto h-16 w-16 mb-3',
                              isDark ? 'text-gray-400' : 'text-gray-500'
                            )}
                          />
                          <p className={cn('text-lg', isDark ? 'text-gray-300' : 'text-gray-700')}>
                            Click to select an HTML file (max 10MB)
                          </p>
                          <p
                            className={cn(
                              'text-sm mt-1',
                              isDark ? 'text-gray-500' : 'text-gray-500'
                            )}
                          >
                            or drag and drop here
                          </p>
                        </div>
                      ) : (
                        <div className="flex flex-col items-center justify-center w-full px-6 py-4">
                          <Upload className="h-16 w-16 text-green-500 mb-3" />
                          <span
                            className={cn(
                              'text-xl font-semibold text-center break-all',
                              isDark ? 'text-white' : 'text-gray-900'
                            )}
                          >
                            {file.name}
                          </span>
                          <span
                            className={cn(
                              'text-md mt-1',
                              isDark ? 'text-gray-400' : 'text-gray-600'
                            )}
                          >
                            ({(file.size / 1024 / 1024).toFixed(2)} MB)
                          </span>
                          <button
                            onClick={(e) => {
                              e.stopPropagation()
                              handleRemoveFile()
                            }}
                            className="mt-4 text-red-500 hover:text-red-400 font-semibold text-lg transition-colors"
                          >
                            Remove File
                          </button>
                        </div>
                      )}
                      <input
                        type="file"
                        accept=".html,.htm"
                        ref={fileInputRef}
                        onChange={handleFileChange}
                        className="hidden"
                      />
                    </div>
                  </div>

                  {error && (
                    <div
                      className={cn(
                        'p-4 rounded-lg border text-center font-semibold',
                        'bg-red-50 border-red-200 text-red-700',
                        isDark && 'bg-red-900/20 border-red-800/50 text-red-400'
                      )}
                    >
                      {error}
                    </div>
                  )}

                  <div className="flex justify-end gap-4 pt-6">
                    <button
                      onClick={() => navigate('/dashboard')}
                      className={cn(
                        'px-6 py-3 rounded-lg transition-colors font-semibold',
                        isDark
                          ? 'bg-white/10 hover:bg-white/20 text-white border border-white/20'
                          : 'bg-gray-200 hover:bg-gray-300 text-gray-900 border border-gray-300'
                      )}
                    >
                      Cancel
                    </button>
                    <button
                      onClick={handleUpload}
                      disabled={isUploading || !file || !fraudType}
                      className={cn(
                        'px-6 py-3 rounded-lg transition-colors font-semibold disabled:opacity-50 disabled:cursor-not-allowed',
                        'bg-blue-600 hover:bg-blue-700 text-white'
                      )}
                    >
                      {isUploading ? `Processing... ${uploadProgress}%` : 'Process Complaint'}
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Instructions moved to right side */}
            <div className="lg:col-span-4">
              <div
                className={cn(
                  'p-6 rounded-lg backdrop-blur-md border shadow-lg sticky top-6',
                  isDark ? 'bg-white/10 border-white/10' : 'bg-white/80 border-gray-200/50'
                )}
              >
                <div className="flex items-center mb-6">
                  <div
                    className={cn(
                      'p-2 rounded-full mr-3',
                      isDark ? 'bg-blue-500/20 text-blue-400' : 'bg-blue-100 text-blue-600'
                    )}
                  >
                    <Info className="w-5 h-5" />
                  </div>
                  <h2 className={cn('text-lg font-bold', isDark ? 'text-white' : 'text-gray-900')}>
                    How to Upload
                  </h2>
                </div>
                <ol className={cn('space-y-4', isDark ? 'text-gray-300' : 'text-gray-700')}>
                  <li className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500 text-white flex items-center justify-center text-sm font-bold mr-3">
                      1
                    </div>
                    <div>
                      <h4 className={cn('font-semibold', isDark ? 'text-white' : 'text-gray-900')}>
                        Select Fraud Type
                      </h4>
                      <p className={cn('text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
                        Choose the type of fraud (e.g., &quot;Online Banking&quot;,
                        &quot;Credit/Debit Card&quot;).
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500 text-white flex items-center justify-center text-sm font-bold mr-3">
                      2
                    </div>
                    <div>
                      <h4 className={cn('font-semibold', isDark ? 'text-white' : 'text-gray-900')}>
                        Set Money Flow Depth
                      </h4>
                      <p className={cn('text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
                        Adjust the layer depth for transaction tracing (2-7). 2-3 is fast, 4-5 is
                        medium, and 6-7 is deep.
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500 text-white flex items-center justify-center text-sm font-bold mr-3">
                      3
                    </div>
                    <div>
                      <h4 className={cn('font-semibold', isDark ? 'text-white' : 'text-gray-900')}>
                        Set Skip Threshold Amount
                      </h4>
                      <p className={cn('text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
                        Specify a threshold to skip small transactions. Transactions below this
                        amount will be skipped during analysis.
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500 text-white flex items-center justify-center text-sm font-bold mr-3">
                      4
                    </div>
                    <div>
                      <h4 className={cn('font-semibold', isDark ? 'text-white' : 'text-gray-900')}>
                        Upload HTML File
                      </h4>
                      <p className={cn('text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
                        Drag and drop or browse to select your bank statement HTML file
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500 text-white flex items-center justify-center text-sm font-bold mr-3">
                      5
                    </div>
                    <div>
                      <h4 className={cn('font-semibold', isDark ? 'text-white' : 'text-gray-900')}>
                        Process Complaint
                      </h4>
                      <p className={cn('text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
                        Click the &quot;Process Complaint&quot; button to initiate extraction and
                        analysis.
                      </p>
                    </div>
                  </li>
                  <li className="flex items-start">
                    <div className="flex-shrink-0 w-6 h-6 rounded-full bg-blue-500 text-white flex items-center justify-center text-sm font-bold mr-3">
                      6
                    </div>
                    <div>
                      <h4 className={cn('font-semibold', isDark ? 'text-white' : 'text-gray-900')}>
                        Review Results
                      </h4>
                      <p className={cn('text-sm', isDark ? 'text-gray-400' : 'text-gray-600')}>
                        You will be redirected to the complaint details page to review the extracted
                        data and graph visualization.
                      </p>
                    </div>
                  </li>
                </ol>
              </div>
            </div>
          </div>
        </FullWidthSection>
      </main>
    </FullWidthLayout>
  )
}

export default ComplaintUpload

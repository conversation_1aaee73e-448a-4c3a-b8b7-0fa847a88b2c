import React, { useState } from 'react'
import { Link, useNavigate } from 'react-router-dom'
import { backendService } from '../services/backendService'
import { CardContainer, CardBody, CardItem } from '../components/ui/3d-card'
import { BackgroundBeams } from '../components/ui/background-beams'
import { FullWidthLayout } from '../components/layout/FullWidthLayout'

interface SignupProps {
  onSignupSuccess?: () => void
}

const Signup: React.FC<SignupProps> = ({ onSignupSuccess }) => {
  const [formData, setFormData] = useState({
    initial: 'Mr.',
    name: '',
    email: '',
    password: '',
    confirmPassword: '',
    designation: '',
    policeStation: '',
    district: '',
    state: ''
  })
  const [error, setError] = useState('')
  const [loading, setLoading] = useState(false)
  const navigate = useNavigate()

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>): void => {
    const { name, value } = e.target
    setFormData((prev) => ({ ...prev, [name]: value }))
    if (error) setError('')
  }

  const handleSignup = async (e: React.FormEvent): Promise<void> => {
    e.preventDefault()
    // Validation
    if (
      !formData.initial ||
      !formData.name ||
      !formData.email ||
      !formData.password ||
      !formData.confirmPassword ||
      !formData.policeStation ||
      !formData.district ||
      !formData.state
    ) {
      setError('Please fill in all required fields (*)')
      return
    }
    if (formData.password !== formData.confirmPassword) {
      setError('Passwords do not match')
      return
    }
    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long')
      return
    }
    setLoading(true)
    setError('')
    try {
      console.log('[Signup] Sending signup request with payload:', {
        email: formData.email,
        password: formData.password,
        initial: formData.initial,
        name: formData.name,
        designation: formData.designation,
        police_station: formData.policeStation,
        district: formData.district,
        state: formData.state
      })
      const result = await backendService.signup({
        email: formData.email,
        password: formData.password,
        initial: formData.initial,
        name: formData.name,
        designation: formData.designation,
        police_station: formData.policeStation,
        district: formData.district,
        state: formData.state
      })
      console.log('[Signup] Signup API result:', result)
      setLoading(false)
      if (
        result.success ||
        result.requires_verification ||
        (result.message && result.message.toLowerCase().includes('user registered successfully'))
      ) {
        console.log(
          '[Signup] Signup successful (by requires_verification/message), navigating to /verify-otp with email:',
          formData.email
        )
        if (onSignupSuccess) onSignupSuccess()
        navigate('/verify-otp', { state: { email: formData.email } })
      } else {
        console.error('[Signup] Signup failed with error:', result.error)
        setError(result.error || result.message || 'Signup failed')
      }
    } catch (err: unknown) {
      console.error('[Signup] Exception during signup:', err)
      setError(err instanceof Error ? err.message : 'Signup failed')
      setLoading(false)
    }
  }

  return (
    <FullWidthLayout
      enableLampBackground={false}
      enableGlassmorphism={false}
      maxWidth="none"
      padding="none"
      className="min-h-screen relative"
    >
      {/* Full-width background */}
      <div className="absolute inset-0">
        <BackgroundBeams />
      </div>

      {/* Centered content container */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-4">
        <div className="w-full max-w-lg">
          <CardContainer>
            <CardBody enableGlassmorphism={true} enableGlow={true}>
              <form onSubmit={handleSignup} className="p-8 w-full flex flex-col gap-6">
                <CardItem
                  as="h2"
                  translateZ="60"
                  className="text-3xl font-bold text-center text-neutral-800 dark:text-neutral-200"
                >
                  Sign Up
                </CardItem>
                <div className="grid grid-cols-6 gap-4">
                  <CardItem translateZ="50" className="col-span-2">
                    <select
                      name="initial"
                      value={formData.initial}
                      onChange={handleInputChange}
                      className="w-full p-3 bg-transparent border-b-2 border-neutral-400 dark:border-neutral-600 focus:outline-none focus:border-primary-500 dark:focus:border-primary-400 text-neutral-800 dark:text-neutral-200"
                      required
                    >
                      <option value="Mr.">Mr.</option>
                      <option value="Miss">Miss</option>
                      <option value="Mrs.">Mrs.</option>
                      <option value="Dr.">Dr.</option>
                    </select>
                  </CardItem>
                  <CardItem translateZ="50" className="col-span-4">
                    <input
                      type="text"
                      name="name"
                      placeholder="Name *"
                      value={formData.name}
                      onChange={handleInputChange}
                      className="w-full p-3 bg-transparent border-b-2 border-neutral-400 dark:border-neutral-600 focus:outline-none focus:border-primary-500 dark:focus:border-primary-400 text-neutral-800 dark:text-neutral-200"
                      required
                    />
                  </CardItem>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <CardItem translateZ="50" className="col-span-1">
                    <input
                      type="email"
                      name="email"
                      placeholder="Email *"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="w-full p-3 bg-transparent border-b-2 border-neutral-400 dark:border-neutral-600 focus:outline-none focus:border-primary-500 dark:focus:border-primary-400 text-neutral-800 dark:text-neutral-200"
                      required
                    />
                  </CardItem>
                  <CardItem translateZ="50" className="col-span-1">
                    <input
                      type="text"
                      name="designation"
                      placeholder="Designation *"
                      value={formData.designation}
                      onChange={handleInputChange}
                      className="w-full p-3 bg-transparent border-b-2 border-neutral-400 dark:border-neutral-600 focus:outline-none focus:border-primary-500 dark:focus:border-primary-400 text-neutral-800 dark:text-neutral-200"
                      required
                    />
                  </CardItem>
                </div>
                <div className="grid grid-cols-3 gap-4">
                  <CardItem translateZ="50" className="col-span-1">
                    <input
                      type="text"
                      name="policeStation"
                      placeholder="Police Station *"
                      value={formData.policeStation}
                      onChange={handleInputChange}
                      className="w-full p-3 bg-transparent border-b-2 border-neutral-400 dark:border-neutral-600 focus:outline-none focus:border-primary-500 dark:focus:border-primary-400 text-neutral-800 dark:text-neutral-200"
                      required
                    />
                  </CardItem>
                  <CardItem translateZ="50" className="col-span-1">
                    <input
                      type="text"
                      name="district"
                      placeholder="District *"
                      value={formData.district}
                      onChange={handleInputChange}
                      className="w-full p-3 bg-transparent border-b-2 border-neutral-400 dark:border-neutral-600 focus:outline-none focus:border-primary-500 dark:focus:border-primary-400 text-neutral-800 dark:text-neutral-200"
                      required
                    />
                  </CardItem>
                  <CardItem translateZ="50" className="col-span-1">
                    <input
                      type="text"
                      name="state"
                      placeholder="State *"
                      value={formData.state}
                      onChange={handleInputChange}
                      className="w-full p-3 bg-transparent border-b-2 border-neutral-400 dark:border-neutral-600 focus:outline-none focus:border-primary-500 dark:focus:border-primary-400 text-neutral-800 dark:text-neutral-200"
                      required
                    />
                  </CardItem>
                </div>
                <div className="grid grid-cols-2 gap-4">
                  <CardItem translateZ="50" className="col-span-1">
                    <input
                      type="password"
                      name="password"
                      placeholder="Password *"
                      value={formData.password}
                      onChange={handleInputChange}
                      className="w-full p-3 bg-transparent border-b-2 border-neutral-400 dark:border-neutral-600 focus:outline-none focus:border-primary-500 dark:focus:border-primary-400 text-neutral-800 dark:text-neutral-200"
                      required
                    />
                  </CardItem>
                  <CardItem translateZ="50" className="col-span-1">
                    <input
                      type="password"
                      name="confirmPassword"
                      placeholder="Confirm Password *"
                      value={formData.confirmPassword}
                      onChange={handleInputChange}
                      className="w-full p-3 bg-transparent border-b-2 border-neutral-400 dark:border-neutral-600 focus:outline-none focus:border-primary-500 dark:focus:border-primary-400 text-neutral-800 dark:text-neutral-200"
                      required
                    />
                  </CardItem>
                </div>
                {error && (
                  <CardItem translateZ="40" className="text-red-500 text-center font-medium">
                    {error}
                  </CardItem>
                )}
                <CardItem translateZ="60">
                  <button
                    type="submit"
                    className="w-full py-3 text-lg font-semibold bg-primary-500 text-neutral-800 dark:text-neutral-200 rounded-lg hover:bg-primary-600 transition-colors"
                    disabled={loading}
                  >
                    {loading ? 'Signing up...' : 'Sign Up'}
                  </button>
                </CardItem>
                <CardItem
                  translateZ="40"
                  className="text-center text-neutral-600 dark:text-neutral-400"
                >
                  Already have an account?{' '}
                  <Link to="/login" className="text-primary-500 hover:underline">
                    Login
                  </Link>
                </CardItem>
              </form>
            </CardBody>
          </CardContainer>
        </div>
      </div>
    </FullWidthLayout>
  )
}

export default Signup

import React, { useState } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { useComplaintData } from '../hooks/useComplaintData'
import { TransactionData } from '../../../shared/api'
import UnifiedNavbar from '../components/UnifiedNavbar'
import { FullWidthLayout, FullWidthSection, ResponsiveGrid } from '../components/layout/FullWidthLayout'
import { Button } from '../components/ui/Button'
import { Card, CardContent, CardHeader, CardTitle } from '../components/ui/Card'
import { useThemeContext } from '../context/useThemeContext'
import { cn } from '../lib/aceternity-utils'
// Individual complaint analytics with date-based filtering

const StatsPage: React.FC = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  const { isDark } = useThemeContext()
  const { data: complaint, loading, error } = useComplaintData(id || '')

  // Date filtering state
  const [startDate, setStartDate] = useState<string>('')
  const [endDate, setEndDate] = useState<string>('')

  // Extract all transactions from layer_transactions
  const allTransactions: TransactionData[] = React.useMemo(() => {
    if (!complaint?.layer_transactions) return []

    return Object.values(complaint.layer_transactions)
      .flat()
      .filter(
        (item): item is TransactionData =>
          typeof item === 'object' && item !== null && 'receiver_ifsc' in item
      )
  }, [complaint])

  // Filter transactions by date range
  const filteredTransactions = React.useMemo(() => {
    if (!startDate && !endDate) return allTransactions

    return allTransactions.filter((transaction) => {
      const transactionDate = new Date(transaction.date)
      const start = startDate ? new Date(startDate) : null
      const end = endDate ? new Date(endDate) : null

      if (start && transactionDate < start) return false
      if (end && transactionDate > end) return false
      return true
    })
  }, [allTransactions, startDate, endDate])

  // Calculate comprehensive analytics for the individual complaint
  const analytics = React.useMemo(() => {
    const transactions = filteredTransactions
    const totalTransactions = transactions.length

    // Calculate total amounts
    const totalAmount = transactions.reduce((sum, transaction) => {
      const amount = parseFloat(transaction.amount || '0')
      return sum + (isNaN(amount) ? 0 : amount)
    }, 0)

    // Transaction type analysis
    const typeStats = transactions.reduce((acc, transaction) => {
      const type = transaction.txn_type || transaction.type || 'Unknown'
      if (!acc[type]) {
        acc[type] = { count: 0, amount: 0 }
      }
      acc[type].count++
      const amount = parseFloat(transaction.amount || '0')
      if (!isNaN(amount)) {
        acc[type].amount += amount
      }
      return acc
    }, {} as Record<string, { count: number; amount: number }>)

    // Layer analysis
    const layerStats = transactions.reduce((acc, transaction) => {
      const layer = transaction.layer || 0
      if (!acc[layer]) {
        acc[layer] = { count: 0, amount: 0 }
      }
      acc[layer].count++
      const amount = parseFloat(transaction.amount || '0')
      if (!isNaN(amount)) {
        acc[layer].amount += amount
      }
      return acc
    }, {} as Record<number, { count: number; amount: number }>)

    // Bank analysis (using enhanced bank data)
    const bankStats = transactions.reduce((acc, transaction) => {
      const bank = transaction.receiver_bank || 'Unknown'
      if (!acc[bank]) {
        acc[bank] = { count: 0, amount: 0 }
      }
      acc[bank].count++
      const amount = parseFloat(transaction.amount || '0')
      if (!isNaN(amount)) {
        acc[bank].amount += amount
      }
      return acc
    }, {} as Record<string, { count: number; amount: number }>)

    // Date range analysis
    const dates = transactions.map(t => new Date(t.date)).filter(d => !isNaN(d.getTime()))
    const dateRange = dates.length > 0 ? {
      earliest: new Date(Math.min(...dates.map(d => d.getTime()))),
      latest: new Date(Math.max(...dates.map(d => d.getTime()))),
      span: dates.length > 1 ? Math.ceil((Math.max(...dates.map(d => d.getTime())) - Math.min(...dates.map(d => d.getTime()))) / (1000 * 60 * 60 * 24)) : 0
    } : null

    return {
      totalTransactions,
      totalAmount,
      typeStats,
      layerStats,
      bankStats,
      dateRange,
      averageAmount: totalTransactions > 0 ? totalAmount / totalTransactions : 0
    }
  }, [filteredTransactions])

  if (loading) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="text-lg">Loading complaint data...</div>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="text-red-600 text-lg mb-2">Error</div>
          <div className="text-gray-600">{error}</div>
        </div>
      </div>
    )
  }

  if (!complaint) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <div className="text-gray-600">Complaint not found</div>
        </div>
      </div>
    )
  }

  return (
    <FullWidthLayout
      enableLampBackground={false}
      enableGlassmorphism={true}
      maxWidth="none"
      padding="none"
      className="min-h-screen"
    >
      <UnifiedNavbar
        title={complaint ? `Statistics - ${complaint.title}` : 'Statistics'}
        showBackButton
        customActions={
          <Button onClick={() => navigate('/all-stats')} variant="outline" size="sm">
            All Stats
          </Button>
        }
      />

      <main className="flex-1 p-6 w-full">
        <div className="w-full space-y-6">
          {/* Date Filter Section */}
          <FullWidthSection
            title={`Statistics for ${complaint.title}`}
            subtitle="Detailed analysis of transactions and patterns with date filtering"
            className="mb-6"
          >
            <Card
              className={cn(
                'backdrop-blur-md border-0 shadow-lg',
                isDark ? 'bg-white/10' : 'bg-white/80'
              )}
            >
              <CardHeader>
                <CardTitle>Date Range Filter</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex gap-4 items-center">
                  <div className="flex-1">
                    <label className="block text-sm font-medium mb-1">Start Date</label>
                    <input
                      type="date"
                      value={startDate}
                      onChange={(e) => setStartDate(e.target.value)}
                      className={cn(
                        'w-full px-3 py-2 border rounded-lg',
                        isDark
                          ? 'bg-gray-800 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      )}
                    />
                  </div>
                  <div className="flex-1">
                    <label className="block text-sm font-medium mb-1">End Date</label>
                    <input
                      type="date"
                      value={endDate}
                      onChange={(e) => setEndDate(e.target.value)}
                      className={cn(
                        'w-full px-3 py-2 border rounded-lg',
                        isDark
                          ? 'bg-gray-800 border-gray-600 text-white'
                          : 'bg-white border-gray-300 text-gray-900'
                      )}
                    />
                  </div>
                  <div className="flex gap-2">
                    <Button
                      onClick={() => {
                        setStartDate('')
                        setEndDate('')
                      }}
                      variant="outline"
                      size="sm"
                    >
                      Clear
                    </Button>
                  </div>
                </div>
                {analytics.dateRange && (
                  <div className="mt-4 text-sm text-muted-foreground">
                    Transaction period: {analytics.dateRange.earliest.toLocaleDateString()} to{' '}
                    {analytics.dateRange.latest.toLocaleDateString()} ({analytics.dateRange.span} days)
                  </div>
                )}
              </CardContent>
            </Card>
          </FullWidthSection>

          {/* Overview Cards */}
          <FullWidthSection className="space-y-6">
            <ResponsiveGrid columns={{ sm: 1, md: 2, lg: 4 }} className="gap-6">
              <Card
                className={cn(
                  'backdrop-blur-md border-0 shadow-lg',
                  isDark ? 'bg-white/10' : 'bg-white/80'
                )}
              >
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Transactions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics.totalTransactions}</div>
                  <div className="text-sm text-muted-foreground">
                    {filteredTransactions.length !== allTransactions.length &&
                      `Filtered from ${allTransactions.length}`}
                  </div>
                </CardContent>
              </Card>

              <Card
                className={cn(
                  'backdrop-blur-md border-0 shadow-lg',
                  isDark ? 'bg-white/10' : 'bg-white/80'
                )}
              >
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Total Amount</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">₹{analytics.totalAmount.toLocaleString()}</div>
                </CardContent>
              </Card>

              <Card
                className={cn(
                  'backdrop-blur-md border-0 shadow-lg',
                  isDark ? 'bg-white/10' : 'bg-white/80'
                )}
              >
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Average Amount</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">₹{analytics.averageAmount.toLocaleString()}</div>
                </CardContent>
              </Card>

              <Card
                className={cn(
                  'backdrop-blur-md border-0 shadow-lg',
                  isDark ? 'bg-white/10' : 'bg-white/80'
                )}
              >
                <CardHeader className="pb-2">
                  <CardTitle className="text-sm font-medium">Transaction Types</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{Object.keys(analytics.typeStats).length}</div>
                </CardContent>
              </Card>
            </ResponsiveGrid>

            {/* Analytics Charts */}
            <ResponsiveGrid columns={{ sm: 1, lg: 2 }} className="gap-6">
              {/* Transaction Type Analysis */}
              <Card
                className={cn(
                  'backdrop-blur-md border-0 shadow-lg',
                  isDark ? 'bg-white/10' : 'bg-white/80'
                )}
              >
                <CardHeader>
                  <CardTitle>Transaction Types</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Object.entries(analytics.typeStats)
                      .sort(([,a], [,b]) => b.amount - a.amount)
                      .map(([type, stats]) => {
                        const percentage = analytics.totalAmount > 0 ? (stats.amount / analytics.totalAmount) * 100 : 0
                        return (
                          <div key={type} className="space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">{type}</span>
                              <span className="text-sm text-muted-foreground">
                                ₹{stats.amount.toLocaleString()} ({stats.count} txns)
                              </span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${percentage}%` }}
                              />
                            </div>
                          </div>
                        )
                      })}
                  </div>
                </CardContent>
              </Card>

              {/* Layer Analysis */}
              <Card
                className={cn(
                  'backdrop-blur-md border-0 shadow-lg',
                  isDark ? 'bg-white/10' : 'bg-white/80'
                )}
              >
                <CardHeader>
                  <CardTitle>Layer Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Object.entries(analytics.layerStats)
                      .sort(([a], [b]) => parseInt(a) - parseInt(b))
                      .map(([layer, stats]) => {
                        const percentage = analytics.totalTransactions > 0 ? (stats.count / analytics.totalTransactions) * 100 : 0
                        return (
                          <div key={layer} className="space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium">Layer {layer}</span>
                              <span className="text-sm text-muted-foreground">
                                {stats.count} transactions (₹{stats.amount.toLocaleString()})
                              </span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-green-500 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${percentage}%` }}
                              />
                            </div>
                          </div>
                        )
                      })}
                  </div>
                </CardContent>
              </Card>

              {/* Top Banks */}
              <Card
                className={cn(
                  'backdrop-blur-md border-0 shadow-lg',
                  isDark ? 'bg-white/10' : 'bg-white/80'
                )}
              >
                <CardHeader>
                  <CardTitle>Top Banks by Amount</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {Object.entries(analytics.bankStats)
                      .sort(([,a], [,b]) => b.amount - a.amount)
                      .slice(0, 5)
                      .map(([bank, stats]) => {
                        const percentage = analytics.totalAmount > 0 ? (stats.amount / analytics.totalAmount) * 100 : 0
                        return (
                          <div key={bank} className="space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-sm font-medium truncate">{bank}</span>
                              <span className="text-sm text-muted-foreground">
                                ₹{stats.amount.toLocaleString()}
                              </span>
                            </div>
                            <div className="w-full bg-gray-200 rounded-full h-2">
                              <div
                                className="bg-purple-500 h-2 rounded-full transition-all duration-300"
                                style={{ width: `${percentage}%` }}
                              />
                            </div>
                          </div>
                        )
                      })}
                  </div>
                </CardContent>
              </Card>

              {/* Summary Statistics */}
              <Card
                className={cn(
                  'backdrop-blur-md border-0 shadow-lg',
                  isDark ? 'bg-white/10' : 'bg-white/80'
                )}
              >
                <CardHeader>
                  <CardTitle>Summary Statistics</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="flex justify-between">
                      <span className="text-sm">Unique Banks:</span>
                      <span className="font-medium">{Object.keys(analytics.bankStats).length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Layers Used:</span>
                      <span className="font-medium">{Object.keys(analytics.layerStats).length}</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">Transaction Types:</span>
                      <span className="font-medium">{Object.keys(analytics.typeStats).length}</span>
                    </div>
                    {analytics.dateRange && (
                      <>
                        <div className="flex justify-between">
                          <span className="text-sm">Time Span:</span>
                          <span className="font-medium">{analytics.dateRange.span} days</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-sm">Avg per Day:</span>
                          <span className="font-medium">
                            {analytics.dateRange.span > 0
                              ? (analytics.totalTransactions / analytics.dateRange.span).toFixed(1)
                              : analytics.totalTransactions} txns
                          </span>
                        </div>
                      </>
                    )}
                  </div>
                </CardContent>
              </Card>
            </ResponsiveGrid>
          </FullWidthSection>
        </div>
      </main>
    </FullWidthLayout>
  )
}

export default StatsPage

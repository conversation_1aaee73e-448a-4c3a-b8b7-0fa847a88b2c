// Layout utility functions and hooks
import React from 'react'

// Utility hook for responsive breakpoints
export const useResponsiveBreakpoint = (): 'sm' | 'md' | 'lg' | 'xl' | '2xl' => {
  const [breakpoint, setBreakpoint] = React.useState<'sm' | 'md' | 'lg' | 'xl' | '2xl'>('lg')

  React.useEffect(() => {
    const updateBreakpoint = () => {
      const width = window.innerWidth
      if (width < 640) setBreakpoint('sm')
      else if (width < 768) setBreakpoint('md')
      else if (width < 1024) setBreakpoint('lg')
      else if (width < 1280) setBreakpoint('xl')
      else setBreakpoint('2xl')
    }

    updateBreakpoint()
    window.addEventListener('resize', updateBreakpoint)
    return () => window.removeEventListener('resize', updateBreakpoint)
  }, [])

  return breakpoint
}

// Layout utility functions
export const getResponsiveColumns = (breakpoint: string): number => {
  switch (breakpoint) {
    case 'sm':
      return 1
    case 'md':
      return 2
    case 'lg':
      return 3
    case 'xl':
      return 4
    case '2xl':
      return 5
    default:
      return 3
  }
}

export const getResponsiveSpacing = (breakpoint: string): string => {
  switch (breakpoint) {
    case 'sm':
      return 'p-2'
    case 'md':
      return 'p-4'
    case 'lg':
      return 'p-6'
    case 'xl':
      return 'p-8'
    case '2xl':
      return 'p-10'
    default:
      return 'p-6'
  }
}

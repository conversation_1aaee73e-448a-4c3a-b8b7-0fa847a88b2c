/**
 * Date formatting utilities for the fraud analysis app
 */

/**
 * Format a date value to a localized date string
 * Handles various input formats and returns '-' for invalid dates
 */
export const formatDate = (value: string | number | Date, locale: string = 'en-US'): string => {
  if (!value) return '-'

  // Handle different date formats
  let date: Date

  if (value instanceof Date) {
    date = value
  } else if (typeof value === 'string') {
    // Clean the string and try different parsing approaches
    const cleanValue = value.trim()

    // Handle empty strings
    if (cleanValue === '' || cleanValue === '-') return '-'

    // Try parsing common date formats
    if (/^\d{2}\/\d{2}\/\d{4}\s/.test(cleanValue)) {
      const [datePart, timePart, ampmPart] = cleanValue.split(' ')
      const [day, month, year] = datePart.split('/')
      if (timePart && ampmPart) {
        const [hours, minutes, seconds] = timePart.split(':')
        let hoursNum = parseInt(hours, 10)
        if (ampmPart.toUpperCase() === 'PM' && hoursNum < 12) {
          hoursNum += 12
        }
        if (ampmPart.toUpperCase() === 'AM' && hoursNum === 12) {
          hoursNum = 0
        }
        date = new Date(
          `${year}-${month}-${day}T${String(hoursNum).padStart(2, '0')}:${minutes}:${seconds}`
        )
      } else {
        date = new Date(`${year}-${month}-${day}`)
      }
    } else if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(cleanValue)) {
      // ISO format with time
      date = new Date(cleanValue)
    } else if (/^\d{4}-\d{2}-\d{2}$/.test(cleanValue)) {
      // ISO date format (YYYY-MM-DD)
      date = new Date(cleanValue)
    } else if (/^\d{2}\/\d{2}\/\d{4}/.test(cleanValue)) {
      // DD/MM/YYYY or MM/DD/YYYY format
      const parts = cleanValue.split('/')
      date = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`)
    } else if (/^\d{2}-\d{2}-\d{4}/.test(cleanValue)) {
      // DD-MM-YYYY format
      const parts = cleanValue.split('-')
      date = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`)
    } else if (/^\d{1,2}\/\d{1,2}\/\d{4}/.test(cleanValue)) {
      // M/D/YYYY or MM/DD/YYYY format
      date = new Date(cleanValue)
    } else {
      // Try direct parsing
      date = new Date(cleanValue)
    }
  } else if (typeof value === 'number') {
    // Assume it's a timestamp
    date = new Date(value)
  } else {
    return '-'
  }

  // Check if the date is valid
  if (isNaN(date.getTime())) {
    console.warn(`[DateUtils] Invalid date value: ${value}`)
    return '-'
  }

  return date.toLocaleDateString(locale)
}

/**
 * Format a date value to a localized date and time string
 */
export const formatDateTime = (value: string | number | Date, locale: string = 'en-US'): string => {
  if (!value) return '-'

  // Handle different date formats
  let date: Date

  if (value instanceof Date) {
    date = value
  } else if (typeof value === 'string') {
    // Clean the string and try different parsing approaches
    const cleanValue = value.trim()

    // Handle empty strings
    if (cleanValue === '' || cleanValue === '-') return '-'

    // Try parsing common date formats (same logic as formatDate)
    if (/^\d{2}\/\d{2}\/\d{4}\s/.test(cleanValue)) {
      const [datePart, timePart, ampmPart] = cleanValue.split(' ')
      const [day, month, year] = datePart.split('/')
      if (timePart && ampmPart) {
        const [hours, minutes, seconds] = timePart.split(':')
        let hoursNum = parseInt(hours, 10)
        if (ampmPart.toUpperCase() === 'PM' && hoursNum < 12) {
          hoursNum += 12
        }
        if (ampmPart.toUpperCase() === 'AM' && hoursNum === 12) {
          hoursNum = 0
        }
        date = new Date(
          `${year}-${month}-${day}T${String(hoursNum).padStart(2, '0')}:${minutes}:${seconds}`
        )
      } else {
        date = new Date(`${year}-${month}-${day}`)
      }
    } else if (/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}/.test(cleanValue)) {
      // ISO format with time
      date = new Date(cleanValue)
    } else if (/^\d{4}-\d{2}-\d{2}$/.test(cleanValue)) {
      // ISO date format (YYYY-MM-DD)
      date = new Date(cleanValue)
    } else if (/^\d{2}\/\d{2}\/\d{4}/.test(cleanValue)) {
      // DD/MM/YYYY or MM/DD/YYYY format
      const parts = cleanValue.split('/')
      date = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`)
    } else if (/^\d{2}-\d{2}-\d{4}/.test(cleanValue)) {
      // DD-MM-YYYY format
      const parts = cleanValue.split('-')
      date = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`)
    } else if (/^\d{1,2}\/\d{1,2}\/\d{4}/.test(cleanValue)) {
      // M/D/YYYY or MM/DD/YYYY format
      date = new Date(cleanValue)
    } else {
      // Try direct parsing
      date = new Date(cleanValue)
    }
  } else if (typeof value === 'number') {
    // Assume it's a timestamp
    date = new Date(value)
  } else {
    return '-'
  }

  // Check if the date is valid
  if (isNaN(date.getTime())) {
    console.warn(`[DateUtils] Invalid datetime value: ${value}`)
    return '-'
  }

  return date.toLocaleString(locale)
}

/**
 * Format a date value to ISO string format
 */
export const formatDateISO = (value: string | number | Date): string => {
  if (!value) return ''

  // Handle different date formats
  let date: Date

  if (value instanceof Date) {
    date = value
  } else if (typeof value === 'string') {
    // Try parsing the string
    date = new Date(value)
  } else if (typeof value === 'number') {
    // Assume it's a timestamp
    date = new Date(value)
  } else {
    return ''
  }

  // Check if the date is valid
  if (isNaN(date.getTime())) {
    return ''
  }

  return date.toISOString()
}

/**
 * Check if a date value is valid
 */
export const isValidDate = (value: string | number | Date): boolean => {
  if (!value) return false

  let date: Date

  if (value instanceof Date) {
    date = value
  } else if (typeof value === 'string') {
    date = new Date(value)
  } else if (typeof value === 'number') {
    date = new Date(value)
  } else {
    return false
  }

  return !isNaN(date.getTime())
}

/**
 * Parse a date string in various formats
 */
export const parseDate = (value: string): Date | null => {
  if (!value) return null

  const cleanValue = value.trim()

  // Handle DD/MM/YYYY format
  if (/^\d{2}\/\d{2}\/\d{4}/.test(cleanValue)) {
    const parts = cleanValue.split('/')
    const date = new Date(`${parts[2]}-${parts[1]}-${parts[0]}`)
    if (!isNaN(date.getTime())) {
      return date
    }
  }

  const date = new Date(value)

  if (!isNaN(date.getTime())) {
    return date
  }

  return null
}

/**
 * Get relative time string (e.g., "2 hours ago")
 */
export const getRelativeTime = (value: string | number | Date): string => {
  if (!value) return '-'

  let date: Date | null
  if (typeof value === 'string') {
    date = parseDate(value)
  } else {
    date = new Date(value)
  }

  if (!date || isNaN(date.getTime())) return '-'

  const now = new Date()
  const diffMs = now.getTime() - date.getTime()
  const diffSeconds = Math.floor(diffMs / 1000)
  const diffMinutes = Math.floor(diffSeconds / 60)
  const diffHours = Math.floor(diffMinutes / 60)
  const diffDays = Math.floor(diffHours / 24)

  if (diffSeconds < 60) {
    return 'Just now'
  } else if (diffMinutes < 60) {
    return `${diffMinutes} minute${diffMinutes !== 1 ? 's' : ''} ago`
  } else if (diffHours < 24) {
    return `${diffHours} hour${diffHours !== 1 ? 's' : ''} ago`
  } else if (diffDays < 30) {
    return `${diffDays} day${diffDays !== 1 ? 's' : ''} ago`
  } else {
    return formatDate(date)
  }
}

/**
 * Debug function to analyze date values
 */
export const debugDateValue = (value: string | number | Date, context: string = ''): void => {
  console.log(`[DateUtils Debug] ${context}:`, {
    value,
    type: typeof value,
    isNull: value === null,
    isUndefined: value === undefined,
    isEmpty: value === '',
    stringValue: String(value),
    parsedDate: value ? new Date(value) : null,
    isValidDate: value ? !isNaN(new Date(value).getTime()) : false
  })
}

import React, { useEffect, useState } from 'react'
import { ThemeContext, Theme, ThemeContextType } from './ThemeContextBase'

// If using React 18+, import type { FC } from 'react' for types

// Types and context moved to ThemeContextBase.tsx for fast refresh compatibility

// Provider props type
interface ThemeProviderProps {
  children: React.ReactNode
  defaultTheme?: Theme
  storageKey?: string
}

// Theme Provider Component
export function ThemeProvider({
  children,
  defaultTheme = 'light',
  storageKey = 'vite-ui-theme'
}: ThemeProviderProps): React.ReactElement {
  const [theme, setTheme] = useState<Theme>(() => {
    if (typeof window !== 'undefined') {
      return (localStorage.getItem(storageKey) as Theme) || defaultTheme
    }
    return defaultTheme
  })

  const [isDark, setIsDark] = useState(false)

  useEffect(() => {
    const root = window.document.documentElement
    root.classList.remove('light', 'dark')

    let effectiveTheme: 'light' | 'dark'

    if (theme === 'system') {
      effectiveTheme = window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    } else {
      effectiveTheme = theme
    }

    root.classList.add(effectiveTheme)
    setIsDark(effectiveTheme === 'dark')
  }, [theme])

  const handleSetTheme = (newTheme: Theme): void => {
    localStorage.setItem(storageKey, newTheme)
    setTheme(newTheme)
  }

  const toggleTheme = (): void => {
    const newTheme = theme === 'dark' ? 'light' : 'dark'
    handleSetTheme(newTheme)
  }

  const value: ThemeContextType = {
    theme,
    isDark,
    setTheme: handleSetTheme,
    toggleTheme
  }

  return <ThemeContext.Provider value={value}>{children}</ThemeContext.Provider>
}

// Custom hook to use theme context is now in useThemeContext.tsx for fast refresh compatibility

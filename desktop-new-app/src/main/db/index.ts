import Database from 'better-sqlite3'
import { app } from 'electron'
import { join } from 'path'
import { ComplaintData } from '../../shared/api'

let db: Database.Database | null = null

export function initializeDatabase(): void {
  try {
    // Use proper cross-platform path for user data
    const dbPath = join(app.getPath('userData'), 'ncrp-matrix.sqlite')
    console.log('[Database] Initializing database at:', dbPath)

    db = new Database(dbPath)

    // Enable WAL mode for better performance
    db.pragma('journal_mode = WAL')

    // Create tables
    createTables()

    console.log('[Database] Database initialized successfully')

    // Log existing complaints for debugging
    const complaints = getComplaints()
    console.log(`[Database] Found ${complaints.length} existing complaints:`, complaints)
  } catch (error) {
    console.error('[Database] Failed to initialize database:', error)
    throw error
  }
}

function createTables(): void {
  if (!db) throw new Error('Database not initialized')

  // Complaints table - optimized for unified data structure
  db.exec(`
    CREATE TABLE IF NOT EXISTS complaints (
      id TEXT PRIMARY KEY,
      title TEXT NOT NULL CHECK(length(title) > 0),
      description TEXT NOT NULL CHECK(length(description) > 0),
      file_name TEXT,
      fraud_type TEXT,
      extraction_type TEXT,
      max_layer INTEGER DEFAULT 7 CHECK(max_layer >= 0 AND max_layer <= 20),
      metadata TEXT CHECK(json_valid(metadata) OR metadata IS NULL), -- JSON string for complaint metadata
      transactions TEXT CHECK(json_valid(transactions) OR transactions IS NULL), -- JSON string for all transactions (legacy)
      layer_transactions TEXT CHECK(json_valid(layer_transactions) OR layer_transactions IS NULL), -- JSON string for unified data structure
      bank_notice_data TEXT CHECK(json_valid(bank_notice_data) OR bank_notice_data IS NULL), -- JSON string for bank notice data
      graph_data TEXT CHECK(json_valid(graph_data) OR graph_data IS NULL), -- JSON string for graph visualization data (unified format)
      extraction_info TEXT CHECK(json_valid(extraction_info) OR extraction_info IS NULL), -- JSON string for extraction metadata
      status TEXT DEFAULT 'processed' CHECK(status IN ('processing', 'processed', 'error', 'archived')),
      created_at TEXT NOT NULL,
      updated_at TEXT NOT NULL
    )
  `)

  // Create indexes for better performance
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_complaints_created_at ON complaints(created_at DESC);
  `)

  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_complaints_fraud_type ON complaints(fraud_type);
  `)

  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_complaints_status ON complaints(status);
  `)

  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_complaints_extraction_type ON complaints(extraction_type);
  `)

  // Create indexes for better performance
  db.exec(`
    CREATE INDEX IF NOT EXISTS idx_complaints_created_at ON complaints(created_at);
    CREATE INDEX IF NOT EXISTS idx_complaints_title ON complaints(title);
    CREATE INDEX IF NOT EXISTS idx_complaints_fraud_type ON complaints(fraud_type);
    CREATE INDEX IF NOT EXISTS idx_complaints_status ON complaints(status);
  `)
}

export function closeDatabase(): void {
  if (db) {
    db.close()
    db = null
    console.log('[Database] Database connection closed')
  }
}

// Utility to ensure all JSON fields are stringified for DB
// Helper to parse JSON fields from DB
function parseJsonField<T>(value: unknown): T | null {
  return typeof value === 'string' && value.length > 0 ? JSON.parse(value) : null
}

// Complaint operations
export function storeComplaint(
  complaint: Omit<ComplaintData, 'id' | 'created_at' | 'updated_at'>
): string {
  if (!db) throw new Error('Database not initialized')

  const id = generateId()
  const now = new Date().toISOString()

  const stmt = db.prepare(`
    INSERT INTO complaints (
      id, title, description, file_name, fraud_type, extraction_type, max_layer,
      metadata, transactions, layer_transactions, bank_notice_data, graph_data,
      extraction_info, status, created_at, updated_at
    )
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `)

  stmt.run(
    id,
    complaint.title,
    complaint.description,
    complaint.file_name ?? undefined,
    complaint.fraud_type ?? undefined,
    complaint.extraction_type ?? undefined,
    complaint.max_layer ?? 7,
    complaint.metadata != null ? JSON.stringify(complaint.metadata) : undefined,
    complaint.transactions != null ? JSON.stringify(complaint.transactions) : undefined,
    complaint.layer_transactions != null ? JSON.stringify(complaint.layer_transactions) : undefined,
    complaint.bank_notice_data != null ? JSON.stringify(complaint.bank_notice_data) : undefined,
    complaint.graph_data != null ? JSON.stringify(complaint.graph_data) : undefined,
    complaint.extraction_info != null ? JSON.stringify(complaint.extraction_info) : undefined,
    complaint.status || 'processed',
    now,
    now
  )

  console.log('[Database] Stored complaint with ID:', id)
  return id
}

// Database validation and optimization functions
export function validateUnifiedDataStructure(data: unknown): { isValid: boolean; errors: string[] } {
  const errors: string[] = []

  if (!data || typeof data !== 'object') {
    errors.push('Data must be an object')
    return { isValid: false, errors }
  }

  const unifiedData = data as Record<string, unknown>

  // Check required properties for unified data structure
  if (!unifiedData.layers || typeof unifiedData.layers !== 'object') {
    errors.push('Missing or invalid layers property')
  }

  if (!Array.isArray(unifiedData.graph_edges)) {
    errors.push('Missing or invalid graph_edges property')
  }

  if (!unifiedData.metadata || typeof unifiedData.metadata !== 'object') {
    errors.push('Missing or invalid metadata property')
  }

  if (typeof unifiedData.max_layer !== 'number') {
    errors.push('Missing or invalid max_layer property')
  }

  if (typeof unifiedData.total_transactions !== 'number') {
    errors.push('Missing or invalid total_transactions property')
  }

  if (typeof unifiedData.total_amount !== 'number') {
    errors.push('Missing or invalid total_amount property')
  }

  return { isValid: errors.length === 0, errors }
}

export function optimizeDatabase(): void {
  if (!db) throw new Error('Database not initialized')

  console.log('[Database] Running optimization...')

  // Analyze and optimize tables
  db.exec('ANALYZE complaints')

  // Vacuum to reclaim space
  db.exec('VACUUM')

  // Update statistics
  db.exec('PRAGMA optimize')

  console.log('[Database] Optimization completed')
}

export function getDatabaseStats(): {
  totalComplaints: number
  totalSize: number
  unifiedDataComplaints: number
  legacyDataComplaints: number
} {
  if (!db) throw new Error('Database not initialized')

  const totalComplaints = db.prepare('SELECT COUNT(*) as count FROM complaints').get() as { count: number }

  // Check for unified vs legacy data structure
  const unifiedDataQuery = db.prepare(`
    SELECT COUNT(*) as count FROM complaints
    WHERE layer_transactions IS NOT NULL
    AND json_valid(layer_transactions) = 1
    AND json_extract(layer_transactions, '$.layers') IS NOT NULL
  `)
  const unifiedDataComplaints = unifiedDataQuery.get() as { count: number }

  const legacyDataComplaints = totalComplaints.count - unifiedDataComplaints.count

  // Get database size (approximate)
  const sizeQuery = db.prepare("SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()")
  const sizeResult = sizeQuery.get() as { size: number }

  return {
    totalComplaints: totalComplaints.count,
    totalSize: sizeResult.size,
    unifiedDataComplaints: unifiedDataComplaints.count,
    legacyDataComplaints
  }
}

export function getComplaints(): ComplaintData[] {
  if (!db) throw new Error('Database not initialized')

  const stmt = db.prepare(`
    SELECT id, title, description, file_name, fraud_type, extraction_type, max_layer,
           metadata, transactions, layer_transactions, bank_notice_data, graph_data,
           extraction_info, status, created_at, updated_at
    FROM complaints
    ORDER BY created_at DESC
  `)

  const rows = stmt.all() as Partial<ComplaintData>[]

  return rows.map((row) => ({
    id: row.id ?? '',
    title: row.title ?? '',
    description: row.description ?? '',
    file_name: row.file_name ?? '',
    fraud_type: row.fraud_type ?? '',
    extraction_type: row.extraction_type ?? '',
    max_layer: row.max_layer ?? 7,
    metadata: parseJsonField<Record<string, unknown>>(row.metadata),
    transactions: parseJsonField<import('../../shared/api').TransactionData[]>(row.transactions),
    layer_transactions: parseJsonField<Record<string, unknown>>(row.layer_transactions),
    bank_notice_data: parseJsonField<Record<string, unknown>>(row.bank_notice_data),
    graph_data: parseJsonField<import('../../shared/api').ComplaintGraphData>(row.graph_data),
    extraction_info: parseJsonField<Record<string, unknown>>(row.extraction_info),
    status: row.status ?? 'processed',
    created_at: row.created_at ?? '',
    updated_at: row.updated_at ?? ''
  }))
}

export function getComplaint(id: string): ComplaintData | null {
  if (!db) throw new Error('Database not initialized')

  const stmt = db.prepare(`
    SELECT id, title, description, file_name, fraud_type, extraction_type, max_layer,
           metadata, transactions, layer_transactions, bank_notice_data, graph_data,
           extraction_info, status, created_at, updated_at
    FROM complaints
    WHERE id = ?
  `)

  const row = stmt.get(id) as Partial<ComplaintData>

  if (!row) return null

  return {
    id: row.id ?? '',
    title: row.title ?? '',
    description: row.description ?? '',
    file_name: row.file_name ?? '',
    fraud_type: row.fraud_type ?? '',
    extraction_type: row.extraction_type ?? '',
    max_layer: row.max_layer ?? 7,
    metadata: parseJsonField<Record<string, unknown>>(row.metadata),
    transactions:
      row.transactions && typeof row.transactions === 'string'
        ? row.transactions !== ''
          ? JSON.parse(row.transactions)
          : null
        : (row.transactions ?? null),
    layer_transactions:
      row.layer_transactions && typeof row.layer_transactions === 'string'
        ? row.layer_transactions !== ''
          ? JSON.parse(row.layer_transactions)
          : null
        : (row.layer_transactions ?? null),
    bank_notice_data:
      row.bank_notice_data && typeof row.bank_notice_data === 'string'
        ? row.bank_notice_data !== ''
          ? JSON.parse(row.bank_notice_data)
          : null
        : (row.bank_notice_data ?? null),
    graph_data:
      row.graph_data && typeof row.graph_data === 'string'
        ? row.graph_data !== ''
          ? JSON.parse(row.graph_data)
          : null
        : (row.graph_data ?? null),
    extraction_info:
      row.extraction_info && typeof row.extraction_info === 'string'
        ? row.extraction_info !== ''
          ? JSON.parse(row.extraction_info)
          : null
        : (row.extraction_info ?? null),
    status: row.status ?? 'processed',
    created_at: row.created_at ?? '',
    updated_at: row.updated_at ?? ''
  }
}

export function deleteComplaint(id: string): boolean {
  if (!db) throw new Error('Database not initialized')

  const stmt = db.prepare('DELETE FROM complaints WHERE id = ?')
  const result = stmt.run(id)

  const deleted = result.changes > 0
  if (deleted) {
    console.log('[Database] Deleted complaint with ID:', id)
  }

  return deleted
}

export function updateComplaint(
  id: string,
  data: Partial<Omit<ComplaintData, 'id' | 'created_at' | 'updated_at'>>
): boolean {
  if (!db) throw new Error('Database not initialized')

  const now = new Date().toISOString()
  const fields = Object.keys(data)
    .map((k) => `${k} = ?`)
    .join(', ')
  const values = Object.values(data).map((v) => (typeof v === 'object' ? JSON.stringify(v) : v))

  if (fields.length === 0) {
    console.warn('[Database] Update called with no fields to update for ID:', id)
    return false
  }

  const stmt = db.prepare(`
    UPDATE complaints
    SET ${fields}, updated_at = ?
    WHERE id = ?
  `)

  const result = stmt.run(...values, now, id)
  const updated = result.changes > 0

  if (updated) {
    console.log('[Database] Updated complaint with ID:', id)
  }

  return updated
}

function generateId(): string {
  return `complaint_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`
}

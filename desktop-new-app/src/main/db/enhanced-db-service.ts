import Database from 'better-sqlite3'
import { app, IpcMainInvokeEvent } from 'electron'
import { join } from 'path'
import { ComplaintData, GraphNode, GraphEdge, NodeData, ComplaintGraphData } from '../../shared/api'

// Validation schemas
interface ValidationRule {
  required?: boolean
  type?: 'string' | 'number' | 'boolean' | 'object' | 'array'
  minLength?: number
  maxLength?: number
  min?: number
  max?: number
  pattern?: RegExp
  custom?: (value: unknown) => boolean | string
}

interface ValidationSchema {
  [key: string]: ValidationRule
}

// Complaint validation schema
const complaintSchema: ValidationSchema = {
  title: { required: true, type: 'string', minLength: 1, maxLength: 255 },
  description: { required: true, type: 'string', minLength: 1, maxLength: 2000 },
  file_name: { type: 'string', maxLength: 255 },
  fraud_type: { type: 'string', maxLength: 100 },
  extraction_type: { type: 'string', maxLength: 100 },
  max_layer: { type: 'number', min: 1, max: 20 },
  status: { type: 'string', maxLength: 50 }
}

// Graph node validation schema
const graphNodeSchema: ValidationSchema = {
  id: { required: true, type: 'string', minLength: 1 },
  type: { type: 'string', maxLength: 50 },
  position: { required: true, type: 'object' },
  data: { required: true, type: 'object' }
}

// Graph edge validation schema
const graphEdgeSchema: ValidationSchema = {
  id: { required: true, type: 'string', minLength: 1 },
  source: { required: true, type: 'string', minLength: 1 },
  target: { required: true, type: 'string', minLength: 1 },
  type: { type: 'string', maxLength: 50 },
  data: { type: 'object' }
}

export class EnhancedDatabaseService {
  private db: Database.Database | null = null
  private isInitialized = false

  /**
   * Initialize the database with proper error handling
   */
  public async initialize(): Promise<void> {
    try {
      const dbPath = join(app.getPath('userData'), 'ncrp-matrix.sqlite')
      console.log('[Enhanced DB] Initializing database at:', dbPath)

      this.db = new Database(dbPath)

      // Enable WAL mode for better performance and concurrency
      this.db.pragma('journal_mode = WAL')
      this.db.pragma('synchronous = NORMAL')
      this.db.pragma('cache_size = 1000')
      this.db.pragma('temp_store = memory')

      // Enable foreign keys
      this.db.pragma('foreign_keys = ON')

      await this.createTables()
      await this.createIndexes()

      this.isInitialized = true
      console.log('[Enhanced DB] Database initialized successfully')
    } catch (error) {
      console.error('[Enhanced DB] Failed to initialize database:', error)
      throw new Error(`Database initialization failed: ${error}`)
    }
  }

  /**
   * Create database tables with proper constraints
   */
  private async createTables(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    // Create complaints table with constraints
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS complaints (
        id TEXT PRIMARY KEY,
        title TEXT NOT NULL CHECK(length(title) > 0 AND length(title) <= 255),
        description TEXT NOT NULL CHECK(length(description) > 0 AND length(description) <= 2000),
        file_name TEXT CHECK(length(file_name) <= 255),
        fraud_type TEXT CHECK(length(fraud_type) <= 100),
        extraction_type TEXT CHECK(length(extraction_type) <= 100),
        max_layer INTEGER DEFAULT 7 CHECK(max_layer >= 1 AND max_layer <= 20),
        metadata TEXT CHECK(json_valid(metadata) OR metadata IS NULL),
        transactions TEXT CHECK(json_valid(transactions) OR transactions IS NULL),
        layer_transactions TEXT CHECK(json_valid(layer_transactions) OR layer_transactions IS NULL),
        bank_notice_data TEXT CHECK(json_valid(bank_notice_data) OR bank_notice_data IS NULL),
        graph_data TEXT CHECK(json_valid(graph_data) OR graph_data IS NULL),
        extraction_info TEXT CHECK(json_valid(extraction_info) OR extraction_info IS NULL),
        status TEXT DEFAULT 'processed' CHECK(length(status) <= 50),
        created_at TEXT NOT NULL,
        updated_at TEXT NOT NULL
      )
    `)

    // Create audit log table for tracking changes
    this.db.exec(`
      CREATE TABLE IF NOT EXISTS audit_log (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        table_name TEXT NOT NULL,
        record_id TEXT NOT NULL,
        operation TEXT NOT NULL CHECK(operation IN ('INSERT', 'UPDATE', 'DELETE')),
        old_values TEXT CHECK(json_valid(old_values) OR old_values IS NULL),
        new_values TEXT CHECK(json_valid(new_values) OR new_values IS NULL),
        timestamp TEXT NOT NULL,
        user_context TEXT
      )
    `)
  }

  /**
   * Create database indexes for better performance
   */
  private async createIndexes(): Promise<void> {
    if (!this.db) throw new Error('Database not initialized')

    this.db.exec(`
      CREATE INDEX IF NOT EXISTS idx_complaints_created_at ON complaints(created_at);
      CREATE INDEX IF NOT EXISTS idx_complaints_status ON complaints(status);
      CREATE INDEX IF NOT EXISTS idx_complaints_fraud_type ON complaints(fraud_type);
      CREATE INDEX IF NOT EXISTS idx_audit_log_timestamp ON audit_log(timestamp);
      CREATE INDEX IF NOT EXISTS idx_audit_log_record_id ON audit_log(record_id);
    `)
  }

  /**
   * Validate data against schema
   */
  private validateData(
    data: Record<string, unknown> | GraphNode | GraphEdge,
    schema: ValidationSchema
  ): { isValid: boolean; errors: string[] } {
    const errors: string[] = []

    for (const [field, rules] of Object.entries(schema)) {
      const value = data[field]

      // Check required fields
      if (rules.required && (value === undefined || value === null || value === '')) {
        errors.push(`Field '${field}' is required`)
        continue
      }

      // Skip validation for optional empty fields
      if (!rules.required && (value === undefined || value === null || value === '')) {
        continue
      }

      // Type validation
      if (rules.type) {
        const actualType = Array.isArray(value) ? 'array' : typeof value
        if (actualType !== rules.type) {
          errors.push(`Field '${field}' must be of type ${rules.type}, got ${actualType}`)
          continue
        }
      }

      // String validations
      if (typeof value === 'string') {
        if (rules.minLength && value.length < rules.minLength) {
          errors.push(`Field '${field}' must be at least ${rules.minLength} characters long`)
        }
        if (rules.maxLength && value.length > rules.maxLength) {
          errors.push(`Field '${field}' must be at most ${rules.maxLength} characters long`)
        }
        if (rules.pattern && !rules.pattern.test(value)) {
          errors.push(`Field '${field}' does not match required pattern`)
        }
      }

      // Number validations
      if (typeof value === 'number') {
        if (rules.min !== undefined && value < rules.min) {
          errors.push(`Field '${field}' must be at least ${rules.min}`)
        }
        if (rules.max !== undefined && value > rules.max) {
          errors.push(`Field '${field}' must be at most ${rules.max}`)
        }
      }

      // Custom validation
      if (rules.custom) {
        const customResult = rules.custom(value)
        if (typeof customResult === 'string') {
          errors.push(`Field '${field}': ${customResult}`)
        } else if (!customResult) {
          errors.push(`Field '${field}' failed custom validation`)
        }
      }
    }

    return { isValid: errors.length === 0, errors }
  }

  /**
   * Sanitize input data
   */
  private sanitizeData<T>(data: T): T {
    if (typeof data === 'string') {
      // Remove potential SQL injection patterns and trim whitespace
      return data.trim().replace(/[<>]/g, '') as T
    }

    if (typeof data === 'object' && data !== null) {
      const sanitized = Array.isArray(data) ? [] : ({} as Record<string, unknown>)
      for (const [key, value] of Object.entries(data)) {
        sanitized[key] = this.sanitizeData(value)
      }
      return sanitized as T
    }

    return data
  }

  /**
   * Execute database operation with transaction support
   */
  private executeWithTransaction<T>(operation: () => T): T {
    if (!this.db) throw new Error('Database not initialized')

    const transaction = this.db.transaction(operation)
    return transaction()
  }

  /**
   * Log audit trail
   */
  private logAudit(
    tableName: string,
    recordId: string,
    operation: 'INSERT' | 'UPDATE' | 'DELETE',
    oldValues?: ComplaintData | GraphNode | GraphEdge | Record<string, unknown> | null,
    newValues?: ComplaintData | GraphNode | GraphEdge | Record<string, unknown> | null,
    userContext?: string
  ): void {
    if (!this.db) return

    try {
      const stmt = this.db.prepare(`
        INSERT INTO audit_log (table_name, record_id, operation, old_values, new_values, timestamp, user_context)
        VALUES (?, ?, ?, ?, ?, ?, ?)
      `)

      stmt.run(
        tableName,
        recordId,
        operation,
        oldValues ? JSON.stringify(oldValues) : null,
        newValues ? JSON.stringify(newValues) : null,
        new Date().toISOString(),
        userContext || null
      )
    } catch (error) {
      console.error('[Enhanced DB] Failed to log audit:', error)
    }
  }

  /**
   * Store complaint with validation and audit logging
   */
  public storeComplaint(
    complaint: Omit<ComplaintData, 'id' | 'created_at' | 'updated_at'>,
    userContext?: string
  ): string {
    if (!this.isInitialized || !this.db) {
      throw new Error('Database not initialized')
    }

    // Sanitize input
    const sanitizedComplaint = this.sanitizeData(complaint)

    // Validate input
    const validation = this.validateData(sanitizedComplaint, complaintSchema)
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`)
    }

    return this.executeWithTransaction(() => {
      const id = this.generateId()
      const now = new Date().toISOString()

      const stmt = this.db!.prepare(`
        INSERT INTO complaints (
          id, title, description, file_name, fraud_type, extraction_type, max_layer,
          metadata, transactions, layer_transactions, bank_notice_data, graph_data,
          extraction_info, status, created_at, updated_at
        )
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
      `)

      const complaintData = {
        id,
        ...sanitizedComplaint,
        created_at: now,
        updated_at: now
      }

      stmt.run(
        id,
        sanitizedComplaint.title,
        sanitizedComplaint.description,
        sanitizedComplaint.file_name || null,
        sanitizedComplaint.fraud_type || null,
        sanitizedComplaint.extraction_type || null,
        sanitizedComplaint.max_layer || 7,
        sanitizedComplaint.metadata ? JSON.stringify(sanitizedComplaint.metadata) : null,
        sanitizedComplaint.transactions ? JSON.stringify(sanitizedComplaint.transactions) : null,
        sanitizedComplaint.layer_transactions
          ? JSON.stringify(sanitizedComplaint.layer_transactions)
          : null,
        sanitizedComplaint.bank_notice_data
          ? JSON.stringify(sanitizedComplaint.bank_notice_data)
          : null,
        sanitizedComplaint.graph_data ? JSON.stringify(sanitizedComplaint.graph_data) : null,
        sanitizedComplaint.extraction_info
          ? JSON.stringify(sanitizedComplaint.extraction_info)
          : null,
        sanitizedComplaint.status || 'processed',
        now,
        now
      )

      // Log audit trail
      this.logAudit('complaints', id, 'INSERT', null, complaintData, userContext)

      console.log('[Enhanced DB] Stored complaint with ID:', id)
      return id
    })
  }

  /**
   * Update complaint with validation and audit logging
   */
  public updateComplaint(
    id: string,
    updates: Partial<Omit<ComplaintData, 'id' | 'created_at' | 'updated_at'>>,
    userContext?: string
  ): boolean {
    if (!this.isInitialized || !this.db) {
      throw new Error('Database not initialized')
    }

    // Get current data for audit log
    const currentData = this.getComplaint(id)
    if (!currentData) {
      throw new Error(`Complaint with ID ${id} not found`)
    }

    // Sanitize input
    const sanitizedUpdates = this.sanitizeData(updates)

    // Validate updates against schema (partial validation)
    const updateSchema: ValidationSchema = {}
    for (const key of Object.keys(sanitizedUpdates)) {
      if (complaintSchema[key]) {
        updateSchema[key] = { ...complaintSchema[key], required: false }
      }
    }

    const validation = this.validateData(sanitizedUpdates, updateSchema)
    if (!validation.isValid) {
      throw new Error(`Validation failed: ${validation.errors.join(', ')}`)
    }

    return this.executeWithTransaction(() => {
      const now = new Date().toISOString()
      const fields = Object.keys(sanitizedUpdates)
        .map((k) => `${k} = ?`)
        .join(', ')

      const values = Object.values(sanitizedUpdates).map((v) =>
        typeof v === 'object' && v !== null ? JSON.stringify(v) : v
      )

      const stmt = this.db!.prepare(`
        UPDATE complaints
        SET ${fields}, updated_at = ?
        WHERE id = ?
      `)

      const result = stmt.run(...values, now, id)
      const success = result.changes > 0

      if (success) {
        // Log audit trail
        this.logAudit(
          'complaints',
          id,
          'UPDATE',
          currentData,
          { ...currentData, ...sanitizedUpdates },
          userContext
        )
        console.log('[Enhanced DB] Updated complaint with ID:', id)
      }

      return success
    })
  }

  /**
   * Delete complaint with audit logging
   */
  public deleteComplaint(id: string, userContext?: string): boolean {
    if (!this.isInitialized || !this.db) {
      throw new Error('Database not initialized')
    }

    // Get current data for audit log
    const currentData = this.getComplaint(id)
    if (!currentData) {
      return false
    }

    return this.executeWithTransaction(() => {
      const stmt = this.db!.prepare('DELETE FROM complaints WHERE id = ?')
      const result = stmt.run(id)
      const success = result.changes > 0

      if (success) {
        // Log audit trail
        this.logAudit('complaints', id, 'DELETE', currentData, null, userContext)
        console.log('[Enhanced DB] Deleted complaint with ID:', id)
      }

      return success
    })
  }

  /**
   * Get complaint by ID with error handling
   */
  public getComplaint(id: string): ComplaintData | null {
    if (!this.isInitialized || !this.db) {
      throw new Error('Database not initialized')
    }

    try {
      const stmt = this.db.prepare(`
        SELECT * FROM complaints WHERE id = ?
      `)

      const row = stmt.get(id) as unknown as Record<string, unknown>
      if (!row) return null

      return this.parseComplaintRow(row)
    } catch (error) {
      console.error('[Enhanced DB] Failed to get complaint:', error)
      throw new Error(`Failed to retrieve complaint: ${error}`)
    }
  }

  /**
   * Get all complaints with pagination and filtering
   */
  public getComplaints(options?: {
    limit?: number
    offset?: number
    status?: string
    fraudType?: string
    sortBy?: string
    sortOrder?: 'ASC' | 'DESC'
  }): { complaints: ComplaintData[]; total: number } {
    if (!this.isInitialized || !this.db) {
      throw new Error('Database not initialized')
    }

    try {
      const {
        limit = 50,
        offset = 0,
        status,
        fraudType,
        sortBy = 'created_at',
        sortOrder = 'DESC'
      } = options || {}

      // Build WHERE clause
      const conditions: string[] = []
      const params: unknown[] = []

      if (status) {
        conditions.push('status = ?')
        params.push(status)
      }

      if (fraudType) {
        conditions.push('fraud_type = ?')
        params.push(fraudType)
      }

      const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''

      // Get total count
      const countStmt = this.db.prepare(`
        SELECT COUNT(*) as total FROM complaints ${whereClause}
      `)
      const { total } = countStmt.get(...params) as { total: number }

      // Get paginated results
      const stmt = this.db.prepare(`
        SELECT * FROM complaints 
        ${whereClause}
        ORDER BY ${sortBy} ${sortOrder}
        LIMIT ? OFFSET ?
      `)

      const rows = stmt.all(...params, limit, offset) as Record<string, unknown>[]
      const complaints = rows.map((row) => this.parseComplaintRow(row))

      return { complaints, total }
    } catch (error) {
      console.error('[Enhanced DB] Failed to get complaints:', error)
      throw new Error(`Failed to retrieve complaints: ${error}`)
    }
  }

  /**
   * Parse complaint row from database
   */
  private parseComplaintRow(row: Record<string, unknown>): ComplaintData {
    return {
      id: String(row.id),
      title: String(row.title),
      description: String(row.description),
      file_name: String(row.file_name || ''),
      fraud_type: String(row.fraud_type || ''),
      extraction_type: String(row.extraction_type || ''),
      max_layer: Number(row.max_layer) || 7,
      metadata: this.parseJsonField(row.metadata as string | null) || {},
      transactions: this.parseJsonField(row.transactions as string | null) || null,
      layer_transactions: this.parseJsonField(row.layer_transactions as string | null) || {},
      bank_notice_data: this.parseJsonField(row.bank_notice_data as string | null) || {},
      graph_data: this.parseJsonField(row.graph_data as string | null) || {
        nodes: [],
        edges: [],
        transactions: [],
        metadata: {},
        max_layer: 7
      },
      extraction_info: this.parseJsonField(row.extraction_info as string | null) || {},
      status: String(row.status || 'processed'),
      created_at: String(row.created_at),
      updated_at: String(row.updated_at)
    }
  }

  /**
   * Parse JSON field with error handling
   */
  private parseJsonField<T>(field: string | null): T | null {
    if (!field) return null

    try {
      return JSON.parse(field) as T
    } catch (error) {
      console.error('[Enhanced DB] Failed to parse JSON field:', error)
      return null
    }
  }

  /**
   * Generate unique ID
   */
  private generateId(): string {
    return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  }

  /**
   * Get database health status
   */
  public getHealthStatus(): {
    isHealthy: boolean
    isInitialized: boolean
    tableCount: number
    lastError?: string
  } {
    try {
      if (!this.isInitialized || !this.db) {
        return {
          isHealthy: false,
          isInitialized: false,
          tableCount: 0,
          lastError: 'Database not initialized'
        }
      }

      const stmt = this.db.prepare("SELECT COUNT(*) as count FROM sqlite_master WHERE type='table'")
      const { count } = stmt.get() as { count: number }

      return {
        isHealthy: true,
        isInitialized: true,
        tableCount: count
      }
    } catch (error) {
      return {
        isHealthy: false,
        isInitialized: this.isInitialized,
        tableCount: 0,
        lastError: String(error)
      }
    }
  }

  /**
   * Update graph node data
   */
  public updateGraphNode(
    complaintId: string,
    nodeId: string,
    nodeData: Partial<NodeData>,
    userContext?: string
  ): boolean {
    if (!this.isInitialized || !this.db) {
      throw new Error('Database not initialized')
    }

    // Get current complaint
    const complaint = this.getComplaint(complaintId)
    if (!complaint) {
      throw new Error(`Complaint with ID ${complaintId} not found`)
    }

    if (!complaint.graph_data || !complaint.graph_data.nodes) {
      throw new Error('No graph data found for complaint')
    }

    // Find and update the node
    const nodeIndex = complaint.graph_data.nodes.findIndex((node) => node.id === nodeId)
    if (nodeIndex === -1) {
      throw new Error(`Node with ID ${nodeId} not found`)
    }

    const oldNode = complaint.graph_data.nodes[nodeIndex]
    const updatedNode = {
      ...oldNode,
      data: { ...oldNode.data, ...nodeData }
    }

    complaint.graph_data.nodes[nodeIndex] = updatedNode

    // Update the complaint
    const success = this.updateComplaint(
      complaintId,
      { graph_data: complaint.graph_data },
      userContext
    )

    if (success) {
      this.logAudit('graph_nodes', nodeId, 'UPDATE', oldNode, updatedNode, userContext)
    }

    return success
  }

  /**
   * Add new graph node
   */
  public addGraphNode(complaintId: string, node: GraphNode, userContext?: string): boolean {
    if (!this.isInitialized || !this.db) {
      throw new Error('Database not initialized')
    }

    // Validate node data
    const validation = this.validateData(node, graphNodeSchema)
    if (!validation.isValid) {
      throw new Error(`Node validation failed: ${validation.errors.join(', ')}`)
    }

    // Get current complaint
    const complaint = this.getComplaint(complaintId)
    if (!complaint) {
      throw new Error(`Complaint with ID ${complaintId} not found`)
    }

    // Initialize graph data if it doesn't exist
    if (!complaint.graph_data) {
      complaint.graph_data = { nodes: [], edges: [], transactions: [], metadata: {}, max_layer: 7 }
    }

    // Check if node ID already exists
    if (complaint.graph_data.nodes.some((existingNode) => existingNode.id === node.id)) {
      throw new Error(`Node with ID ${node.id} already exists`)
    }

    // Add the node
    complaint.graph_data.nodes.push(node)

    // Update the complaint
    const success = this.updateComplaint(
      complaintId,
      { graph_data: complaint.graph_data },
      userContext
    )

    if (success) {
      this.logAudit('graph_nodes', node.id, 'INSERT', null, node, userContext)
    }

    return success
  }

  /**
   * Delete graph node
   */
  public deleteGraphNode(complaintId: string, nodeId: string, userContext?: string): boolean {
    if (!this.isInitialized || !this.db) {
      throw new Error('Database not initialized')
    }

    // Get current complaint
    const complaint = this.getComplaint(complaintId)
    if (!complaint) {
      throw new Error(`Complaint with ID ${complaintId} not found`)
    }

    if (!complaint.graph_data || !complaint.graph_data.nodes) {
      throw new Error('No graph data found for complaint')
    }

    // Find the node to delete
    const nodeIndex = complaint.graph_data.nodes.findIndex((node) => node.id === nodeId)
    if (nodeIndex === -1) {
      throw new Error(`Node with ID ${nodeId} not found`)
    }

    const deletedNode = complaint.graph_data.nodes[nodeIndex]

    // Remove the node
    complaint.graph_data.nodes.splice(nodeIndex, 1)

    // Remove any edges connected to this node
    if (complaint.graph_data.edges) {
      complaint.graph_data.edges = complaint.graph_data.edges.filter(
        (edge) => edge.source !== nodeId && edge.target !== nodeId
      )
    }

    // Update the complaint
    const success = this.updateComplaint(
      complaintId,
      { graph_data: complaint.graph_data },
      userContext
    )

    if (success) {
      this.logAudit('graph_nodes', nodeId, 'DELETE', deletedNode, null, userContext)
    }

    return success
  }

  /**
   * Add new graph edge
   */
  public addGraphEdge(complaintId: string, edge: GraphEdge, userContext?: string): boolean {
    if (!this.isInitialized || !this.db) {
      throw new Error('Database not initialized')
    }

    // Validate edge data
    const validation = this.validateData(edge, graphEdgeSchema)
    if (!validation.isValid) {
      throw new Error(`Edge validation failed: ${validation.errors.join(', ')}`)
    }

    // Get current complaint
    const complaint = this.getComplaint(complaintId)
    if (!complaint) {
      throw new Error(`Complaint with ID ${complaintId} not found`)
    }

    // Initialize graph data if it doesn't exist
    if (!complaint.graph_data) {
      complaint.graph_data = { nodes: [], edges: [], transactions: [], metadata: {}, max_layer: 7 }
    }

    // Check if edge ID already exists
    if (complaint.graph_data.edges.some((existingEdge) => existingEdge.id === edge.id)) {
      throw new Error(`Edge with ID ${edge.id} already exists`)
    }

    // Verify that source and target nodes exist
    const sourceExists = complaint.graph_data.nodes.some((node) => node.id === edge.source)
    const targetExists = complaint.graph_data.nodes.some((node) => node.id === edge.target)

    if (!sourceExists) {
      throw new Error(`Source node with ID ${edge.source} not found`)
    }
    if (!targetExists) {
      throw new Error(`Target node with ID ${edge.target} not found`)
    }

    // Add the edge
    complaint.graph_data.edges.push(edge)

    // Update the complaint
    const success = this.updateComplaint(
      complaintId,
      { graph_data: complaint.graph_data },
      userContext
    )

    if (success) {
      this.logAudit('graph_edges', edge.id, 'INSERT', null, edge, userContext)
    }

    return success
  }

  /**
   * Delete graph edge
   */
  public deleteGraphEdge(complaintId: string, edgeId: string, userContext?: string): boolean {
    if (!this.isInitialized || !this.db) {
      throw new Error('Database not initialized')
    }

    // Get current complaint
    const complaint = this.getComplaint(complaintId)
    if (!complaint) {
      throw new Error(`Complaint with ID ${complaintId} not found`)
    }

    if (!complaint.graph_data || !complaint.graph_data.edges) {
      throw new Error('No graph data found for complaint')
    }

    // Find the edge to delete
    const edgeIndex = complaint.graph_data.edges.findIndex((edge) => edge.id === edgeId)
    if (edgeIndex === -1) {
      throw new Error(`Edge with ID ${edgeId} not found`)
    }

    const deletedEdge = complaint.graph_data.edges[edgeIndex]

    // Remove the edge
    complaint.graph_data.edges.splice(edgeIndex, 1)

    // Update the complaint
    const success = this.updateComplaint(
      complaintId,
      { graph_data: complaint.graph_data },
      userContext
    )

    if (success) {
      this.logAudit('graph_edges', edgeId, 'DELETE', deletedEdge, null, userContext)
    }

    return success
  }

  /**
   * Get graph data for a complaint
   */
  public getGraphData(complaintId: string): ComplaintGraphData | null {
    const complaint = this.getComplaint(complaintId)
    return complaint?.graph_data || null
  }

  /**
   * Close database connection
   */
  public close(): void {
    if (this.db) {
      this.db.close()
      this.db = null
      this.isInitialized = false
      console.log('[Enhanced DB] Database connection closed')
    }
  }
}

// Enhanced IPC handlers with validation and error handling
export class EnhancedIPCHandlers {
  constructor(private dbService: EnhancedDatabaseService) {}

  /**
   * Handle store complaint with validation
   */
  public async handleStoreComplaint(
    event: IpcMainInvokeEvent,
    complaint: Omit<ComplaintData, 'id' | 'created_at' | 'updated_at'>
  ): Promise<{ success: boolean; id?: string; error?: string }> {
    try {
      // Log the request for audit purposes
      const senderId = event.sender.id
      console.log('[Enhanced IPC] Store complaint requested from:', senderId)

      const id = this.dbService.storeComplaint(complaint, `renderer-process-${senderId}`)
      return { success: true, id }
    } catch (error) {
      console.error('[Enhanced IPC] Store complaint failed:', error)
      return { success: false, error: String(error) }
    }
  }

  /**
   * Handle update complaint with validation
   */
  public async handleUpdateComplaint(
    event: IpcMainInvokeEvent,
    id: string,
    updates: Partial<Omit<ComplaintData, 'id' | 'created_at' | 'updated_at'>>
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const senderId = event.sender.id
      console.log('[Enhanced IPC] Update complaint requested from:', senderId)

      const success = this.dbService.updateComplaint(id, updates, `renderer-process-${senderId}`)
      return { success }
    } catch (error) {
      console.error('[Enhanced IPC] Update complaint failed:', error)
      return { success: false, error: String(error) }
    }
  }

  /**
   * Handle delete complaint with validation
   */
  public async handleDeleteComplaint(
    event: IpcMainInvokeEvent,
    id: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      const senderId = event.sender.id
      console.log('[Enhanced IPC] Delete complaint requested from:', senderId)

      const success = this.dbService.deleteComplaint(id, `renderer-process-${senderId}`)
      return { success }
    } catch (error) {
      console.error('[Enhanced IPC] Delete complaint failed:', error)
      return { success: false, error: String(error) }
    }
  }

  /**
   * Handle get complaint with error handling
   */
  public async handleGetComplaint(
    event: IpcMainInvokeEvent,
    id: string
  ): Promise<{ success: boolean; data?: ComplaintData; error?: string }> {
    try {
      const senderId = event.sender.id
      console.log('[Enhanced IPC] Get complaint requested from:', senderId)

      const data = this.dbService.getComplaint(id)
      return { success: true, data: data || undefined }
    } catch (error) {
      console.error('[Enhanced IPC] Get complaint failed:', error)
      return { success: false, error: String(error) }
    }
  }

  /**
   * Handle get complaints with pagination
   */
  public async handleGetComplaints(
    event: IpcMainInvokeEvent,
    options?: {
      limit?: number
      offset?: number
      status?: string
      fraudType?: string
      sortBy?: string
      sortOrder?: 'ASC' | 'DESC'
    }
  ): Promise<{
    success: boolean
    data?: { complaints: ComplaintData[]; total: number }
    error?: string
  }> {
    try {
      const senderId = event.sender.id
      console.log('[Enhanced IPC] Get complaints requested from:', senderId)

      const data = this.dbService.getComplaints(options)
      return { success: true, data }
    } catch (error) {
      console.error('[Enhanced IPC] Get complaints failed:', error)
      return { success: false, error: String(error) }
    }
  }

  /**
   * Handle database health check
   */
  public async handleHealthCheck(event: IpcMainInvokeEvent): Promise<{
    success: boolean
    health?: {
      isHealthy: boolean
      isInitialized: boolean
      tableCount: number
      lastError?: string
    }
    error?: string
  }> {
    try {
      // Log the event source for audit purposes
      const senderId = event.sender.id
      console.log('[Enhanced IPC] Health check requested from:', senderId)

      const health = this.dbService.getHealthStatus()
      return { success: true, health }
    } catch (error) {
      console.error('[Enhanced IPC] Health check failed:', error)
      return { success: false, error: String(error) }
    }
  }

  /**
   * Handle update graph node
   */
  public async handleUpdateGraphNode(
    event: Electron.IpcMainInvokeEvent,
    complaintId: string,
    nodeId: string,
    nodeData: Partial<NodeData>
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('[Enhanced IPC] Update graph node requested from:', event.sender.id)

      const success = this.dbService.updateGraphNode(
        complaintId,
        nodeId,
        nodeData,
        'renderer-process'
      )
      return { success }
    } catch (error) {
      console.error('[Enhanced IPC] Update graph node failed:', error)
      return { success: false, error: String(error) }
    }
  }

  /**
   * Handle add graph node
   */
  public async handleAddGraphNode(
    event: Electron.IpcMainInvokeEvent,
    complaintId: string,
    node: GraphNode
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('[Enhanced IPC] Add graph node requested from:', event.sender.id)

      const success = this.dbService.addGraphNode(complaintId, node, 'renderer-process')
      return { success }
    } catch (error) {
      console.error('[Enhanced IPC] Add graph node failed:', error)
      return { success: false, error: String(error) }
    }
  }

  /**
   * Handle delete graph node
   */
  public async handleDeleteGraphNode(
    event: Electron.IpcMainInvokeEvent,
    complaintId: string,
    nodeId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('[Enhanced IPC] Delete graph node requested from:', event.sender.id)

      const success = this.dbService.deleteGraphNode(complaintId, nodeId, 'renderer-process')
      return { success }
    } catch (error) {
      console.error('[Enhanced IPC] Delete graph node failed:', error)
      return { success: false, error: String(error) }
    }
  }

  /**
   * Handle add graph edge
   */
  public async handleAddGraphEdge(
    event: Electron.IpcMainInvokeEvent,
    complaintId: string,
    edge: GraphEdge
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('[Enhanced IPC] Add graph edge requested from:', event.sender.id)

      const success = this.dbService.addGraphEdge(complaintId, edge, 'renderer-process')
      return { success }
    } catch (error) {
      console.error('[Enhanced IPC] Add graph edge failed:', error)
      return { success: false, error: String(error) }
    }
  }

  /**
   * Handle delete graph edge
   */
  public async handleDeleteGraphEdge(
    event: Electron.IpcMainInvokeEvent,
    complaintId: string,
    edgeId: string
  ): Promise<{ success: boolean; error?: string }> {
    try {
      console.log('[Enhanced IPC] Delete graph edge requested from:', event.sender.id)

      const success = this.dbService.deleteGraphEdge(complaintId, edgeId, 'renderer-process')
      return { success }
    } catch (error) {
      console.error('[Enhanced IPC] Delete graph edge failed:', error)
      return { success: false, error: String(error) }
    }
  }

  /**
   * Handle get graph data
   */
  public async handleGetGraphData(
    event: Electron.IpcMainInvokeEvent,
    complaintId: string
  ): Promise<{ success: boolean; data?: ComplaintGraphData; error?: string }> {
    try {
      console.log('[Enhanced IPC] Get graph data requested from:', event.sender.id)

      const data = this.dbService.getGraphData(complaintId)
      return { success: true, data: data || undefined }
    } catch (error) {
      console.error('[Enhanced IPC] Get graph data failed:', error)
      return { success: false, error: String(error) }
    }
  }
}

// Export singleton instance
export const enhancedDbService = new EnhancedDatabaseService()
export const enhancedIPCHandlers = new EnhancedIPCHandlers(enhancedDbService)
export default enhancedDbService

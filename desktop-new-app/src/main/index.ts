import { app, shell, <PERSON>rowserWindow, ipc<PERSON>ain } from 'electron'
import { join } from 'path'
import path from 'path'
import fs from 'fs'
import os from 'os'
import { createHash } from 'crypto'
import { electronApp, optimizer, is } from '@electron-toolkit/utils'
import icon from '../../resources/icon.png?asset'
import {
  AUTH_LOGIN,
  AUTH_SIGNUP,
  AUTH_LOGOUT,
  AUTH_GET_SESSION,
  AUTH_HAS_TOKEN,
  AUTH_VERIFY_EMAIL,
  AUTH_RESEND_OTP,
  AUTH_SET_TOKEN,
  AUTH_GET_TOKEN,
  AUTH_CLEAR_TOKEN,
  DB_STORE_COMPLAINT,
  DB_GET_COMPLAINTS,
  DB_GET_COMPLAINT,
  DB_DELETE_COMPLAINT,
  DB_UPDATE_COMPLAINT,
  DB_BACKUP_DATABASE,
  DB_DELETE_COMPLAINTS_BY_MONTH,
  API_UPLOAD_HTML,
  DB_GET_GRAPH_DATA,
  DB_UPDATE_GRAPH_NODE,
  DB_ADD_GRAPH_NODE,
  DB_DELETE_GRAPH_NODE,
  DB_ADD_GRAPH_EDGE,
  DB_DELETE_GRAPH_EDGE,
  IFSC_VALIDATE,
  IFSC_FETCH_DETAILS,
  TEMPLATE_STORE,
  TEMPLATE_GET,
  TEMPLATE_DELETE
} from '../shared/ipc-channels'
import { User, GraphNode, NodeData, GraphEdge } from '../shared/api'
import {
  initializeDatabase,
  closeDatabase,
  storeComplaint,
  getComplaints,
  getComplaint,
  deleteComplaint,
  updateComplaint
} from './db'
import { mainIFSCService } from './services/ifscService'
// Bank enhancement service is no longer used in upload process since backend handles unified data
import { informationSearchService } from './services/informationSearchService'
import { simplifiedAuthService } from './services/simplifiedAuthService'

// IFSC enrichment function for main process
async function enrichUnifiedDataWithIFSC(unifiedData: any): Promise<any> {
  if (!unifiedData || !unifiedData.layers) {
    return unifiedData
  }

  console.log('[Main] Starting IFSC enrichment for unified data')

  // Enrich each layer's transactions
  for (const [, layerData] of Object.entries(unifiedData.layers)) {
    if (layerData && typeof layerData === 'object' && 'transactions' in layerData) {
      const transactions = (layerData as any).transactions
      if (Array.isArray(transactions)) {
        for (const transaction of transactions) {
          if (transaction.core && transaction.core.receiver_ifsc) {
            try {
              const ifscDetails = await mainIFSCService.fetchIFSCDetails(transaction.core.receiver_ifsc)
              if (ifscDetails) {
                // Enrich the transaction with IFSC data
                transaction.core.receiver_bank = `${ifscDetails.BANK} - ${ifscDetails.BRANCH}`
                transaction.core.receiver_bank_full = ifscDetails.BANK
                transaction.core.receiver_branch = ifscDetails.BRANCH
                transaction.core.receiver_district = ifscDetails.DISTRICT
                transaction.core.receiver_state = ifscDetails.STATE
                transaction.core.receiver_city = ifscDetails.CITY
                transaction.core.receiver_address = ifscDetails.ADDRESS

                // Add enriched bank data
                transaction.enriched_bank_data = {
                  original_bank_name: transaction.core.receiver_bank || '',
                  bank_name: ifscDetails.BANK,
                  branch_name: ifscDetails.BRANCH || '',
                  branch_address: ifscDetails.ADDRESS || '',
                  branch_state: ifscDetails.STATE || '',
                  branch_city: ifscDetails.CITY || '',
                  branch_district: ifscDetails.DISTRICT || '',
                  ifsc_code: transaction.core.receiver_ifsc,
                  is_ifsc_valid: true,
                  enrichment_status: 'success',
                  enriched_at: new Date().toISOString()
                }

                console.log(`[Main] Enriched transaction with IFSC ${transaction.core.receiver_ifsc}`)
              }
            } catch (error) {
              console.error(`[Main] Failed to enrich IFSC ${transaction.core.receiver_ifsc}:`, error)
            }
          }
        }
      }
    }
  }

  console.log('[Main] IFSC enrichment completed')
  return unifiedData
}
const BACKEND_BASE_URL = 'http://127.0.0.1:8000/api/v1'

function createWindow(): void {
  // Create the browser window.
  const mainWindow = new BrowserWindow({
    width: 900,
    height: 670,
    show: false,
    autoHideMenuBar: true,
    ...(process.platform === 'linux' ? { icon } : {}),
    webPreferences: {
      preload: join(__dirname, '../preload/index.js'),
      contextIsolation: true,
      nodeIntegration: false,
      sandbox: true
    }
  })

  mainWindow.on('ready-to-show', () => {
    mainWindow.show()
  })

  mainWindow.webContents.setWindowOpenHandler((details) => {
    shell.openExternal(details.url)
    return { action: 'deny' }
  })

  // HMR for renderer base on electron-vite cli.
  // Load the remote URL for development or the local html file for production.
  if (is.dev && process.env['ELECTRON_RENDERER_URL']) {
    mainWindow.loadURL(process.env['ELECTRON_RENDERER_URL'])
  } else {
    mainWindow.loadFile(join(__dirname, '../renderer/index.html'))
  }
}

// This method will be called when Electron has finished
// initialization and is ready to create browser windows.
// Some APIs can only be used after this event occurs.

// Token management is now handled by SimplifiedAuthService

app.whenReady().then(() => {
  // Set app user model id for windows
  electronApp.setAppUserModelId('com.electron')

  // Initialize local database
  try {
    initializeDatabase()
  } catch (error) {
    console.error('[Main] Failed to initialize database:', error)
  }

  // Default open or close DevTools by F12 in development
  // and ignore CommandOrControl + R in production.
  // see https://github.com/alex8088/electron-toolkit/tree/master/packages/utils
  app.on('browser-window-created', (_, window) => {
    optimizer.watchWindowShortcuts(window)
  })

  // IPC Handlers for simplified token management
  ipcMain.handle(AUTH_SET_TOKEN, async (_, token: string, refreshToken?: string, userEmail?: string) => {
    try {
      await simplifiedAuthService.storeToken(token, refreshToken, userEmail)
      return { success: true }
    } catch (error) {
      console.error('[Main] Failed to set token:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Set token failed' }
    }
  })

  ipcMain.handle(AUTH_GET_TOKEN, async () => {
    try {
      const token = await simplifiedAuthService.getToken()
      return { token }
    } catch (error) {
      console.error('[Main] Failed to get token:', error)
      return { token: null, error: error instanceof Error ? error.message : 'Get token failed' }
    }
  })

  ipcMain.handle(AUTH_CLEAR_TOKEN, async () => {
    try {
      simplifiedAuthService.clearToken()
      return { success: true }
    } catch (error) {
      console.error('[Main] Failed to clear token:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Clear token failed'
      }
    }
  })

  ipcMain.handle(AUTH_HAS_TOKEN, async () => {
    try {
      const hasToken = await simplifiedAuthService.hasValidToken()
      return { hasToken }
    } catch (error) {
      console.error('[Main] Failed to check token:', error)
      return { hasToken: false }
    }
  })

  // IPC Handlers for backend communication
  ipcMain.handle(AUTH_LOGIN, async (_, credentials) => {
    try {
      // Generate device fingerprint with hardware specs
      const deviceInfo = simplifiedAuthService.getDeviceInfo()

      const url = `${BACKEND_BASE_URL}/auth/login`
      const body = new URLSearchParams({
        username: credentials.email,
        password: credentials.password,
        device_fingerprint: deviceInfo.fingerprint
      }).toString()
      const headers = { 'Content-Type': 'application/x-www-form-urlencoded' }
      console.log('[Electron Main] LOGIN: URL:', url)
      console.log('[Electron Main] LOGIN: Body (without sensitive data):', {
        username: credentials.email,
        device_fingerprint: deviceInfo.fingerprint.substring(0, 16) + '...'
      })
      console.log('[Electron Main] LOGIN: Headers:', headers)

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body
      })

      console.log('[Electron Main] LOGIN: Response status:', response.status)
      const data = await response.text()
      console.log('[Electron Main] LOGIN: Response body:', data)

      let parsed
      try {
        parsed = JSON.parse(data)
      } catch {
        parsed = { raw: data }
      }
      // Adapt response to what frontend expects
      if (response.ok && parsed.access_token) {
        // Store token using simplified auth service
        try {
          await simplifiedAuthService.storeToken(
            parsed.access_token,
            parsed.refresh_token,
            credentials.email
          )
        } catch (tokenError) {
          console.error('[Main] Failed to store token:', tokenError)
        }

        return {
          success: true,
          token: parsed.access_token,
          refresh_token: parsed.refresh_token,
          user: parsed.user
        }
      }
      return {
        success: false,
        error: parsed.error || parsed.message || 'Invalid credentials'
      }
    } catch (error) {
      console.error('[Electron Main] LOGIN: Error:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Login failed' }
    }
  })

  ipcMain.handle(AUTH_SIGNUP, async (_, credentials) => {
    try {
      console.log('[Electron Main] SIGNUP: Sending registration request with device fingerprint:', {
        email: credentials.email,
        device_fingerprint: credentials.device_fingerprint ? 'present' : 'missing'
      })

      const response = await fetch(`${BACKEND_BASE_URL}/auth/register`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(credentials)
      })
      const data = await response.json()
      // The backend now returns `requires_verification` and `email`
      // We don't set token here as user needs to verify email first
      return data
    } catch (error: unknown) {
      console.error('Signup failed in main process:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Signup failed' }
    }
  })

  ipcMain.handle(AUTH_VERIFY_EMAIL, async (_, { email, otp }) => {
    console.log('[Electron Main] OTP VERIFY: Handler called')
    try {
      const url = `${BACKEND_BASE_URL}/auth/verify-email`
      const payload = {
        email: typeof email === 'string' ? email : String(email),
        otp: typeof otp === 'string' ? otp : String(otp)
      }
      const headers = { 'Content-Type': 'application/json' }
      console.log('[Electron Main] OTP VERIFY: URL:', url)
      console.log('[Electron Main] OTP VERIFY: Payload:', JSON.stringify(payload))
      console.log('[Electron Main] OTP VERIFY: Headers:', headers)

      const response = await fetch(url, {
        method: 'POST',
        headers,
        body: JSON.stringify(payload)
      })

      console.log('[Electron Main] OTP VERIFY: Response status:', response.status)
      const data = await response.text()
      console.log('[Electron Main] OTP VERIFY: Response body:', data)

      let parsed
      try {
        parsed = JSON.parse(data)
      } catch {
        parsed = { raw: data }
      }
      return parsed
    } catch (error) {
      console.error('[Electron Main] OTP VERIFY: Error:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Email verification failed'
      }
    }
  })

  ipcMain.handle(AUTH_RESEND_OTP, async (_, { email }) => {
    try {
      const response = await fetch(`${BACKEND_BASE_URL}/auth/resend-otp`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ email })
      })
      const data = await response.json()
      return data
    } catch (error: unknown) {
      console.error('Resend OTP failed in main process:', error)
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Failed to resend OTP'
      }
    }
  })

  // Simplified logout with token cleanup
  ipcMain.handle(AUTH_LOGOUT, async () => {
    try {
      // Use the enhanced logout method that clears tokens and regenerates session ID
      await simplifiedAuthService.logout()
      console.log('[Main] User logged out successfully')
      return { success: true }
    } catch (error) {
      console.error('[Main] Failed to logout:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Logout failed' }
    }
  })

  ipcMain.handle(AUTH_GET_SESSION, async (): Promise<User | null> => {
    try {
      const token = await simplifiedAuthService.getToken()
      if (!token) return null

      const response = await fetch(`${BACKEND_BASE_URL}/auth/verify`, {
        headers: { Authorization: `Bearer ${token}` }
      })
      if (!response.ok) {
        console.error(
          '[Electron Main] GET_SESSION: Backend /auth/verify returned non-ok status:',
          response.status
        )
        // Clear invalid token
        simplifiedAuthService.clearToken()
        return null
      }
      const data = await response.json()
      if (data.valid && data.user) {
        return data.user
      }
      console.error('[Electron Main] GET_SESSION: Invalid response from /auth/verify:', data)
      return null
    } catch (error) {
      console.error('[Electron Main] GET_SESSION: Error:', error)
      return null
    }
  })

  // AUTH_HAS_TOKEN is now handled by the simplified auth service above

  // System information handler for device fingerprinting
  ipcMain.handle('system:getSystemInfo', async () => {
    try {
      const networkInterfaces = os.networkInterfaces()
      const networkMacs = Object.values(networkInterfaces)
        .flat()
        .filter(iface => iface && !iface.internal && iface.mac !== '00:00:00:00:00:00')
        .map(iface => iface!.mac)
        .filter((mac, index, arr) => arr.indexOf(mac) === index) // Remove duplicates

      // Generate machine ID from system characteristics
      const machineData = {
        platform: os.platform(),
        arch: os.arch(),
        hostname: os.hostname(),
        cpus: os.cpus().length,
        totalMemory: os.totalmem(),
        networkMacs: networkMacs.sort(),
        userInfo: os.userInfo().username
      }

      const machineId = createHash('sha256')
        .update(JSON.stringify(machineData))
        .digest('hex')

      return {
        platform: os.platform(),
        arch: os.arch(),
        cpuCores: os.cpus().length,
        totalMemory: os.totalmem(),
        osVersion: os.release(),
        hostname: os.hostname(),
        machineId: machineId,
        hardwareUuid: machineId, // Use same as machine ID for simplicity
        networkInterfaces: networkMacs
      }
    } catch (error) {
      console.error('[Main] Failed to get system info:', error)
      return {
        platform: 'unknown',
        arch: 'unknown',
        cpuCores: 0,
        totalMemory: 0,
        osVersion: 'unknown',
        hostname: 'unknown',
        machineId: null,
        hardwareUuid: null,
        networkInterfaces: []
      }
    }
  })

  // Database IPC Handlers
  ipcMain.handle(DB_STORE_COMPLAINT, async (_, complaint) => {
    try {
      return storeComplaint(complaint)
    } catch (error) {
      console.error('[Main] Failed to store complaint:', error)
      throw error
    }
  })

  ipcMain.handle(DB_GET_COMPLAINTS, async () => {
    try {
      return getComplaints()
    } catch (error) {
      console.error('[Main] Failed to get complaints:', error)
      throw error
    }
  })

  ipcMain.handle(DB_GET_COMPLAINT, async (_, id) => {
    try {
      return getComplaint(id)
    } catch (error) {
      console.error('[Main] Failed to get complaint:', error)
      throw error
    }
  })

  ipcMain.handle(DB_DELETE_COMPLAINT, async (_, id) => {
    try {
      return deleteComplaint(id)
    } catch (error) {
      console.error('[Main] Failed to delete complaint:', error)
      throw error
    }
  })

  ipcMain.handle(DB_UPDATE_COMPLAINT, async (_, id, data) => {
    try {
      return updateComplaint(id, data)
    } catch (error) {
      console.error('[Main] Failed to update complaint:', error)
      throw error
    }
  })

  ipcMain.handle(DB_BACKUP_DATABASE, async () => {
    try {
      const dbPath = join(app.getPath('userData'), 'ncrp-matrix.sqlite')
      console.log('[Main] Creating database backup from:', dbPath)

      if (!fs.existsSync(dbPath)) {
        throw new Error('Database file not found')
      }

      const dbData = fs.readFileSync(dbPath)
      const base64Data = dbData.toString('base64')

      return { success: true, data: base64Data }
    } catch (error) {
      console.error('[Main] Failed to backup database:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  ipcMain.handle(DB_DELETE_COMPLAINTS_BY_MONTH, async (_, month: string) => {
    try {
      console.log('[Main] Deleting complaints for month:', month)

      // Parse the month string (format: YYYY-MM)
      const [year, monthNum] = month.split('-')
      if (!year || !monthNum) {
        throw new Error('Invalid month format. Expected YYYY-MM')
      }

      // Get start and end dates for the month
      const startDate = new Date(parseInt(year), parseInt(monthNum) - 1, 1)
      const endDate = new Date(parseInt(year), parseInt(monthNum), 0, 23, 59, 59, 999)

      const startISO = startDate.toISOString()
      const endISO = endDate.toISOString()

      console.log('[Main] Deleting complaints between:', startISO, 'and', endISO)

      // Get all complaints to find ones in the date range
      const allComplaints = getComplaints()
      const complaintsToDelete = allComplaints.filter((complaint) => {
        const createdAt = new Date(complaint.created_at)
        return createdAt >= startDate && createdAt <= endDate
      })

      console.log(`[Main] Found ${complaintsToDelete.length} complaints to delete`)

      // Delete each complaint
      let deletedCount = 0
      for (const complaint of complaintsToDelete) {
        try {
          const success = deleteComplaint(complaint.id)
          if (success) {
            deletedCount++
          }
        } catch (error) {
          console.error(`[Main] Failed to delete complaint ${complaint.id}:`, error)
        }
      }

      console.log(`[Main] Successfully deleted ${deletedCount} complaints`)
      return { success: true }
    } catch (error) {
      console.error('[Main] Failed to delete complaints by month:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  ipcMain.handle(
    API_UPLOAD_HTML,
    async (_, { fileContent, fileName, fraudType, layerDepth, skipThresholdAmount }) => {
      console.log('[Main] uploadHtml args:', {
        fileName,
        fraudType,
        layerDepth,
        skipThresholdAmount,
        fileContentLength: fileContent.length
      })
      try {
        // Get token using simplified auth service
        const token = await simplifiedAuthService.getToken()

        if (!token) {
          return { success: false, error: 'Unauthorized' }
        }

        const formData = new FormData()
        formData.append('file', new Blob([fileContent], { type: 'text/html' }), fileName)
        formData.append('fraud_type', fraudType)
        formData.append('max_layer', String(layerDepth))
        formData.append('skip_threshold', String(skipThresholdAmount))

        const response = await fetch(`${BACKEND_BASE_URL}/extraction/parse-html`, {
          method: 'POST',
          headers: {
            Authorization: `Bearer ${token}`
          },
          body: formData
        })

        const data = await response.json()
        if (response.ok) {
          // Remove any existing complaint with the same complaint_number
          const newComplaintNumber = data.data.metadata.complaint_number
          if (newComplaintNumber !== undefined && newComplaintNumber !== null) {
            const existing = getComplaints()
            existing.forEach((c) => {
              if (c.metadata?.complaint_number === newComplaintNumber) {
                deleteComplaint(c.id)
              }
            })
          }

          console.log('[Main] Processing unified data structure from backend')

          // The backend now sends unified_data and bank_notice_data
          let unifiedData = data.data.unified_data
          const bankNoticeData = data.data.bank_notice_data

          // Enrich unified data with IFSC information
          console.log('[Main] Enriching data with IFSC information')
          unifiedData = await enrichUnifiedDataWithIFSC(unifiedData)

          console.log('[Main] Storing complaint with unified data structure')

          const complaintId = storeComplaint({
            title: `Complaint from ${fileName}`,
            description: `Fraud Type: ${fraudType}`,
            file_name: fileName,
            fraud_type: fraudType,
            extraction_type: data.data.extraction_info.extraction_type || data.data.extraction_info.service,
            max_layer: layerDepth,
            metadata: data.data.metadata,
            transactions: null, // Not used in unified structure
            layer_transactions: unifiedData, // Store unified data
            bank_notice_data: bankNoticeData, // Store bank notice data
            graph_data: unifiedData, // Store unified data (will be transformed by frontend)
            extraction_info: data.data.extraction_info,
            status: 'processed'
          })

          console.log('[Main] Complaint stored with enhanced bank data, ID:', complaintId)
          return { success: true, complaintId: complaintId, message: data.message }
        } else {
          return { success: false, error: data.detail || 'Upload failed' }
        }
      } catch (error) {
        console.error('[Main] Failed to upload HTML:', error)
        return {
          success: false,
          error: error instanceof Error ? error.message : 'HTML upload failed'
        }
      }
    }
  )

  ipcMain.handle(DB_GET_GRAPH_DATA, async (_, complaintId: string) => {
    try {
      const complaint = getComplaint(complaintId)
      return complaint?.graph_data
    } catch (error) {
      console.error('[Main] Failed to get graph data:', error)
      throw error
    }
  })

  ipcMain.handle(
    DB_UPDATE_GRAPH_NODE,
    async (_, complaintId: string, nodeId: string, data: NodeData) => {
      try {
        const complaint = getComplaint(complaintId)
        if (complaint && complaint.graph_data) {
          const nodeIndex = complaint.graph_data.nodes.findIndex((n) => n.id === nodeId)
          if (nodeIndex > -1) {
            complaint.graph_data.nodes[nodeIndex].data = data
            updateComplaint(complaintId, { graph_data: complaint.graph_data })
          }
        }
      } catch (error) {
        console.error('[Main] Failed to update graph node:', error)
        throw error
      }
    }
  )

  ipcMain.handle(DB_ADD_GRAPH_NODE, async (_, complaintId: string, node: GraphNode) => {
    try {
      const complaint = getComplaint(complaintId)
      if (complaint && complaint.graph_data) {
        complaint.graph_data.nodes.push(node)
        updateComplaint(complaintId, { graph_data: complaint.graph_data })
      }
    } catch (error) {
      console.error('[Main] Failed to add graph node:', error)
      throw error
    }
  })

  ipcMain.handle(DB_DELETE_GRAPH_NODE, async (_, complaintId: string, nodeId: string) => {
    try {
      const complaint = getComplaint(complaintId)
      if (complaint && complaint.graph_data) {
        complaint.graph_data.nodes = complaint.graph_data.nodes.filter((n) => n.id !== nodeId)
        complaint.graph_data.edges = complaint.graph_data.edges.filter(
          (e) => e.source !== nodeId && e.target !== nodeId
        )
        updateComplaint(complaintId, { graph_data: complaint.graph_data })
      }
    } catch (error) {
      console.error('[Main] Failed to delete graph node:', error)
      throw error
    }
  })

  ipcMain.handle(DB_ADD_GRAPH_EDGE, async (_, complaintId: string, edge: GraphEdge) => {
    try {
      const complaint = getComplaint(complaintId)
      if (complaint && complaint.graph_data) {
        complaint.graph_data.edges.push(edge)
        updateComplaint(complaintId, { graph_data: complaint.graph_data })
      }
    } catch (error) {
      console.error('[Main] Failed to add graph edge:', error)
      throw error
    }
  })

  ipcMain.handle(DB_DELETE_GRAPH_EDGE, async (_, complaintId: string, edgeId: string) => {
    try {
      const complaint = getComplaint(complaintId)
      if (complaint && complaint.graph_data) {
        complaint.graph_data.edges = complaint.graph_data.edges.filter((e) => e.id !== edgeId)
        updateComplaint(complaintId, { graph_data: complaint.graph_data })
      }
    } catch (error) {
      console.error('[Main] Failed to delete graph edge:', error)
      throw error
    }
  })

  // IFSC Service handlers
  ipcMain.handle(IFSC_VALIDATE, async (_, ifscCode: string): Promise<boolean> => {
    try {
      console.log(`[Main] IFSC validation requested for: ${ifscCode}`)
      const isValid = mainIFSCService.validateIFSC(ifscCode)
      console.log(`[Main] IFSC validation result for ${ifscCode}: ${isValid}`)
      return isValid
    } catch (error) {
      console.error('[Main] Failed to validate IFSC:', error)
      return false
    }
  })

  ipcMain.handle(IFSC_FETCH_DETAILS, async (_, ifscCode: string) => {
    try {
      console.log(`[Main] IFSC details fetch requested for: ${ifscCode}`)
      const details = await mainIFSCService.fetchIFSCDetails(ifscCode)
      console.log(
        `[Main] IFSC details fetch result for ${ifscCode}:`,
        details ? 'Success' : 'Failed'
      )

      // Return in the format expected by the renderer
      return {
        success: details !== null,
        data: details,
        error: details === null ? 'IFSC details not found' : null
      }
    } catch (error) {
      console.error('[Main] Failed to fetch IFSC details:', error)
      return {
        success: false,
        data: null,
        error: error instanceof Error ? error.message : 'Unknown error'
      }
    }
  })

  // Template Management handlers
  ipcMain.handle(
    TEMPLATE_STORE,
    async (_, templateData: { name: string; content: string; fileName: string }) => {
      try {
        console.log('[Main] Template store requested')
        // Store template in user data directory
        const userDataPath = app.getPath('userData')
        const templatePath = path.join(userDataPath, 'template.json')

        const templateInfo = {
          name: templateData.name,
          fileName: templateData.fileName,
          content: templateData.content,
          updatedAt: new Date().toISOString()
        }

        fs.writeFileSync(templatePath, JSON.stringify(templateInfo, null, 2))
        console.log('[Main] Template stored successfully')

        return { success: true }
      } catch (error) {
        console.error('[Main] Failed to store template:', error)
        return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
      }
    }
  )

  ipcMain.handle(TEMPLATE_GET, async () => {
    try {
      console.log('[Main] Template get requested')
      const userDataPath = app.getPath('userData')
      const templatePath = path.join(userDataPath, 'template.json')

      if (!fs.existsSync(templatePath)) {
        return { success: true, template: null }
      }

      const templateData = JSON.parse(fs.readFileSync(templatePath, 'utf8'))
      console.log('[Main] Template retrieved successfully')

      return { success: true, template: templateData }
    } catch (error) {
      console.error('[Main] Failed to get template:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  ipcMain.handle(TEMPLATE_DELETE, async () => {
    try {
      console.log('[Main] Template delete requested')
      const userDataPath = app.getPath('userData')
      const templatePath = path.join(userDataPath, 'template.json')

      if (fs.existsSync(templatePath)) {
        fs.unlinkSync(templatePath)
        console.log('[Main] Template deleted successfully')
      }

      return { success: true }
    } catch (error) {
      console.error('[Main] Failed to delete template:', error)
      return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
    }
  })

  // Information Search IPC Handlers
  ipcMain.handle('INFORMATION_SEARCH', async (_, filters, pagination) => {
    try {
      return informationSearchService.searchRecords(filters, pagination)
    } catch (error) {
      console.error('[Main] Failed to search information records:', error)
      throw error
    }
  })

  ipcMain.handle('INFORMATION_GET_BY_ID', async (_, id) => {
    try {
      return informationSearchService.getRecordById(id)
    } catch (error) {
      console.error('[Main] Failed to get information record:', error)
      throw error
    }
  })

  ipcMain.handle('INFORMATION_CREATE', async (_, record) => {
    try {
      return informationSearchService.createRecord(record)
    } catch (error) {
      console.error('[Main] Failed to create information record:', error)
      throw error
    }
  })

  ipcMain.handle('INFORMATION_UPDATE', async (_, id, record) => {
    try {
      return informationSearchService.updateRecord(id, record)
    } catch (error) {
      console.error('[Main] Failed to update information record:', error)
      throw error
    }
  })

  ipcMain.handle('INFORMATION_DELETE', async (_, id) => {
    try {
      return informationSearchService.deleteRecord(id)
    } catch (error) {
      console.error('[Main] Failed to delete information record:', error)
      throw error
    }
  })

  ipcMain.handle('INFORMATION_GET_BANKS', async () => {
    try {
      return informationSearchService.getUniqueBanks()
    } catch (error) {
      console.error('[Main] Failed to get unique banks:', error)
      throw error
    }
  })

  ipcMain.handle('INFORMATION_GET_DESIGNATIONS', async () => {
    try {
      return informationSearchService.getUniqueDesignations()
    } catch (error) {
      console.error('[Main] Failed to get unique designations:', error)
      throw error
    }
  })

  createWindow()

  app.on('activate', function () {
    // On macOS it's common to re-create a window in the app when the
    // dock icon is clicked and there are no other windows open.
    if (BrowserWindow.getAllWindows().length === 0) createWindow()
  })
})

// Quit when all windows are closed, except on macOS. There, it's common
// for applications and their menu bar to stay active until the user quits
// explicitly with Cmd + Q.
app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit()
  }
})

// Clean up database connection on app quit
app.on('before-quit', async () => {
  console.log('[Main] App is quitting, performing cleanup...')

  // Close database connections
  closeDatabase()

  // Close information search database
  try {
    informationSearchService.close()
    console.log('[Main] Information search database closed')
  } catch (error) {
    console.error('[Main] Failed to close information search database:', error)
  }

  // Token cleanup is handled automatically by SimplifiedAuthService lifecycle handlers
  console.log('[Main] Cleanup completed')
})

// In this file you can include the rest of your app's specific main process
// code. You can also put them in separate files and require them here.

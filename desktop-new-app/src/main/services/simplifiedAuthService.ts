/**
 * Simplified Authentication Service for Electron
 * 
 * Implements simplified token-based authentication with:
 * - Token valid until app closes or device shuts down
 * - Secure token deletion on app closure
 * - Device fingerprinting with hardware-level identifiers
 * - No session management or complex state tracking
 */

import { app, safeStorage } from 'electron'
import * as fs from 'fs'
import * as path from 'path'
import * as os from 'os'
import { createHash } from 'crypto'

interface AuthToken {
  access_token: string
  refresh_token?: string
  expires_at: number
  user_email: string
  device_fingerprint: string
  session_id: string
  created_at: number
}

interface DeviceHardwareSpecs {
  platform: string
  arch: string
  cpuModel: string
  cpuCores: number
  totalMemory: number
  machineId: string
  hostname: string
  osVersion: string
  osRelease: string
}

export class SimplifiedAuthService {
  private static instance: SimplifiedAuthService | null = null
  private tokenFilePath: string
  private currentToken: AuthToken | null = null
  private sessionId: string
  private isAppClosing: boolean = false

  private constructor() {
    // Store token in user data directory with app-specific name
    this.tokenFilePath = path.join(app.getPath('userData'), 'auth_token.enc')

    // Generate unique session ID for this app instance
    this.sessionId = this.generateSessionId()

    // Set up cleanup on app events
    this.setupCleanupHandlers()
  }

  /**
   * Get singleton instance
   */
  static getInstance(): SimplifiedAuthService {
    if (!SimplifiedAuthService.instance) {
      SimplifiedAuthService.instance = new SimplifiedAuthService()
    }
    return SimplifiedAuthService.instance
  }

  /**
   * Generate comprehensive device fingerprint with hardware-level identifiers
   */
  generateDeviceFingerprint(): { fingerprint: string; hardwareSpecs: DeviceHardwareSpecs } {
    try {
      // Collect hardware-level identifiers that remain constant across restarts
      const hardwareSpecs: DeviceHardwareSpecs = {
        platform: os.platform(),
        arch: os.arch(),
        cpuModel: os.cpus()[0]?.model || 'unknown',
        cpuCores: os.cpus().length,
        totalMemory: os.totalmem(),
        machineId: this.getMachineId(),
        hostname: os.hostname(),
        osVersion: os.version(),
        osRelease: os.release()
      }

      // Create fingerprint from hardware specs (excluding network interfaces and volatile data)
      const fingerprintData = {
        platform: hardwareSpecs.platform,
        arch: hardwareSpecs.arch,
        cpuModel: hardwareSpecs.cpuModel,
        cpuCores: hardwareSpecs.cpuCores,
        totalMemory: hardwareSpecs.totalMemory,
        machineId: hardwareSpecs.machineId,
        // Note: Excluding hostname, IP addresses, and network interfaces
        // as they can change with network connectivity
        osVersion: hardwareSpecs.osVersion
      }

      const fingerprintString = JSON.stringify(fingerprintData, Object.keys(fingerprintData).sort())
      const fingerprint = createHash('sha256').update(fingerprintString).digest('hex')

      console.log('[SimplifiedAuth] Generated device fingerprint:', fingerprint.substring(0, 16) + '...')
      
      return { fingerprint, hardwareSpecs }
    } catch (error) {
      console.error('[SimplifiedAuth] Error generating device fingerprint:', error)
      
      // Fallback fingerprint
      const fallbackData = {
        platform: os.platform(),
        arch: os.arch(),
        timestamp: Date.now()
      }
      const fallbackFingerprint = createHash('sha256').update(JSON.stringify(fallbackData)).digest('hex')
      
      return {
        fingerprint: fallbackFingerprint,
        hardwareSpecs: {
          platform: os.platform(),
          arch: os.arch(),
          cpuModel: 'unknown',
          cpuCores: 0,
          totalMemory: 0,
          machineId: 'unknown',
          hostname: 'unknown',
          osVersion: 'unknown',
          osRelease: 'unknown'
        }
      }
    }
  }

  /**
   * Get machine ID (hardware-level identifier)
   */
  private getMachineId(): string {
    try {
      // Try to get a stable machine identifier
      const networkInterfaces = os.networkInterfaces()
      const macAddresses: string[] = []
      
      Object.values(networkInterfaces).forEach(interfaces => {
        interfaces?.forEach(iface => {
          if (iface.mac && iface.mac !== '00:00:00:00:00:00') {
            macAddresses.push(iface.mac)
          }
        })
      })
      
      // Use the first non-zero MAC address as part of machine ID
      const primaryMac = macAddresses.sort()[0] || 'no-mac'
      
      // Combine with other hardware identifiers
      const machineData = {
        mac: primaryMac,
        platform: os.platform(),
        arch: os.arch(),
        cpuModel: os.cpus()[0]?.model || 'unknown'
      }
      
      return createHash('sha256').update(JSON.stringify(machineData)).digest('hex').substring(0, 16)
    } catch (error) {
      console.error('[SimplifiedAuth] Error getting machine ID:', error)
      return 'fallback-machine-id'
    }
  }

  /**
   * Store authentication token securely
   */
  async storeToken(token: string, refreshToken?: string, userEmail?: string): Promise<void> {
    try {
      if (!safeStorage.isEncryptionAvailable()) {
        throw new Error('Encryption not available')
      }

      const deviceInfo = this.generateDeviceFingerprint()
      
      const authToken: AuthToken = {
        access_token: token,
        refresh_token: refreshToken,
        expires_at: Date.now() + (365 * 24 * 60 * 60 * 1000), // 1 year from now (effectively until app closes)
        user_email: userEmail || 'unknown',
        device_fingerprint: deviceInfo.fingerprint,
        session_id: this.sessionId,
        created_at: Date.now()
      }

      const tokenData = JSON.stringify(authToken)
      const encrypted = safeStorage.encryptString(tokenData)
      
      fs.writeFileSync(this.tokenFilePath, encrypted)
      this.currentToken = authToken
      
      console.log('[SimplifiedAuth] Token stored securely')
    } catch (error) {
      console.error('[SimplifiedAuth] Failed to store token:', error)
      throw error
    }
  }

  /**
   * Retrieve authentication token
   */
  async getToken(): Promise<string | null> {
    try {
      // Check in-memory token first
      if (this.currentToken) {
        // Validate session ID (token is only valid for current app session)
        if (this.currentToken.session_id === this.sessionId && Date.now() < this.currentToken.expires_at) {
          return this.currentToken.access_token
        } else {
          // Token expired or from different session, clear it
          console.log('[SimplifiedAuth] Token invalid (expired or different session), clearing')
          this.currentToken = null
          this.clearToken()
          return null
        }
      }

      // Check file-based token
      if (!fs.existsSync(this.tokenFilePath)) {
        return null
      }

      if (!safeStorage.isEncryptionAvailable()) {
        throw new Error('Encryption not available')
      }

      const encrypted = fs.readFileSync(this.tokenFilePath)
      const tokenData = safeStorage.decryptString(encrypted)
      const authToken: AuthToken = JSON.parse(tokenData)

      // Check if token is expired
      if (Date.now() >= authToken.expires_at) {
        console.log('[SimplifiedAuth] Token expired, clearing')
        this.clearToken()
        return null
      }

      // Verify session ID (token should only be valid for current app session)
      if (authToken.session_id !== this.sessionId) {
        console.log('[SimplifiedAuth] Token from different session, clearing')
        this.clearToken()
        return null
      }

      // Verify device fingerprint
      const currentDeviceInfo = this.generateDeviceFingerprint()
      if (authToken.device_fingerprint !== currentDeviceInfo.fingerprint) {
        console.warn('[SimplifiedAuth] Device fingerprint mismatch, clearing token')
        this.clearToken()
        return null
      }

      this.currentToken = authToken
      return authToken.access_token
    } catch (error) {
      console.error('[SimplifiedAuth] Failed to get token:', error)
      this.clearToken() // Clear potentially corrupted token
      return null
    }
  }

  /**
   * Check if valid token exists
   */
  async hasValidToken(): Promise<boolean> {
    const token = await this.getToken()
    return token !== null
  }

  /**
   * Clear authentication token
   */
  clearToken(): void {
    try {
      this.currentToken = null

      if (fs.existsSync(this.tokenFilePath)) {
        fs.unlinkSync(this.tokenFilePath)
        console.log('[SimplifiedAuth] Token file deleted')
      }
    } catch (error) {
      console.error('[SimplifiedAuth] Failed to clear token:', error)
    }
  }

  /**
   * Logout user and clear all authentication data
   */
  async logout(): Promise<void> {
    try {
      console.log('[SimplifiedAuth] User logout initiated')
      this.clearToken()

      // Generate new session ID to invalidate any cached tokens
      this.sessionId = this.generateSessionId()

      console.log('[SimplifiedAuth] Logout completed successfully')
    } catch (error) {
      console.error('[SimplifiedAuth] Failed to logout:', error)
      throw error
    }
  }

  /**
   * Generate unique session ID for this app instance
   */
  private generateSessionId(): string {
    const timestamp = Date.now()
    const random = Math.random().toString(36).substring(2)
    return `session_${timestamp}_${random}`
  }

  /**
   * Setup cleanup handlers for app events
   */
  private setupCleanupHandlers(): void {
    // Clear token on app quit
    app.on('before-quit', () => {
      console.log('[SimplifiedAuth] App quitting, clearing authentication token')
      this.isAppClosing = true
      this.clearToken()
    })

    // Clear token on window close (if it's the last window)
    app.on('window-all-closed', () => {
      if (process.platform !== 'darwin') {
        console.log('[SimplifiedAuth] All windows closed, clearing authentication token')
        this.isAppClosing = true
        this.clearToken()
      }
    })

    // Clear token on app termination
    process.on('SIGINT', () => {
      console.log('[SimplifiedAuth] SIGINT received, clearing authentication token')
      this.isAppClosing = true
      this.clearToken()
      process.exit(0)
    })

    process.on('SIGTERM', () => {
      console.log('[SimplifiedAuth] SIGTERM received, clearing authentication token')
      this.isAppClosing = true
      this.clearToken()
      process.exit(0)
    })

    // Handle unexpected app crashes
    process.on('uncaughtException', (error) => {
      console.error('[SimplifiedAuth] Uncaught exception, clearing token:', error)
      this.isAppClosing = true
      this.clearToken()
      process.exit(1)
    })

    process.on('unhandledRejection', (reason) => {
      console.error('[SimplifiedAuth] Unhandled rejection, clearing token:', reason)
      this.isAppClosing = true
      this.clearToken()
      process.exit(1)
    })
  }

  /**
   * Get current user email from token
   */
  async getCurrentUserEmail(): Promise<string | null> {
    try {
      const token = await this.getToken()
      if (!token || !this.currentToken) {
        return null
      }
      return this.currentToken.user_email
    } catch (error) {
      console.error('[SimplifiedAuth] Failed to get current user email:', error)
      return null
    }
  }

  /**
   * Get device information for debugging
   */
  getDeviceInfo(): { fingerprint: string; hardwareSpecs: DeviceHardwareSpecs } {
    return this.generateDeviceFingerprint()
  }

  /**
   * Check if app is currently closing
   */
  getIsAppClosing(): boolean {
    return this.isAppClosing
  }

  /**
   * Get current session ID
   */
  getSessionId(): string {
    return this.sessionId
  }

  /**
   * Get token information for debugging (without exposing actual token)
   */
  getTokenInfo(): { hasToken: boolean; userEmail?: string; sessionId?: string; createdAt?: number } | null {
    if (!this.currentToken) {
      return null
    }

    return {
      hasToken: true,
      userEmail: this.currentToken.user_email,
      sessionId: this.currentToken.session_id,
      createdAt: this.currentToken.created_at
    }
  }
}

// Export singleton instance
export const simplifiedAuthService = SimplifiedAuthService.getInstance()

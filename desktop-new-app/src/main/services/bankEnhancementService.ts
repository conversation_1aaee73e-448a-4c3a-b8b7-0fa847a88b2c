import { mainIFSCService } from './ifscService'
import { TransactionData, ComplaintGraphData, GraphNode } from '../../shared/api'

interface EnhancedBankData {
  original_bank_name?: string
  enhanced_bank_name: string
  bank: string
  branch: string
  address: string
  city: string
  state: string
  district: string
  ifsc: string
  enhancement_status: 'success' | 'no_ifsc' | 'invalid_ifsc' | 'api_error'
  enhanced_at: string
}

/**
 * Service for automatically enhancing bank data during complaint upload
 */
class BankEnhancementService {
  private cache = new Map<string, EnhancedBankData>()

  /**
   * Enhance bank data for a complete complaint
   */
  async enhanceComplaintBankData(complaintData: {
    layer_transactions?: Record<string, unknown> | null
    graph_data?: ComplaintGraphData | null
    bank_notice_data?: Record<string, unknown> | null
  }): Promise<{
    layer_transactions?: Record<string, unknown> | null
    graph_data?: ComplaintGraphData | null
    bank_notice_data?: Record<string, unknown> | null
  }> {
    console.log('[Bank Enhancement] Starting bank data enhancement for complaint')

    try {
      const enhanced = { ...complaintData }

      // Enhance layer_transactions
      if (enhanced.layer_transactions) {
        try {
          enhanced.layer_transactions = await this.enhanceLayerTransactions(
            enhanced.layer_transactions
          )
        } catch (error) {
          console.error('[Bank Enhancement] Error enhancing layer_transactions:', error)
          // Continue with original data if enhancement fails
        }
      }

      // Enhance graph_data
      if (enhanced.graph_data) {
        try {
          enhanced.graph_data = await this.enhanceGraphData(enhanced.graph_data)
        } catch (error) {
          console.error('[Bank Enhancement] Error enhancing graph_data:', error)
          // Continue with original data if enhancement fails
        }
      }

      // Enhance bank_notice_data
      if (enhanced.bank_notice_data) {
        try {
          enhanced.bank_notice_data = await this.enhanceBankNoticeData(enhanced.bank_notice_data)
        } catch (error) {
          console.error('[Bank Enhancement] Error enhancing bank_notice_data:', error)
          // Continue with original data if enhancement fails
        }
      }

      console.log('[Bank Enhancement] Bank data enhancement completed')
      return enhanced
    } catch (error) {
      console.error('[Bank Enhancement] Critical error during bank data enhancement:', error)
      // Return original data if there's a critical error
      return complaintData
    }
  }

  /**
   * Enhance layer transactions with bank data
   */
  private async enhanceLayerTransactions(
    layerTransactions: Record<string, unknown>
  ): Promise<Record<string, unknown>> {
    const enhanced = { ...layerTransactions }

    for (const [layerKey, layerData] of Object.entries(enhanced)) {
      if (Array.isArray(layerData)) {
        enhanced[layerKey] = await Promise.all(
          layerData.map(async (transaction) => {
            if (this.isTransactionData(transaction)) {
              return await this.enhanceTransactionBankData(transaction)
            }
            return transaction
          })
        )
      }
    }

    return enhanced
  }

  /**
   * Enhance graph data with bank information
   */
  private async enhanceGraphData(graphData: ComplaintGraphData): Promise<ComplaintGraphData> {
    const enhanced = { ...graphData }

    // Ensure nodes array exists and is valid
    if (!enhanced.nodes || !Array.isArray(enhanced.nodes)) {
      console.log('[Bank Enhancement] No valid nodes array found in graph data')
      return enhanced
    }

    // Enhance nodes that contain transaction data
    enhanced.nodes = await Promise.all(
      enhanced.nodes.map(async (node) => {
        // Skip null or undefined nodes
        if (!node) {
          console.log('[Bank Enhancement] Skipping null/undefined node')
          return node
        }
        return await this.enhanceGraphNode(node)
      })
    )

    // Enhance transactions array if present
    if (enhanced.transactions && Array.isArray(enhanced.transactions)) {
      enhanced.transactions = await Promise.all(
        enhanced.transactions.map(async (transaction) => {
          return await this.enhanceTransactionBankData(transaction)
        })
      )
    }

    return enhanced
  }

  /**
   * Enhance bank notice data
   */
  private async enhanceBankNoticeData(
    bankNoticeData: Record<string, unknown>
  ): Promise<Record<string, unknown>> {
    const enhanced = { ...bankNoticeData }

    // Enhance suspects data if present
    if (enhanced.suspects && Array.isArray(enhanced.suspects)) {
      enhanced.suspects = await Promise.all(
        enhanced.suspects.map(async (suspect: Record<string, unknown>) => {
          if (suspect.ifsc_code && typeof suspect.ifsc_code === 'string') {
            const enhancedData = await this.enhanceBankDataByIFSC(
              (suspect.bank_name as string) || '',
              suspect.ifsc_code
            )
            return {
              ...suspect,
              bank_name: enhancedData.enhanced_bank_name,
              enhanced_bank_data: enhancedData
            }
          }
          return suspect
        })
      )
    }

    return enhanced
  }

  /**
   * Enhance a single transaction with bank data
   */
  private async enhanceTransactionBankData(transaction: TransactionData): Promise<TransactionData> {
    const enhanced = { ...transaction }

    // Enhance receiver bank data
    if (transaction.receiver_ifsc) {
      const enhancedData = await this.enhanceBankDataByIFSC(
        transaction.receiver_bank || '',
        transaction.receiver_ifsc
      )
      enhanced.receiver_bank = enhancedData.enhanced_bank_name
      ;(
        enhanced as TransactionData & { enhanced_receiver_bank_data: EnhancedBankData }
      ).enhanced_receiver_bank_data = enhancedData
    }

    // Enhance sender bank data if sender_ifsc exists
    const transactionWithSender = transaction as TransactionData & { sender_ifsc?: string }
    if (transactionWithSender.sender_ifsc) {
      const enhancedData = await this.enhanceBankDataByIFSC(
        transaction.sender_bank || '',
        transactionWithSender.sender_ifsc
      )
      enhanced.sender_bank = enhancedData.enhanced_bank_name
      ;(
        enhanced as TransactionData & { enhanced_sender_bank_data: EnhancedBankData }
      ).enhanced_sender_bank_data = enhancedData
    }

    return enhanced
  }

  /**
   * Enhance a graph node with bank data
   */
  private async enhanceGraphNode(node: GraphNode): Promise<GraphNode> {
    const enhanced = { ...node }

    // Check if node has data property and it's not null/undefined
    if (!enhanced.data || typeof enhanced.data !== 'object') {
      console.log(
        '[Bank Enhancement] Skipping node enhancement - node has no data property:',
        enhanced.id
      )
      return enhanced
    }

    // Check if node data contains IFSC codes and bank information
    const nodeData = enhanced.data

    // Look for various IFSC field names
    const ifscFields = ['ifsc', 'receiver_ifsc', 'sender_ifsc', 'ifsc_code']
    const bankFields = ['bank', 'bank_name', 'receiver_bank', 'sender_bank']

    try {
      for (const ifscField of ifscFields) {
        // Safely check if the IFSC field exists and is a valid string
        if (nodeData[ifscField] && typeof nodeData[ifscField] === 'string') {
          const ifscCode = nodeData[ifscField] as string

          // Skip empty or whitespace-only IFSC codes
          if (!ifscCode.trim()) {
            console.log(
              `[Bank Enhancement] Skipping empty IFSC field '${ifscField}' in node:`,
              enhanced.id
            )
            continue
          }

          // Find corresponding bank field
          let bankField = bankFields.find((field) => nodeData[field])
          if (!bankField) {
            bankField = 'bank_name' // Default field name
          }

          const originalBankName = (nodeData[bankField] as string) || ''

          console.log(`[Bank Enhancement] Enhancing node ${enhanced.id} with IFSC ${ifscCode}`)
          const enhancedData = await this.enhanceBankDataByIFSC(originalBankName, ifscCode)

          // Update the bank field with enhanced name
          enhanced.data = {
            ...enhanced.data,
            [bankField]: enhancedData.enhanced_bank_name,
            [`enhanced_${ifscField}_data`]: enhancedData
          }

          console.log(
            `[Bank Enhancement] Successfully enhanced node ${enhanced.id} for IFSC ${ifscCode}`
          )
        }
      }
    } catch (error) {
      console.error(`[Bank Enhancement] Error enhancing graph node ${enhanced.id}:`, error)
      // Continue processing other nodes even if this one fails
    }

    return enhanced
  }

  /**
   * Enhance bank data using IFSC code
   */
  private async enhanceBankDataByIFSC(
    originalBankName: string,
    ifscCode: string
  ): Promise<EnhancedBankData> {
    // Check cache first
    const cacheKey = ifscCode.trim().toUpperCase()
    if (this.cache.has(cacheKey)) {
      const cached = this.cache.get(cacheKey)!
      return {
        ...cached,
        original_bank_name: originalBankName
      }
    }

    const enhancedData: EnhancedBankData = {
      original_bank_name: originalBankName,
      enhanced_bank_name: originalBankName, // Fallback to original
      bank: '',
      branch: '',
      address: '',
      city: '',
      state: '',
      district: '',
      ifsc: ifscCode,
      enhancement_status: 'no_ifsc',
      enhanced_at: new Date().toISOString()
    }

    if (!ifscCode || ifscCode.trim().length === 0) {
      enhancedData.enhancement_status = 'no_ifsc'
      enhancedData.enhanced_bank_name = originalBankName || 'Unknown Bank'
      this.cache.set(cacheKey, enhancedData)
      return enhancedData
    }

    try {
      const ifscDetails = await mainIFSCService.fetchIFSCDetails(ifscCode)

      if (ifscDetails) {
        // Create enhanced bank name combining multiple fields
        const bankParts = [
          ifscDetails.BANK,
          ifscDetails.BRANCH,
          ifscDetails.CITY,
          ifscDetails.STATE
        ].filter((part) => part && part.trim().length > 0)

        enhancedData.enhanced_bank_name = bankParts.join(' - ')
        enhancedData.bank = ifscDetails.BANK || ''
        enhancedData.branch = ifscDetails.BRANCH || ''
        enhancedData.address = ifscDetails.ADDRESS || ''
        enhancedData.city = ifscDetails.CITY || ''
        enhancedData.state = ifscDetails.STATE || ''
        enhancedData.district = ifscDetails.DISTRICT || ''
        enhancedData.enhancement_status = 'success'

        console.log(
          `[Bank Enhancement] Enhanced bank data for IFSC ${ifscCode}: ${enhancedData.enhanced_bank_name}`
        )
      } else {
        enhancedData.enhancement_status = 'invalid_ifsc'
        enhancedData.enhanced_bank_name = originalBankName || 'Invalid IFSC'
        console.log(`[Bank Enhancement] Invalid IFSC code: ${ifscCode}`)
      }
    } catch (error) {
      console.error(`[Bank Enhancement] Error enhancing bank data for IFSC ${ifscCode}:`, error)
      enhancedData.enhancement_status = 'api_error'
      enhancedData.enhanced_bank_name = originalBankName || 'API Error'
    }

    // Cache the result
    this.cache.set(cacheKey, enhancedData)
    return enhancedData
  }

  /**
   * Type guard to check if object is TransactionData
   */
  private isTransactionData(obj: unknown): obj is TransactionData {
    return (
      obj !== null &&
      typeof obj === 'object' &&
      'receiver_ifsc' in obj &&
      'receiver_bank' in obj &&
      typeof (obj as Record<string, unknown>).receiver_ifsc === 'string' &&
      typeof (obj as Record<string, unknown>).receiver_bank === 'string'
    )
  }

  /**
   * Clear the cache
   */
  clearCache(): void {
    this.cache.clear()
  }

  /**
   * Get cache statistics
   */
  getCacheStats(): { size: number; keys: string[] } {
    return {
      size: this.cache.size,
      keys: Array.from(this.cache.keys())
    }
  }
}

// Export singleton instance
export const bankEnhancementService = new BankEnhancementService()

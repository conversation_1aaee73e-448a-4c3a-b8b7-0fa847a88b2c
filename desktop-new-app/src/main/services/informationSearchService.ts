import Database from 'better-sqlite3'
import path from 'path'
import { app } from 'electron'
import * as fs from 'fs'

export interface InformationRecord {
  id: number
  s_no: string
  bank: string
  name_of_officer: string
  designation: string
  mobile_no: string
  email: string
  last_login: string
  created_at: string
  updated_at: string
}

export interface SearchFilters {
  searchTerm?: string
  bank?: string
  designation?: string
}

export interface PaginationOptions {
  page: number
  limit: number
}

export interface SearchResult {
  data: InformationRecord[]
  total: number
  page: number
  limit: number
  totalPages: number
}

class InformationSearchService {
  private db: Database.Database | null = null
  private dbPath: string

  constructor() {
    // Use the converted SQLite database
    // In development, use the resources directory, in production use app resources
    if (app.isPackaged) {
      this.dbPath = path.join(process.resourcesPath, 'information_search.db')
    } else {
      // For development, use a more reliable path resolution
      this.dbPath = path.join(__dirname, '..', '..', '..', 'resources', 'information_search.db')
    }

    console.log('[Information Search] Database path:', this.dbPath)
    console.log('[Information Search] App is packaged:', app.isPackaged)
    console.log('[Information Search] __dirname:', __dirname)
  }

  /**
   * Initialize database connection
   */
  private initDatabase(): Database.Database {
    if (this.db) {
      return this.db
    }

    // Check if database file exists
    if (!fs.existsSync(this.dbPath)) {
      console.error('[Information Search] Database file missing:', this.dbPath)

      // Try to create the database file if it doesn't exist
      try {
        this.createDatabaseFile()
      } catch (createError) {
        console.error('[Information Search] Failed to create database file:', createError)
        throw new Error(`Information search database file not found at: ${this.dbPath}`)
      }
    }

    try {
      this.db = new Database(this.dbPath)
      console.log('[Information Search] Connected to database successfully')
      return this.db
    } catch (err) {
      console.error('[Information Search] Failed to connect to database:', err)
      console.error('[Information Search] Database path:', this.dbPath)
      throw new Error(`Database connection failed: ${err} (Path: ${this.dbPath})`)
    }
  }

  /**
   * Create database file and initialize schema if it doesn't exist
   */
  private createDatabaseFile(): void {
    // Ensure the directory exists
    const dbDir = path.dirname(this.dbPath)
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true })
      console.log('[Information Search] Created database directory:', dbDir)
    }

    try {
      // Create the database and initialize schema
      const db = new Database(this.dbPath)

      // Create the information_data table
      const createTableSQL = `
        CREATE TABLE IF NOT EXISTS information_data (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          s_no TEXT,
          bank TEXT,
          name_of_officer TEXT,
          designation TEXT,
          mobile_no TEXT,
          email TEXT,
          last_login TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
        )
      `

      db.exec(createTableSQL)
      console.log('[Information Search] Database file created and initialized')
      db.close()
    } catch (err) {
      console.error('[Information Search] Failed to create database file:', err)
      throw err
    }
  }

  /**
   * Search information records with filters and pagination
   */
  searchRecords(
    filters: SearchFilters = {},
    pagination: PaginationOptions = { page: 1, limit: 50 }
  ): SearchResult {
    const db = this.initDatabase()

    const { searchTerm, bank, designation } = filters
    const { page, limit } = pagination
    const offset = (page - 1) * limit

    // Build WHERE clause
    const conditions: string[] = []
    const params: (string | number)[] = []

    if (searchTerm && searchTerm.trim()) {
      conditions.push(`(
        name_of_officer LIKE ? OR
        bank LIKE ? OR
        designation LIKE ? OR
        email LIKE ? OR
        mobile_no LIKE ?
      )`)
      const searchPattern = `%${searchTerm.trim()}%`
      params.push(searchPattern, searchPattern, searchPattern, searchPattern, searchPattern)
    }

    if (bank && bank.trim()) {
      conditions.push('bank LIKE ?')
      params.push(`%${bank.trim()}%`)
    }

    if (designation && designation.trim()) {
      conditions.push('designation LIKE ?')
      params.push(`%${designation.trim()}%`)
    }

    const whereClause = conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : ''

    // Get total count
    const countQuery = `SELECT COUNT(*) as total FROM information_data ${whereClause}`
    const countStmt = db.prepare(countQuery)
    const totalResult = countStmt.get(params) as { total: number }
    const total = totalResult.total

    // Get paginated data
    const dataQuery = `
      SELECT * FROM information_data
      ${whereClause}
      ORDER BY bank, name_of_officer
      LIMIT ? OFFSET ?
    `
    const dataParams = [...params, limit, offset]
    const dataStmt = db.prepare(dataQuery)
    const data = dataStmt.all(dataParams) as InformationRecord[]

    return {
      data,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit)
    }
  }

  /**
   * Get a single record by ID
   */
  getRecordById(id: number): InformationRecord | null {
    const db = this.initDatabase()
    const stmt = db.prepare('SELECT * FROM information_data WHERE id = ?')
    const result = stmt.get(id) as InformationRecord | undefined
    return result || null
  }

  /**
   * Create a new record
   */
  createRecord(record: Omit<InformationRecord, 'id' | 'created_at' | 'updated_at'>): number {
    const db = this.initDatabase()

    const insertQuery = `
      INSERT INTO information_data (
        s_no, bank, name_of_officer, designation, mobile_no, email, last_login
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `

    const stmt = db.prepare(insertQuery)
    const result = stmt.run(
      record.s_no,
      record.bank,
      record.name_of_officer,
      record.designation,
      record.mobile_no,
      record.email,
      record.last_login
    )

    return result.lastInsertRowid as number
  }

  /**
   * Update an existing record
   */
  updateRecord(
    id: number,
    record: Partial<Omit<InformationRecord, 'id' | 'created_at' | 'updated_at'>>
  ): boolean {
    const db = this.initDatabase()

    const fields = Object.keys(record).filter(
      (key) => record[key as keyof typeof record] !== undefined
    )
    const setClause = fields.map((field) => `${field} = ?`).join(', ')
    const values = fields.map((field) => record[field as keyof typeof record])

    const updateQuery = `
      UPDATE information_data
      SET ${setClause}, updated_at = CURRENT_TIMESTAMP
      WHERE id = ?
    `

    const stmt = db.prepare(updateQuery)
    const result = stmt.run(...values, id)
    return result.changes > 0
  }

  /**
   * Delete a record
   */
  deleteRecord(id: number): boolean {
    const db = this.initDatabase()
    const stmt = db.prepare('DELETE FROM information_data WHERE id = ?')
    const result = stmt.run(id)
    return result.changes > 0
  }

  /**
   * Get unique banks for filter dropdown
   */
  getUniqueBanks(): string[] {
    const db = this.initDatabase()
    const stmt = db.prepare(
      "SELECT DISTINCT bank FROM information_data WHERE bank IS NOT NULL AND bank != '' ORDER BY bank"
    )
    const rows = stmt.all() as { bank: string }[]
    return rows.map((row) => row.bank)
  }

  /**
   * Get unique designations for filter dropdown
   */
  getUniqueDesignations(): string[] {
    const db = this.initDatabase()
    const stmt = db.prepare(
      "SELECT DISTINCT designation FROM information_data WHERE designation IS NOT NULL AND designation != '' ORDER BY designation"
    )
    const rows = stmt.all() as { designation: string }[]
    return rows.map((row) => row.designation)
  }

  /**
   * Close database connection
   */
  close(): void {
    if (this.db) {
      this.db.close()
      this.db = null
      console.log('[Information Search] Database connection closed')
    }
  }
}

// Export singleton instance
export const informationSearchService = new InformationSearchService()

/**
 * Unified Data Structure for Fraud Analysis App
 * 
 * This file defines the new unified data structure that combines layer_transactions
 * and graph_data into a single source of truth, eliminating duplication and ensuring
 * consistency between graph visualization and table views.
 */

// Core transaction data - essential fields used by both views
export interface CoreTransactionData {
  // Primary identifiers
  id: string // Unique transaction identifier
  txn_id?: string
  transaction_id?: string
  
  // Transaction details
  fraud_type: string
  txn_type: string
  amount: string
  date: string
  reference: string
  layer: number
  
  // Account information
  sender_account: string
  sender_transaction_id: string
  sender_bank: string
  receiver_account: string
  receiver_transaction_id: string
  receiver_bank: string
  receiver_ifsc: string
  receiver_info: string
  
  // Enhanced bank data (from IFSC enrichment)
  enhanced_receiver_bank_data?: EnhancedBankData
  enhanced_sender_bank_data?: EnhancedBankData
  
  // Metadata
  created_at: string
  updated_at: string
}

// Enhanced bank data structure
export interface EnhancedBankData {
  original_bank_name?: string
  enhanced_bank_name: string
  bank: string
  branch: string
  address: string
  city: string
  state: string
  district: string
  ifsc: string
  enhancement_status: 'success' | 'no_ifsc' | 'invalid_ifsc' | 'api_error'
  enhanced_at: string
}

// Graph-specific data for visualization
export interface GraphNodeData {
  // Node positioning and type
  position: { x: number; y: number }
  node_type: 'sender_account' | 'receiver_account' | 'metadata'
  
  // Visual properties
  label: string
  is_sender_node: boolean
  is_main_transaction: boolean
  is_victim: boolean
  
  // Consolidation data for multiple transactions to same account
  is_consolidated: boolean
  consolidated_transactions?: CoreTransactionData[]
  transaction_count: number
  total_amount: string
  
  // Node state
  is_collapsed: boolean
  has_children: boolean
  visible_fields: Set<string>
}

// Edge data for graph connections
export interface GraphEdgeData {
  id: string
  source_transaction_id: string
  target_transaction_id: string
  edge_type: 'transaction_flow' | 'account_link'
  label?: string
  animated?: boolean
  style?: Record<string, unknown>
}

// Unified transaction structure
export interface UnifiedTransaction {
  // Core transaction data
  core: CoreTransactionData
  
  // Graph-specific data (only present for transactions that appear in graph)
  graph?: GraphNodeData
  
  // Bank notice inclusion flag
  include_in_bank_notice: boolean
}

// Layer organization structure
export interface LayerData {
  layer_number: number
  transactions: UnifiedTransaction[]
  total_amount: number
  transaction_count: number
}

// Unified complaint data structure
export interface UnifiedComplaintData {
  // Basic complaint info
  id: string
  title: string
  description: string
  file_name?: string
  fraud_type?: string
  extraction_type?: string
  max_layer: number
  status: string
  created_at: string
  updated_at: string
  
  // Metadata
  metadata: Record<string, unknown>
  extraction_info: Record<string, unknown>
  
  // Unified transaction data organized by layers
  layers: Record<string, LayerData>
  
  // Graph edges (separate from transactions)
  graph_edges: GraphEdgeData[]
  
  // Bank notice data (auto-generated from transactions)
  bank_notice_data: BankNoticeData
}

// Bank notice data structure
export interface BankNoticeData {
  banks: Record<string, BankNoticeEntry>
  total_banks: number
  total_amount: number
  last_updated: string
}

export interface BankNoticeEntry {
  bank_name: string
  enhanced_bank_name?: string
  transactions: CoreTransactionData[]
  total_amount: number
  transaction_count: number
  enhanced_bank_data?: EnhancedBankData
}

// Data transformation interfaces for service layer
export interface ReactFlowGraphData {
  nodes: ReactFlowNode[]
  edges: ReactFlowEdge[]
  metadata: Record<string, unknown>
  max_layer: number
}

export interface ReactFlowNode {
  id: string
  type: string
  position: { x: number; y: number }
  data: Record<string, unknown>
}

export interface ReactFlowEdge {
  id: string
  source: string
  target: string
  type?: string
  data?: Record<string, unknown>
}

// Table data structure for complaint details view
export interface TransactionTableData {
  transactions: TransactionTableRow[]
  total_count: number
  layers: string[]
}

export interface TransactionTableRow {
  id: string
  layer: number
  fraud_type: string
  txn_type: string
  sender_account: string
  sender_bank: string
  receiver_account: string
  receiver_bank: string
  amount: string
  date: string
  reference: string
  receiver_ifsc: string
  receiver_info: string
  sender_transaction_id: string
  receiver_transaction_id: string
  sr_no: string
  include_in_bank_notice: boolean
}

// Migration interface for backward compatibility
export interface LegacyComplaintData {
  layer_transactions?: Record<string, unknown>
  graph_data?: {
    nodes: unknown[]
    edges: unknown[]
    transactions: unknown[]
    metadata: Record<string, unknown>
    max_layer: number
  }
  bank_notice_data?: Record<string, unknown>
}

// Service interfaces
export interface DataTransformationService {
  // Convert unified data to ReactFlow format
  toReactFlowData(unifiedData: UnifiedComplaintData): ReactFlowGraphData
  
  // Convert unified data to table format
  toTableData(unifiedData: UnifiedComplaintData): TransactionTableData
  
  // Update unified data from table changes
  updateFromTableData(
    unifiedData: UnifiedComplaintData,
    tableChanges: Partial<TransactionTableRow>,
    transactionId: string
  ): UnifiedComplaintData
  
  // Update unified data from graph changes
  updateFromGraphData(
    unifiedData: UnifiedComplaintData,
    nodeChanges: Partial<ReactFlowNode>,
    nodeId: string
  ): UnifiedComplaintData
  
  // Migrate legacy data to unified format
  migrateLegacyData(legacyData: LegacyComplaintData): UnifiedComplaintData
  
  // Auto-update bank notice data
  updateBankNoticeData(unifiedData: UnifiedComplaintData): UnifiedComplaintData
}

// Event types for real-time synchronization
export interface DataSyncEvent {
  type: 'transaction_updated' | 'transaction_added' | 'transaction_deleted' | 'bank_notice_updated'
  complaint_id: string
  transaction_id?: string
  data?: unknown
  timestamp: string
}

export interface DataSyncService {
  // Subscribe to data changes
  subscribe(callback: (event: DataSyncEvent) => void): () => void
  
  // Emit data change events
  emit(event: DataSyncEvent): void
  
  // Sync data across components
  syncData(complaintId: string): Promise<void>
}

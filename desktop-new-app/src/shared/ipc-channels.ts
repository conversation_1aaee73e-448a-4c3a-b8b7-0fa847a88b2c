// IPC Channel Constants
export const AUTH_LOGIN = 'auth:login'
export const AUTH_SIGNUP = 'auth:signup'
export const AUTH_LOGOUT = 'auth:logout'
export const AUTH_GET_SESSION = 'auth:getSession'
export const AUTH_HAS_TOKEN = 'auth:hasToken'
export const AUTH_VERIFY_EMAIL = 'auth:verifyEmail'
export const AUTH_RESEND_OTP = 'auth:resendOtp'
export const AUTH_SET_TOKEN = 'auth:setToken'
export const AUTH_GET_TOKEN = 'auth:getToken'
export const AUTH_CLEAR_TOKEN = 'auth:clearToken'

// Local Database Operations
export const DB_STORE_COMPLAINT = 'db:storeComplaint'
export const DB_GET_COMPLAINTS = 'db:getComplaints'
export const DB_GET_COMPLAINT = 'db:getComplaint'
export const DB_DELETE_COMPLAINT = 'db:deleteComplaint'
export const DB_UPDATE_COMPLAINT = 'db:updateComplaint'
export const DB_BACKUP_DATABASE = 'db:backupDatabase'
export const DB_DELETE_COMPLAINTS_BY_MONTH = 'db:deleteComplaintsByMonth'

// API Calls
export const API_UPLOAD_HTML = 'api:uploadHtml'

// Graph Data Operations
export const DB_GET_GRAPH_DATA = 'db:getGraphData'
export const DB_UPDATE_GRAPH_NODE = 'db:updateGraphNode'
export const DB_ADD_GRAPH_NODE = 'db:addGraphNode'
export const DB_DELETE_GRAPH_NODE = 'db:deleteGraphNode'
export const DB_ADD_GRAPH_EDGE = 'db:addGraphEdge'
export const DB_DELETE_GRAPH_EDGE = 'db:deleteGraphEdge'

// IFSC Service channels
export const IFSC_VALIDATE = 'ifsc:validate'
export const IFSC_FETCH_DETAILS = 'ifsc:fetch-details'

// Template Management
export const TEMPLATE_STORE = 'template:store'
export const TEMPLATE_GET = 'template:get'
export const TEMPLATE_DELETE = 'template:delete'

const XLSX = require('xlsx')
const sqlite3 = require('sqlite3').verbose()
const path = require('path')
const fs = require('fs')

/**
 * Convert XLSX file to SQLite database for information search
 */
async function convertXlsxToSqlite() {
  const xlsxPath = path.join(__dirname, '../resources/information search page data.xlsx')
  const sqlitePath = path.join(__dirname, '../resources/information_search.db')

  console.log('Converting XLSX to SQLite...')
  console.log('Input file:', xlsxPath)
  console.log('Output file:', sqlitePath)

  try {
    // Check if XLSX file exists
    if (!fs.existsSync(xlsxPath)) {
      throw new Error(`XLSX file not found: ${xlsxPath}`)
    }

    // Read the XLSX file
    console.log('Reading XLSX file...')
    const workbook = XLSX.readFile(xlsxPath)
    const sheetName = workbook.SheetNames[0] // Use first sheet
    const worksheet = workbook.Sheets[sheetName]
    
    // Convert to JSON
    const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
    
    if (jsonData.length === 0) {
      throw new Error('No data found in XLSX file')
    }

    console.log(`Found ${jsonData.length} rows in XLSX file`)

    // Get headers from first row
    const headers = jsonData[0]
    const dataRows = jsonData.slice(1)

    console.log('Headers:', headers)
    console.log(`Data rows: ${dataRows.length}`)

    // Create table with dynamic columns based on headers
    const rawSanitizedHeaders = headers.map((header, index) => {
      // Handle undefined, null, empty string, or non-string values
      if (header === undefined || header === null || header === '' || typeof header !== 'string') {
        return `column_${index + 1}`
      }

      const sanitized = header
        .toString()
        .toLowerCase()
        .replace(/[^a-z0-9]/g, '_')
        .replace(/^_+|_+$/g, '')
        .replace(/_+/g, '_')

      return sanitized || `column_${index + 1}`
    })

    // Handle duplicates by adding suffix and filter out any remaining empty values
    const sanitizedHeaders = rawSanitizedHeaders
      .filter(col => col && col.trim() !== '') // Remove any empty columns
      .map((col, index, filteredArray) => {
        const count = filteredArray.slice(0, index).filter(c => c === col).length
        return count === 0 ? col : `${col}_${count + 1}`
      })

    console.log('Sanitized headers:', sanitizedHeaders)
    console.log('Number of headers:', sanitizedHeaders.length)

    // Remove existing SQLite file if it exists
    if (fs.existsSync(sqlitePath)) {
      fs.unlinkSync(sqlitePath)
      console.log('Removed existing SQLite file')
    }

    // Create SQLite database
    console.log('Creating SQLite database...')
    const db = new sqlite3.Database(sqlitePath)

    const createTableSQL = `
      CREATE TABLE information_data (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        ${sanitizedHeaders.map(col => `${col} TEXT`).join(', ')},
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )
    `

    console.log('Creating table with SQL:', createTableSQL)

    await new Promise((resolve, reject) => {
      db.run(createTableSQL, (err) => {
        if (err) reject(err)
        else resolve()
      })
    })

    // Insert data
    console.log('Inserting data...')
    const placeholders = sanitizedHeaders.map(() => '?').join(', ')
    const insertSQL = `INSERT INTO information_data (${sanitizedHeaders.join(', ')}) VALUES (${placeholders})`

    const stmt = db.prepare(insertSQL)

    let insertedCount = 0
    for (const row of dataRows) {
      // Ensure row has same length as headers, fill missing values with empty string
      const normalizedRow = sanitizedHeaders.map((_, index) => {
        const value = row[index]
        return value !== undefined && value !== null ? value.toString() : ''
      })

      try {
        await new Promise((resolve, reject) => {
          stmt.run(normalizedRow, (err) => {
            if (err) reject(err)
            else resolve()
          })
        })
        insertedCount++
      } catch (err) {
        console.warn(`Failed to insert row ${insertedCount + 1}:`, err.message)
      }
    }

    stmt.finalize()

    // Create indexes for better search performance
    console.log('Creating indexes...')
    const indexPromises = sanitizedHeaders.slice(0, 5).map((col, index) => {
      return new Promise((resolve, reject) => {
        db.run(`CREATE INDEX IF NOT EXISTS idx_${col} ON information_data(${col})`, (err) => {
          if (err) reject(err)
          else resolve()
        })
      })
    })

    await Promise.all(indexPromises)

    // Close database
    await new Promise((resolve, reject) => {
      db.close((err) => {
        if (err) reject(err)
        else resolve()
      })
    })

    console.log(`✅ Conversion completed successfully!`)
    console.log(`📊 Inserted ${insertedCount} records`)
    console.log(`📁 SQLite database created at: ${sqlitePath}`)
    console.log(`🗂️  Table: information_data`)
    console.log(`📋 Columns: ${sanitizedHeaders.join(', ')}`)

    return {
      success: true,
      recordsInserted: insertedCount,
      databasePath: sqlitePath,
      tableName: 'information_data',
      columns: sanitizedHeaders
    }

  } catch (error) {
    console.error('❌ Conversion failed:', error.message)
    throw error
  }
}

// Run conversion if this script is executed directly
if (require.main === module) {
  convertXlsxToSqlite()
    .then((result) => {
      console.log('Conversion result:', result)
      process.exit(0)
    })
    .catch((error) => {
      console.error('Conversion error:', error)
      process.exit(1)
    })
}

module.exports = { convertXlsxToSqlite }

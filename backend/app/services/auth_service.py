"""
Authentication service with enterprise-level security
Following 2025 security best practices
"""
import logging
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
from fastapi import HTT<PERSON>Exception, status
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.security import (
    get_password_hash,
    verify_password,
    create_tokens
)
from app.core.config import settings
from app.models.user import UserInDB, UserCreate, UserLogin
from app.services.email_service import generate_otp, get_otp_expiry, send_password_reset_email, send_verification_email

logger = logging.getLogger(__name__)


def make_aware_utc(dt):
    if isinstance(dt, datetime):
        if dt.tzinfo is None:
            return dt.replace(tzinfo=timezone.utc)
        return dt.astimezone(timezone.utc)
    return dt


class AuthService:
    """Authentication service with comprehensive security features"""
    
    def __init__(self, db: AsyncIOMotorDatabase):
        self.db = db
        self.users_collection = db.users
    
    async def create_user(self, user_data: UserCreate, send_email: bool = True, device_fingerprint: Optional[str] = None) -> Dict[str, Any]:
        """
        Create a new user with secure password hashing or handle existing user cases
        """
        existing_user = await self.users_collection.find_one({"email": user_data.email})
        if existing_user:
            if not existing_user.get("email_verified", False):
                # Resend OTP if not verified
                if send_email:
                    try:
                        safe_name = user_data.name if user_data.name is not None else ""
                        await self._send_verification_email(user_data.email, safe_name)
                    except Exception as e:
                        logger.error(f"Failed to resend verification email to {user_data.email}: {str(e)}")
                return {
                    "existing": True,
                    "email_verified": False,
                    "email": user_data.email
                }
            else:
                return {
                    "existing": True,
                    "email_verified": True,
                    "email": user_data.email
                }
        
        # Hash password using Argon2
        hashed_password = get_password_hash(user_data.password)
        
        # Create user document
        now = datetime.now(timezone.utc)
        user_doc = {
            "email": user_data.email,
            "hashed_password": hashed_password,
            "initial": user_data.initial,
            "name": user_data.name,
            "designation": user_data.designation,
            "police_station": user_data.police_station,
            "district": user_data.district,
            "state": user_data.state,
            "is_active": True,
            "email_verified": False,
            "roles": ["user"],
            "created_at": now,
            "updated_at": now,
            "failed_login_attempts": 0,
            "account_locked_until": None,
            # "session_id": None,  # REMOVED - No session management
            "last_login": None,
            "paid": True,  # Start with paid=True for trial period
            "subscription_start_date": now,
            "subscription_expires": now + timedelta(days=3),  # 3-day trial period
            # Device management fields
            "device_fingerprint": device_fingerprint or user_data.device_fingerprint,
            "device_registered_at": now if (device_fingerprint or user_data.device_fingerprint) else None,
            "device_last_access": None,
            "device_info": "Registered during signup"
        }
        
        # Insert user
        result = await self.users_collection.insert_one(user_doc)
        user_doc["_id"] = result.inserted_id

        # Send verification email if requested
        if send_email:
            try:
                safe_name = user_data.name if user_data.name is not None else ""
                await self._send_verification_email(user_data.email, safe_name)
            except Exception as e:
                logger.error(f"Failed to send verification email to {user_data.email}: {str(e)}")
                # Don't fail user creation if email fails

        logger.info(f"Created new user: {user_data.email}")
        return {"existing": False, "user": UserInDB(**user_doc)}
    
    async def authenticate_user(
        self,
        login_data: UserLogin,
        client_ip: Optional[str] = None,
        device_fingerprint: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Simplified user authentication
        """
        user = await self.users_collection.find_one(
            {"email": login_data.email}
        )

        if not user:
            # Prevent user enumeration by using same timing
            get_password_hash("dummy_password")
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )

        # Simplified account lockout check
        failed_attempts = user.get("failed_login_attempts", 0)
        if failed_attempts >= settings.MAX_FAILED_LOGIN_ATTEMPTS:
            last_failed = user.get("last_failed_login")
            if last_failed:
                lockout_until = make_aware_utc(last_failed) + timedelta(minutes=settings.ACCOUNT_LOCKOUT_MINUTES)
                now_aware = make_aware_utc(datetime.now(timezone.utc))
                if now_aware < lockout_until:
                    raise HTTPException(
                        status_code=status.HTTP_423_LOCKED,
                        detail=f"Account locked. Try again in {settings.ACCOUNT_LOCKOUT_MINUTES} minutes."
                    )
        
        # Verify password
        if not verify_password(login_data.password, user["hashed_password"]):
            await self._handle_failed_login(user["_id"])
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid email or password"
            )
        
        # Check if user is active
        if not user.get("is_active", True):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Account is deactivated"
            )

        # Check license and subscription status
        await self._verify_license_status(user)

        # Handle device fingerprinting for license enforcement
        if device_fingerprint:
            await self._handle_device_fingerprinting(user, device_fingerprint, client_ip)

        # Reset failed login attempts on successful login
        await self._reset_failed_login_attempts(user["_id"])

        # Generate tokens (no session management)
        access_token, refresh_token = create_tokens(
            user_email=user["email"],
            roles=user.get("roles", ["user"])
        )

        # Update user login info (no session_id)
        await self.users_collection.update_one(
            {"_id": user["_id"]},
            {
                "$set": {
                    "last_login": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )
        
        logger.info(f"User authenticated successfully: {user['email']}")
        
        return {
            "access_token": access_token,
            "refresh_token": refresh_token,
            "token_type": "bearer",
            "expires_in": settings.ACCESS_TOKEN_EXPIRE_MINUTES * 60,
            "user": {
                "id": str(user["_id"]),
                "email": user["email"],
                "initial": user.get("initial"),
                "name": user.get("name"),
                "designation": user.get("designation"),
                "organization": user.get("organization"),
                "roles": user.get("roles", ["user"]),
                "email_verified": user.get("email_verified", False)
            }
        }
    
    async def get_user_by_email(self, email: str) -> Optional[UserInDB]:
        """Get user by email"""
        user_doc = await self.users_collection.find_one({"email": email})
        if user_doc:
            return UserInDB(**user_doc)
        return None
    
    async def _is_account_locked(self, user: Dict[str, Any]) -> bool:
        """Check if account is locked due to failed login attempts"""
        if not user.get("account_locked_until"):
            return False
        lockout_time = user["account_locked_until"]
        if isinstance(lockout_time, datetime):
            now_aware = make_aware_utc(datetime.now(timezone.utc))
            lockout_time_aware = make_aware_utc(lockout_time)
            return now_aware < lockout_time_aware
        return False
    
    async def _handle_failed_login(self, user_id: str):
        """Handle failed login attempt with progressive lockout"""
        user = await self.users_collection.find_one({"_id": user_id})
        if not user:
            return
        
        failed_attempts = user.get("failed_login_attempts", 0) + 1
        
        update_data = {
            "failed_login_attempts": failed_attempts,
            "updated_at": datetime.now(timezone.utc)
        }
        
        # Lock account if too many failed attempts
        if failed_attempts >= settings.MAX_FAILED_LOGIN_ATTEMPTS:
            lockout_until = datetime.now(timezone.utc) + timedelta(
                minutes=settings.ACCOUNT_LOCKOUT_MINUTES
            )
            update_data["account_locked_until"] = lockout_until
            
            logger.warning(
                f"Account locked due to {failed_attempts} failed attempts: "
                f"user_id={user_id}"
            )
        
        await self.users_collection.update_one(
            {"_id": user_id},
            {"$set": update_data}
        )
    
    async def _reset_failed_login_attempts(self, user_id: str):
        """Reset failed login attempts after successful login"""
        await self.users_collection.update_one(
            {"_id": user_id},
            {
                "$set": {
                    "failed_login_attempts": 0,
                    "account_locked_until": None,
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

    async def initiate_password_reset(self, email: str) -> bool:
        """
        Initiate password reset process by sending OTP email

        Args:
            email: User's email address

        Returns:
            bool: True if reset initiated successfully, False otherwise
        """
        # Check if user exists
        user = await self.users_collection.find_one({"email": email})
        if not user:
            # Don't reveal if user exists or not for security
            logger.info(f"Password reset requested for non-existent email: {email}")
            return True  # Return True to prevent user enumeration

        # Check if user is active
        if not user.get("is_active", True):
            logger.warning(f"Password reset requested for inactive user: {email}")
            return True  # Return True to prevent user enumeration

        # Generate OTP and expiry
        reset_otp = generate_otp()
        otp_expiry = get_otp_expiry(minutes=15)  # 15 minutes expiry

        # Update user with reset OTP info
        await self.users_collection.update_one(
            {"_id": user["_id"]},
            {
                "$set": {
                    "password_reset_otp": reset_otp,
                    "password_reset_otp_expires": otp_expiry,
                    "password_reset_otp_used": False,
                    "last_reset_request": datetime.now(timezone.utc),
                    "reset_otp_failed_attempts": 0,
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        # Send password reset email asynchronously
        try:
            await send_password_reset_email(
                email=email,
                otp=reset_otp,
                name=user.get("name", "")
            )
            logger.info(f"Password reset email sent successfully to: {email}")
            return True
        except Exception as e:
            logger.error(f"Failed to send password reset email to {email}: {str(e)}")
            return False

    async def verify_reset_otp(self, email: str, otp: str) -> bool:
        """
        Verify password reset OTP

        Args:
            email: User's email address
            otp: OTP to verify

        Returns:
            bool: True if OTP is valid, False otherwise
        """
        user = await self.users_collection.find_one({"email": email})
        if not user:
            return False

        # Check if OTP exists and hasn't been used
        stored_otp = user.get("password_reset_otp")
        otp_expiry = user.get("password_reset_otp_expires")
        otp_used = user.get("password_reset_otp_used", False)

        if not stored_otp or not otp_expiry or otp_used:
            return False

        # Ensure both are offset-aware
        otp_expiry_aware = make_aware_utc(otp_expiry)
        now_aware = make_aware_utc(datetime.now(timezone.utc))
        if now_aware > otp_expiry_aware:
            logger.warning(f"Expired password reset OTP used for: {email}")
            return False

        # Verify OTP
        if stored_otp != otp:
            # Increment failed attempts
            failed_attempts = user.get("reset_otp_failed_attempts", 0) + 1
            await self.users_collection.update_one(
                {"_id": user["_id"]},
                {"$set": {"reset_otp_failed_attempts": failed_attempts}}
            )

            # Lock reset attempts after 5 failed tries
            if failed_attempts >= 5:
                await self.users_collection.update_one(
                    {"_id": user["_id"]},
                    {
                        "$set": {
                            "password_reset_otp_used": True,
                            "updated_at": datetime.now(timezone.utc)
                        }
                    }
                )
                logger.warning(f"Password reset OTP locked due to failed attempts: {email}")

            return False

        return True

    async def reset_password(self, email: str, otp: str, new_password: str) -> bool:
        """
        Reset user password with OTP verification

        Args:
            email: User's email address
            otp: OTP for verification
            new_password: New password to set

        Returns:
            bool: True if password reset successfully, False otherwise
        """
        # Verify OTP first
        if not await self.verify_reset_otp(email, otp):
            return False

        user = await self.users_collection.find_one({"email": email})
        if not user:
            return False

        # Hash new password
        hashed_password = get_password_hash(new_password)

        # Update user with new password and mark OTP as used
        result = await self.users_collection.update_one(
            {"_id": user["_id"]},
            {
                "$set": {
                    "hashed_password": hashed_password,
                    "password_reset_otp_used": True,
                    "reset_otp_failed_attempts": 0,
                    "password_last_changed": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        if result.modified_count == 0:
            logger.error(f"Failed to update password for user: {email}")
            return False

        logger.info(f"Password successfully reset for user: {email}")
        return True

    async def _verify_license_status(self, user: Dict[str, Any]):
        """
        Verify user's license and subscription status

        Args:
            user: User document from database

        Raises:
            HTTPException: If user doesn't have valid license
        """
        # Check if user has paid subscription
        is_paid = user.get("paid", False)
        subscription_expires = user.get("subscription_expires")

        if not is_paid:
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail="Valid paid subscription required to access desktop application"
            )

        # Check subscription expiry
        if subscription_expires:
            if isinstance(subscription_expires, datetime):
                subscription_expires_aware = make_aware_utc(subscription_expires)
                now_aware = make_aware_utc(datetime.now(timezone.utc))
                if now_aware > subscription_expires_aware:
                    raise HTTPException(
                        status_code=status.HTTP_402_PAYMENT_REQUIRED,
                        detail="Subscription has expired. Please renew to continue using the application"
                    )

        logger.info(f"License verification passed for user: {user['email']}")

    async def _handle_device_fingerprinting(
        self,
        user: Dict[str, Any],
        device_fingerprint: str,
        client_ip: Optional[str] = None
    ):
        """
        Handle device fingerprinting for license enforcement

        Args:
            user: User document from database
            device_fingerprint: Unique device identifier
            client_ip: Client IP address for logging

        Raises:
            HTTPException: If device limit exceeded or unauthorized device
        """
        stored_fingerprint = user.get("device_fingerprint")

        # If no device registered, register this device
        if not stored_fingerprint:
            await self.users_collection.update_one(
                {"_id": user["_id"]},
                {
                    "$set": {
                        "device_fingerprint": device_fingerprint,
                        "device_registered_at": datetime.now(timezone.utc),
                        "device_last_access": datetime.now(timezone.utc),
                        # Store hardware-level identifiers instead of IP
                        "device_hardware_specs": None,  # Will be updated with actual hardware data
                        "device_machine_version": None,
                        "device_build_config": None,
                        "device_platform_info": None,
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )
            logger.info(f"Device registered for user: {user['email']}")
            return

        # If device fingerprint doesn't match, deny access
        if stored_fingerprint != device_fingerprint:
            logger.warning(
                f"Unauthorized device access attempt for user {user['email']} "
                f"from IP {client_ip}. Expected: {stored_fingerprint[:8]}..., "
                f"Got: {device_fingerprint[:8]}..."
            )
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="This device is not authorized. Only one device per license is allowed. "
                       "Contact support to reset your device registration."
            )

        # Update last access time for authorized device
        await self.users_collection.update_one(
            {"_id": user["_id"]},
            {
                "$set": {
                    "device_last_access": datetime.now(timezone.utc),
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        logger.info(f"Device access authorized for user: {user['email']}")

    async def reset_device_fingerprint(self, user_email: str) -> bool:
        """
        Reset device fingerprint for a user (admin/support function)

        Args:
            user_email: Email of the user

        Returns:
            bool: True if reset successful, False otherwise
        """
        try:
            result = await self.users_collection.update_one(
                {"email": user_email},
                {
                    "$unset": {
                        "device_fingerprint": "",
                        "device_registered_at": "",
                        "device_last_access": "",
                        "device_info": ""
                    },
                    "$set": {
                        "updated_at": datetime.now(timezone.utc)
                    }
                }
            )

            if result.modified_count > 0:
                logger.info(f"Device fingerprint reset for user: {user_email}")
                return True
            else:
                logger.warning(f"No user found or no changes made for: {user_email}")
                return False

        except Exception as e:
            logger.error(f"Failed to reset device fingerprint for {user_email}: {e}")
            return False

    async def _send_verification_email(self, email: str, name: str = ""):
        """
        Send email verification OTP to user

        Args:
            email: User's email address
            name: User's name for personalization
        """
        # Generate OTP and expiry
        verification_otp = generate_otp()
        otp_expiry = get_otp_expiry(minutes=30)  # 30 minutes expiry for verification

        # Update user with verification OTP info
        await self.users_collection.update_one(
            {"email": email},
            {
                "$set": {
                    "verification_otp": verification_otp,
                    "verification_otp_expires": otp_expiry,
                    "verification_otp_used": False,
                    "verification_otp_failed_attempts": 0,
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        # Send verification email
        await send_verification_email(
            email=email,
            otp=verification_otp,
            name=name
        )

        logger.info(f"Verification email sent to: {email}")

    async def verify_email(self, email: str, otp: str) -> bool:
        """
        Verify email address using OTP

        Args:
            email: User's email address
            otp: OTP to verify

        Returns:
            bool: True if email verified successfully, False otherwise
        """
        user = await self.users_collection.find_one({"email": email})
        if not user:
            logger.error(f"[OTP VERIFY] User not found for email: {email}")
            return False

        # Check if already verified
        if user.get("email_verified", False):
            logger.warning(f"[OTP VERIFY] Email already verified for: {email}")
            return True

        # Check if OTP exists and hasn't been used
        stored_otp = user.get("verification_otp")
        otp_expires = user.get("verification_otp_expires")
        otp_used = user.get("verification_otp_used", False)

        if not stored_otp:
            logger.error(f"[OTP VERIFY] No OTP stored for user: {email}")
            return False
        if not otp_expires:
            logger.error(f"[OTP VERIFY] No OTP expiry for user: {email}")
            return False
        if otp_used:
            logger.warning(f"[OTP VERIFY] OTP already used for user: {email}")
            return False

        # Ensure both are offset-aware
        otp_expires_aware = make_aware_utc(otp_expires)
        now_aware = make_aware_utc(datetime.now(timezone.utc))
        if now_aware > otp_expires_aware:
            logger.warning(f"[OTP VERIFY] Expired verification OTP used for: {email}")
            return False

        # Verify OTP
        if stored_otp != otp:
            logger.error(f"[OTP VERIFY] OTP mismatch for user: {email}. Stored: {stored_otp}, Provided: {otp}")
            # Increment failed attempts
            failed_attempts = user.get("verification_otp_failed_attempts", 0) + 1
            await self.users_collection.update_one(
                {"_id": user["_id"]},
                {"$set": {"verification_otp_failed_attempts": failed_attempts}}
            )

            # Lock verification attempts after 5 failed tries
            if failed_attempts >= 5:
                await self.users_collection.update_one(
                    {"_id": user["_id"]},
                    {
                        "$set": {
                            "verification_otp_used": True,
                            "updated_at": datetime.now(timezone.utc)
                        }
                    }
                )
                logger.warning(f"[OTP VERIFY] Email verification OTP locked due to failed attempts: {email}")

            return False

        # Mark email as verified and OTP as used
        await self.users_collection.update_one(
            {"_id": user["_id"]},
            {
                "$set": {
                    "email_verified": True,
                    "verification_otp_used": True,
                    "email_verified_at": datetime.now(timezone.utc),
                    "verification_otp_failed_attempts": 0,
                    "updated_at": datetime.now(timezone.utc)
                }
            }
        )

        logger.info(f"Email verified successfully for user: {email}")
        return True

    async def resend_verification_otp(self, email: str) -> bool:
        """
        Resend email verification OTP

        Args:
            email: User's email address

        Returns:
            bool: True if OTP resent successfully, False otherwise
        """
        user = await self.users_collection.find_one({"email": email})
        if not user:
            # Don't reveal if user exists or not for security
            logger.info(f"Verification OTP resend requested for non-existent email: {email}")
            return True  # Return True to prevent user enumeration

        # Check if already verified
        if user.get("email_verified", False):
            logger.info(f"Verification OTP resend requested for already verified email: {email}")
            return True

        # Check if user is active
        if not user.get("is_active", True):
            logger.warning(f"Verification OTP resend requested for inactive user: {email}")
            return True  # Return True to prevent user enumeration

        # Send verification email
        try:
            await self._send_verification_email(email, user.get("name", ""))
            return True
        except Exception as e:
            logger.error(f"Failed to resend verification email to {email}: {str(e)}")
            return False

"""
User models for Desktop NCRP Matrix Backend
Following 2025 Pydantic v2 best practices
"""
from datetime import datetime, timezone
from typing import Optional, List
from pydantic import BaseModel, EmailStr, Field, field_validator
from bson import ObjectId


class PyObjectId(ObjectId):
    """Custom ObjectId type for Pydantic"""
    @classmethod
    def __get_pydantic_core_schema__(cls, source_type, handler):
        from pydantic_core import core_schema
        return core_schema.no_info_plain_validator_function(cls.validate)

    @classmethod
    def validate(cls, v):
        if not ObjectId.is_valid(v):
            raise ValueError("Invalid ObjectId")
        return ObjectId(v)

    @classmethod
    def __get_pydantic_json_schema__(cls, field_schema, handler):
        field_schema.update(type="string")
        return field_schema


class UserBase(BaseModel):
    """Base user model with common fields"""
    email: EmailStr
    initial: Optional[str] = None
    name: Optional[str] = None
    designation: Optional[str] = None
    police_station: Optional[str] = None
    district: Optional[str] = None
    state: Optional[str] = None
    is_active: bool = True
    email_verified: bool = False
    roles: List[str] = Field(default_factory=lambda: ["user"])


class UserCreate(BaseModel):
    """User creation model"""
    email: EmailStr
    password: str = Field(..., min_length=8, max_length=128)
    initial: Optional[str] = Field(None, max_length=10)
    name: Optional[str] = Field(None, max_length=100)
    designation: Optional[str] = Field(None, max_length=100)
    police_station: Optional[str] = Field(None, max_length=100)
    district: Optional[str] = Field(None, max_length=100)
    state: Optional[str] = Field(None, max_length=100)
    device_fingerprint: Optional[str] = Field(None, max_length=64)  # SHA-256 hash
    
    @field_validator("password")
    @classmethod
    def validate_password(cls, v):
        """Validate password strength"""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        if not any(c.isupper() for c in v):
            raise ValueError("Password must contain at least one uppercase letter")
        if not any(c.islower() for c in v):
            raise ValueError("Password must contain at least one lowercase letter")
        if not any(c.isdigit() for c in v):
            raise ValueError("Password must contain at least one digit")
        return v


class UserLogin(BaseModel):
    """User login model"""
    email: EmailStr
    password: str


class UserInDB(UserBase):
    """User model as stored in database"""
    id: PyObjectId = Field(default_factory=PyObjectId, alias="_id")
    hashed_password: str
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    last_login: Optional[datetime] = None
    failed_login_attempts: int = 0
    account_locked_until: Optional[datetime] = None
    # session_id: Optional[str] = None  # REMOVED - No session management

    # License & Subscription Management
    paid: bool = False
    subscription_start_date: Optional[datetime] = None
    subscription_expires: Optional[datetime] = None

    # Device Management for License Enforcement - Hardware-level identifiers only
    device_fingerprint: Optional[str] = None  # SHA-256 hash of hardware specs
    device_registered_at: Optional[datetime] = None
    device_last_access: Optional[datetime] = None
    device_hardware_specs: Optional[str] = None  # JSON string of hardware specifications
    device_machine_version: Optional[str] = None  # Machine version information
    device_build_config: Optional[str] = None  # Build configuration details
    device_platform_info: Optional[str] = None  # Platform and architecture info

    # Remove template-related fields (handled locally in desktop app)
    # has_template: bool = False  # REMOVED - handled locally
    # template_filename: Optional[str] = None  # REMOVED - handled locally
    
    model_config = {
        "populate_by_name": True,
        "arbitrary_types_allowed": True,
        "json_encoders": {ObjectId: str}
    }


class User(UserBase):
    """User model for API responses (excludes sensitive data)"""
    id: str
    created_at: datetime
    last_login: Optional[datetime] = None
    
    class Config:
        from_attributes = True


class UserUpdate(BaseModel):
    """User update model"""
    initial: Optional[str] = Field(None, max_length=10)
    name: Optional[str] = Field(None, max_length=100)
    designation: Optional[str] = Field(None, max_length=100)
    organization: Optional[str] = Field(None, max_length=100)
    
    class Config:
        extra = "forbid"  # Prevent additional fields


class PasswordChange(BaseModel):
    """Password change model"""
    current_password: str
    new_password: str = Field(..., min_length=8, max_length=128)
    
    @field_validator("new_password")
    @classmethod
    def validate_new_password(cls, v):
        """Validate new password strength"""
        if len(v) < 8:
            raise ValueError("Password must be at least 8 characters long")
        if not any(c.isupper() for c in v):
            raise ValueError("Password must contain at least one uppercase letter")
        if not any(c.islower() for c in v):
            raise ValueError("Password must contain at least one lowercase letter")
        if not any(c.isdigit() for c in v):
            raise ValueError("Password must contain at least one digit")
        return v

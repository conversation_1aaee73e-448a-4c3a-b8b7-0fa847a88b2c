"""
Device Management Routes for License Enforcement
"""
import logging
from typing import Any, List
from fastapi import APIRouter, Depends, HTTPException, status
from motor.motor_asyncio import AsyncIOMotorDatabase

from app.core.database import get_database
from app.core.security import get_current_user
from app.models.user import UserInDB
from app.services.auth_service import AuthService

logger = logging.getLogger(__name__)

router = APIRouter()


@router.get("/info")
async def get_device_info(
    current_user: UserInDB = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database)
) -> Any:
    """
    Get current user's device information
    """
    try:
        auth_service = AuthService(db)
        user = await auth_service.get_user_by_email(current_user.email)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        device_info = {
            "device_registered": bool(user.get("device_fingerprint")),
            "device_registered_at": user.get("device_registered_at"),
            "device_last_access": user.get("device_last_access"),
            "device_info": user.get("device_info", "No device information available")
        }
        
        return {
            "success": True,
            "device_info": device_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get device info for user {current_user.email}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve device information"
        )


@router.post("/reset")
async def reset_device_registration(
    current_user: UserInDB = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database)
) -> Any:
    """
    Reset device registration for current user (admin/support function)
    This allows the user to register a new device
    """
    try:
        auth_service = AuthService(db)
        success = await auth_service.reset_device_fingerprint(current_user.email)
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found or no device to reset"
            )
        
        logger.info(f"Device registration reset for user: {current_user.email}")
        
        return {
            "success": True,
            "message": "Device registration has been reset. You can now register a new device on next login."
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to reset device for user {current_user.email}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reset device registration"
        )


@router.get("/status")
async def get_device_status(
    current_user: UserInDB = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database)
) -> Any:
    """
    Get device and subscription status for current user
    """
    try:
        auth_service = AuthService(db)
        user = await auth_service.get_user_by_email(current_user.email)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Check subscription status
        from datetime import datetime, timezone
        now = datetime.now(timezone.utc)
        subscription_active = user.get("paid", False)
        subscription_expires = user.get("subscription_expires")
        
        if subscription_expires and now > subscription_expires:
            subscription_active = False
        
        device_status = {
            "device_registered": bool(user.get("device_fingerprint")),
            "device_last_access": user.get("device_last_access"),
            "subscription_active": subscription_active,
            "subscription_expires": subscription_expires,
            "account_active": user.get("is_active", True),
            "email_verified": user.get("email_verified", False)
        }
        
        return {
            "success": True,
            "status": device_status
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get device status for user {current_user.email}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve device status"
        )


@router.get("/security-log")
async def get_security_log(
    current_user: UserInDB = Depends(get_current_user),
    db: AsyncIOMotorDatabase = Depends(get_database)
) -> Any:
    """
    Get security-related events for current user's device
    """
    try:
        auth_service = AuthService(db)
        user = await auth_service.get_user_by_email(current_user.email)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        
        # Basic security information (in a real system, you'd have a separate security log collection)
        security_info = {
            "last_login": user.get("last_login"),
            "failed_login_attempts": user.get("failed_login_attempts", 0),
            "account_locked_until": user.get("account_locked_until"),
            "device_registered_at": user.get("device_registered_at"),
            "device_last_access": user.get("device_last_access"),
            "created_at": user.get("created_at"),
            "updated_at": user.get("updated_at")
        }
        
        return {
            "success": True,
            "security_log": security_info
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get security log for user {current_user.email}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve security log"
        )

# Advanced Electron-Backend Communication Security Research
**Desktop NCRP Matrix Application**  
**Date:** 2025-01-07  
**Research Focus:** Secure communication between Electron app and Coolify VPS backend  

## Current State Analysis

### Current Communication Method
The application currently uses **standard HTTPS fetch requests** from Electron main process to FastAPI backend:

```typescript
// Current implementation in desktop-new-app/src/main/index.ts
const response = await fetch(`${BACKEND_BASE_URL}/auth/login`, {
  method: 'POST',
  headers: { 'Content-Type': 'application/x-www-form-urlencoded' },
  body: new URLSearchParams({
    username: credentials.email,
    password: credentials.password,
    device_fingerprint: credentials.device_fingerprint || ''
  }).toString()
})
```

### Current Security Features
✅ **Implemented:**
- HTTPS communication
- JWT token authentication
- Device fingerprinting
- Secure token storage using Electron's safeStorage
- Rate limiting on backend
- Input validation and sanitization

⚠️ **Security Weaknesses:**
- No certificate pinning
- Standard TLS without additional verification
- No request signing/verification
- No end-to-end encryption beyond TLS
- No mutual authentication
- Vulnerable to man-in-the-middle attacks if CA is compromised

---

## Advanced Security Methods Research

### 1. Certificate Pinning

**Description:** Validate that the server's certificate matches a known, trusted certificate or public key.

**Security Benefit:** Prevents MITM attacks even if a Certificate Authority is compromised.

**Implementation Complexity:** Medium

**Code Example:**
```typescript
import { app } from 'electron'
import * as crypto from 'crypto'

// Store expected certificate fingerprint
const EXPECTED_CERT_FINGERPRINT = 'sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA='

app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
  if (url.startsWith(BACKEND_BASE_URL)) {
    event.preventDefault()
    
    // Calculate certificate fingerprint
    const fingerprint = crypto
      .createHash('sha256')
      .update(certificate.data)
      .digest('base64')
    
    const certFingerprint = `sha256/${fingerprint}`
    
    // Verify against expected fingerprint
    callback(certFingerprint === EXPECTED_CERT_FINGERPRINT)
  } else {
    callback(false)
  }
})
```

**Coolify Deployment Considerations:**
- Need to extract and store certificate fingerprint during deployment
- Certificate rotation requires app updates
- Let's Encrypt certificates change every 90 days

### 2. Mutual TLS (mTLS) Authentication

**Description:** Both client and server authenticate each other using certificates.

**Security Benefit:** Strongest authentication method, prevents unauthorized clients.

**Implementation Complexity:** High

**Code Example:**
```typescript
import * as https from 'https'
import * as fs from 'fs'

// Load client certificate and key
const clientCert = fs.readFileSync('path/to/client-cert.pem')
const clientKey = fs.readFileSync('path/to/client-key.pem')
const caCert = fs.readFileSync('path/to/ca-cert.pem')

const httpsAgent = new https.Agent({
  cert: clientCert,
  key: clientKey,
  ca: caCert,
  rejectUnauthorized: true
})

// Use with fetch
const response = await fetch(url, {
  method: 'POST',
  agent: httpsAgent,
  headers,
  body
})
```

**Coolify Deployment Considerations:**
- Requires certificate management infrastructure
- Client certificates must be embedded in Electron app
- Complex certificate lifecycle management
- May require custom Nginx configuration

### 3. JWT Token Security Enhancements

**Description:** Enhanced JWT security with additional claims and validation.

**Security Benefit:** Stronger token validation and shorter attack windows.

**Implementation Complexity:** Low-Medium

**Enhanced JWT Implementation:**
```typescript
// Enhanced token creation (backend)
const createEnhancedAccessToken = (
  subject: string,
  deviceFingerprint: string,
  clientIP: string
) => {
  const payload = {
    sub: subject,
    exp: Math.floor(Date.now() / 1000) + (15 * 60), // 15 minutes
    iat: Math.floor(Date.now() / 1000),
    jti: crypto.randomUUID(),
    aud: 'desktop-app',
    iss: 'ncrp-matrix-backend',
    device: crypto.createHash('sha256').update(deviceFingerprint).digest('hex'),
    ip: crypto.createHash('sha256').update(clientIP).digest('hex'),
    scope: ['read', 'write']
  }
  
  return jwt.sign(payload, SECRET_KEY, { algorithm: 'HS256' })
}

// Enhanced token validation (backend)
const validateEnhancedToken = (token: string, deviceFingerprint: string, clientIP: string) => {
  const payload = jwt.verify(token, SECRET_KEY)
  
  // Validate device fingerprint
  const expectedDevice = crypto.createHash('sha256').update(deviceFingerprint).digest('hex')
  if (payload.device !== expectedDevice) {
    throw new Error('Device mismatch')
  }
  
  // Validate IP (optional - may cause issues with dynamic IPs)
  const expectedIP = crypto.createHash('sha256').update(clientIP).digest('hex')
  if (payload.ip !== expectedIP) {
    console.warn('IP address changed for user:', payload.sub)
  }
  
  return payload
}
```

### 4. API Request Signing and Verification

**Description:** Sign each API request with HMAC to ensure integrity and authenticity.

**Security Benefit:** Prevents request tampering and replay attacks.

**Implementation Complexity:** Medium

**Code Example:**
```typescript
// Client-side request signing
import * as crypto from 'crypto'

class SecureAPIClient {
  private apiKey: string
  private secretKey: string
  
  constructor(apiKey: string, secretKey: string) {
    this.apiKey = apiKey
    this.secretKey = secretKey
  }
  
  private signRequest(method: string, url: string, body: string, timestamp: number): string {
    const message = `${method}\n${url}\n${body}\n${timestamp}`
    return crypto
      .createHmac('sha256', this.secretKey)
      .update(message)
      .digest('hex')
  }
  
  async secureRequest(url: string, options: RequestInit): Promise<Response> {
    const timestamp = Math.floor(Date.now() / 1000)
    const body = options.body?.toString() || ''
    const method = options.method || 'GET'
    
    const signature = this.signRequest(method, url, body, timestamp)
    
    const headers = {
      ...options.headers,
      'X-API-Key': this.apiKey,
      'X-Timestamp': timestamp.toString(),
      'X-Signature': signature
    }
    
    return fetch(url, { ...options, headers })
  }
}

// Backend verification
const verifyRequestSignature = (req: Request): boolean => {
  const apiKey = req.headers['x-api-key']
  const timestamp = parseInt(req.headers['x-timestamp'])
  const signature = req.headers['x-signature']
  
  // Check timestamp (prevent replay attacks)
  const now = Math.floor(Date.now() / 1000)
  if (Math.abs(now - timestamp) > 300) { // 5 minutes tolerance
    return false
  }
  
  // Get secret key for API key
  const secretKey = getSecretKeyForAPIKey(apiKey)
  if (!secretKey) return false
  
  // Recreate signature
  const message = `${req.method}\n${req.url}\n${req.body}\n${timestamp}`
  const expectedSignature = crypto
    .createHmac('sha256', secretKey)
    .update(message)
    .digest('hex')
  
  return crypto.timingSafeEqual(
    Buffer.from(signature, 'hex'),
    Buffer.from(expectedSignature, 'hex')
  )
}
```

### 5. End-to-End Encryption for Sensitive Data

**Description:** Encrypt sensitive data before transmission, decrypt on the other end.

**Security Benefit:** Protects data even if TLS is compromised.

**Implementation Complexity:** High

**Code Example:**
```typescript
import * as crypto from 'crypto'

class E2EEncryption {
  private publicKey: string
  private privateKey: string
  
  constructor(publicKey: string, privateKey: string) {
    this.publicKey = publicKey
    this.privateKey = privateKey
  }
  
  encrypt(data: string): string {
    // Generate random AES key
    const aesKey = crypto.randomBytes(32)
    const iv = crypto.randomBytes(16)
    
    // Encrypt data with AES
    const cipher = crypto.createCipher('aes-256-gcm', aesKey, iv)
    let encrypted = cipher.update(data, 'utf8', 'hex')
    encrypted += cipher.final('hex')
    const authTag = cipher.getAuthTag()
    
    // Encrypt AES key with RSA public key
    const encryptedKey = crypto.publicEncrypt(this.publicKey, aesKey)
    
    return JSON.stringify({
      encryptedData: encrypted,
      encryptedKey: encryptedKey.toString('base64'),
      iv: iv.toString('base64'),
      authTag: authTag.toString('base64')
    })
  }
  
  decrypt(encryptedPayload: string): string {
    const payload = JSON.parse(encryptedPayload)
    
    // Decrypt AES key with RSA private key
    const aesKey = crypto.privateDecrypt(
      this.privateKey,
      Buffer.from(payload.encryptedKey, 'base64')
    )
    
    // Decrypt data with AES
    const decipher = crypto.createDecipher(
      'aes-256-gcm',
      aesKey,
      Buffer.from(payload.iv, 'base64')
    )
    decipher.setAuthTag(Buffer.from(payload.authTag, 'base64'))
    
    let decrypted = decipher.update(payload.encryptedData, 'hex', 'utf8')
    decrypted += decipher.final('utf8')
    
    return decrypted
  }
}
```

### 6. Secure Token Storage in Electron

**Description:** Enhanced secure storage for authentication tokens and keys.

**Security Benefit:** Protects stored credentials from local attacks.

**Implementation Complexity:** Low-Medium

**Enhanced Implementation:**
```typescript
import { safeStorage, app } from 'electron'
import * as crypto from 'crypto'
import * as keytar from 'keytar'

class SecureStorage {
  private serviceName = 'ncrp-matrix-desktop'
  
  async storeToken(userId: string, token: string): Promise<void> {
    try {
      // Use keytar for cross-platform secure storage
      await keytar.setPassword(this.serviceName, `token_${userId}`, token)
    } catch (error) {
      // Fallback to Electron's safeStorage
      if (safeStorage.isEncryptionAvailable()) {
        const encrypted = safeStorage.encryptString(token)
        // Store encrypted data to file with user-specific name
        const fs = require('fs')
        const path = require('path')
        const tokenPath = path.join(app.getPath('userData'), `token_${userId}.enc`)
        fs.writeFileSync(tokenPath, encrypted)
      } else {
        throw new Error('No secure storage available')
      }
    }
  }
  
  async getToken(userId: string): Promise<string | null> {
    try {
      // Try keytar first
      return await keytar.getPassword(this.serviceName, `token_${userId}`)
    } catch (error) {
      // Fallback to Electron's safeStorage
      try {
        const fs = require('fs')
        const path = require('path')
        const tokenPath = path.join(app.getPath('userData'), `token_${userId}.enc`)
        
        if (fs.existsSync(tokenPath)) {
          const encrypted = fs.readFileSync(tokenPath)
          return safeStorage.decryptString(encrypted)
        }
      } catch (fallbackError) {
        console.error('Failed to retrieve token:', fallbackError)
      }
      return null
    }
  }
  
  async deleteToken(userId: string): Promise<void> {
    try {
      await keytar.deletePassword(this.serviceName, `token_${userId}`)
    } catch (error) {
      // Fallback cleanup
      try {
        const fs = require('fs')
        const path = require('path')
        const tokenPath = path.join(app.getPath('userData'), `token_${userId}.enc`)
        if (fs.existsSync(tokenPath)) {
          fs.unlinkSync(tokenPath)
        }
      } catch (fallbackError) {
        console.error('Failed to delete token:', fallbackError)
      }
    }
  }
}
```

---

## Implementation Recommendations

### Priority 1: High Security, Low Complexity
1. **Enhanced JWT Security** - Immediate implementation
2. **Secure Token Storage Enhancement** - Quick wins
3. **Request Signing for Critical Operations** - Authentication and sensitive data

### Priority 2: Medium Security, Medium Complexity  
1. **Certificate Pinning** - For production deployment
2. **API Request Signing** - For all API calls
3. **Enhanced Error Handling** - Prevent information leakage

### Priority 3: High Security, High Complexity
1. **Mutual TLS (mTLS)** - For maximum security environments
2. **End-to-End Encryption** - For highly sensitive data
3. **Hardware Security Module Integration** - Enterprise deployments

### Coolify VPS Specific Considerations

**Advantages:**
- Full control over server configuration
- Can implement custom Nginx configurations
- Easy certificate management with Let's Encrypt
- Docker container isolation

**Challenges:**
- Certificate rotation with Let's Encrypt (90-day cycle)
- Limited hardware security module options
- Need for custom certificate management scripts
- Backup and disaster recovery for certificates

**Recommended Implementation for Coolify:**

1. **Start with Enhanced JWT + Request Signing**
   - Low complexity, high security benefit
   - No infrastructure changes required
   - Easy to implement and test

2. **Add Certificate Pinning**
   - Medium complexity, high security benefit
   - Requires certificate fingerprint management
   - Can be implemented with Let's Encrypt automation

3. **Consider mTLS for Future**
   - High complexity, maximum security
   - Requires significant infrastructure changes
   - Best for enterprise or high-security requirements

---

---

## Practical Implementation Guide

### Phase 1: Enhanced JWT Security (Immediate - 1 week)

**Backend Changes:**
```python
# backend/app/core/security.py - Enhanced JWT
def create_enhanced_access_token(
    subject: str,
    device_fingerprint: str,
    client_ip: str,
    roles: Optional[List[str]] = None,
    expires_delta: Optional[timedelta] = None
) -> str:
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=15)  # Shorter expiry

    to_encode = {
        "sub": str(subject),
        "exp": expire,
        "iat": datetime.now(timezone.utc),
        "jti": secrets.token_urlsafe(16),
        "type": "access",
        "roles": roles or ["user"],
        "iss": settings.PROJECT_NAME,
        "aud": "desktop-app",
        "device": hashlib.sha256(device_fingerprint.encode()).hexdigest()[:16],
        "client": hashlib.sha256(client_ip.encode()).hexdigest()[:16],
        "scope": ["read", "write"]
    }
    return jwt.encode(to_encode, settings.SECRET_KEY, algorithm=settings.ALGORITHM)
```

**Frontend Changes:**
```typescript
// desktop-new-app/src/renderer/src/services/secureApiService.ts
class SecureApiService {
  private static instance: SecureApiService

  public static getInstance(): SecureApiService {
    if (!SecureApiService.instance) {
      SecureApiService.instance = new SecureApiService()
    }
    return SecureApiService.instance
  }

  async makeSecureRequest(url: string, options: RequestInit): Promise<Response> {
    const token = await window.api.auth.getToken()
    const deviceInfo = await deviceService.generateDeviceFingerprint()

    const headers = {
      ...options.headers,
      'Authorization': `Bearer ${token?.token}`,
      'X-Device-Fingerprint': deviceInfo.fingerprint,
      'X-Client-Version': '1.0.0',
      'X-Request-ID': crypto.randomUUID()
    }

    return fetch(url, { ...options, headers })
  }
}
```

### Phase 2: Request Signing (Short-term - 2 weeks)

**Implementation:**
```typescript
// desktop-new-app/src/main/services/cryptoService.ts
import * as crypto from 'crypto'

export class CryptoService {
  private static readonly HMAC_SECRET = process.env.HMAC_SECRET || 'fallback-secret'

  static signRequest(method: string, url: string, body: string, timestamp: number): string {
    const message = `${method}\n${url}\n${body}\n${timestamp}`
    return crypto
      .createHmac('sha256', this.HMAC_SECRET)
      .update(message)
      .digest('hex')
  }

  static verifySignature(
    method: string,
    url: string,
    body: string,
    timestamp: number,
    signature: string
  ): boolean {
    const expectedSignature = this.signRequest(method, url, body, timestamp)
    return crypto.timingSafeEqual(
      Buffer.from(signature, 'hex'),
      Buffer.from(expectedSignature, 'hex')
    )
  }
}

// Update main process to sign requests
ipcMain.handle(AUTH_LOGIN, async (_, credentials) => {
  const timestamp = Math.floor(Date.now() / 1000)
  const body = new URLSearchParams({
    username: credentials.email,
    password: credentials.password,
    device_fingerprint: credentials.device_fingerprint || ''
  }).toString()

  const signature = CryptoService.signRequest('POST', '/api/v1/auth/login', body, timestamp)

  const headers = {
    'Content-Type': 'application/x-www-form-urlencoded',
    'X-Timestamp': timestamp.toString(),
    'X-Signature': signature,
    'X-Client-ID': 'desktop-app'
  }

  const response = await fetch(`${BACKEND_BASE_URL}/auth/login`, {
    method: 'POST',
    headers,
    body
  })
  // ... rest of implementation
})
```

### Phase 3: Certificate Pinning (Medium-term - 1 month)

**Implementation:**
```typescript
// desktop-new-app/src/main/security/certificatePinning.ts
import { app, session } from 'electron'
import * as crypto from 'crypto'

export class CertificatePinning {
  private static readonly PINNED_CERTIFICATES = [
    'sha256/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=', // Primary cert
    'sha256/BBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBBB='  // Backup cert
  ]

  static initialize(): void {
    app.on('certificate-error', (event, webContents, url, error, certificate, callback) => {
      if (url.startsWith(process.env.BACKEND_BASE_URL || 'https://your-backend.com')) {
        event.preventDefault()

        const fingerprint = this.getCertificateFingerprint(certificate)
        const isValid = this.PINNED_CERTIFICATES.includes(fingerprint)

        if (!isValid) {
          console.error('Certificate pinning failed for:', url)
          console.error('Expected one of:', this.PINNED_CERTIFICATES)
          console.error('Got:', fingerprint)
        }

        callback(isValid)
      } else {
        callback(false)
      }
    })

    // Also set up session certificate verification
    session.defaultSession.setCertificateVerifyProc((request, callback) => {
      if (request.hostname.includes('your-backend-domain.com')) {
        const fingerprint = this.getCertificateFingerprint(request.certificate)
        callback(this.PINNED_CERTIFICATES.includes(fingerprint) ? 0 : -2)
      } else {
        callback(-3) // Use default verification
      }
    })
  }

  private static getCertificateFingerprint(certificate: Electron.Certificate): string {
    const fingerprint = crypto
      .createHash('sha256')
      .update(Buffer.from(certificate.data))
      .digest('base64')
    return `sha256/${fingerprint}`
  }

  // Utility method to extract certificate fingerprint for configuration
  static async extractCertificateFingerprint(url: string): Promise<string> {
    return new Promise((resolve, reject) => {
      const https = require('https')
      const urlObj = new URL(url)

      const req = https.request({
        hostname: urlObj.hostname,
        port: urlObj.port || 443,
        method: 'GET',
        rejectUnauthorized: false
      }, (res) => {
        const cert = res.connection.getPeerCertificate()
        if (cert) {
          const fingerprint = crypto
            .createHash('sha256')
            .update(cert.raw)
            .digest('base64')
          resolve(`sha256/${fingerprint}`)
        } else {
          reject(new Error('No certificate found'))
        }
      })

      req.on('error', reject)
      req.end()
    })
  }
}

// Initialize in main.ts
CertificatePinning.initialize()
```

### Deployment Scripts for Coolify

**Certificate Management Script:**
```bash
#!/bin/bash
# scripts/update-certificate-pins.sh

BACKEND_URL="https://your-backend.coolify.domain.com"
CERT_FILE="/tmp/backend-cert.pem"

# Extract certificate
echo | openssl s_client -servername $(echo $BACKEND_URL | cut -d'/' -f3) \
  -connect $(echo $BACKEND_URL | cut -d'/' -f3):443 2>/dev/null \
  | openssl x509 -outform PEM > $CERT_FILE

# Calculate fingerprint
FINGERPRINT=$(openssl x509 -in $CERT_FILE -pubkey -noout | \
  openssl pkey -pubin -outform der | \
  openssl dgst -sha256 -binary | \
  base64)

echo "Certificate fingerprint: sha256/$FINGERPRINT"

# Update configuration file
sed -i "s/AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA=/$FINGERPRINT/g" \
  src/main/security/certificatePinning.ts

echo "Certificate pinning updated. Rebuild the application."
```

---

## Conclusion

The current implementation provides **adequate security** for most use cases. For enhanced security suitable for production deployment with 500 users, I recommend implementing:

1. **Enhanced JWT Security** (immediate)
2. **Request Signing for Authentication** (short-term)
3. **Certificate Pinning** (medium-term)

This approach provides **significant security improvements** while maintaining **reasonable implementation complexity** and **compatibility with Coolify deployment environment**.

**Security Improvement Summary:**
- **Current Security Level:** B+ (Good)
- **After Phase 1:** A- (Very Good)
- **After Phase 2:** A (Excellent)
- **After Phase 3:** A+ (Outstanding)

**Implementation Timeline:**
- **Phase 1:** 1 week (Enhanced JWT)
- **Phase 2:** 2 weeks (Request Signing)
- **Phase 3:** 1 month (Certificate Pinning)

**Total Implementation Time:** 6-8 weeks for complete advanced security implementation.
